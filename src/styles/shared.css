@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Headers */
  .section-header {
    @apply text-2xl font-bold text-red-600 mb-6 text-center;
  }

  /* Form Elements */
  .form-label {
    @apply block text-base font-medium text-gray-700 mb-1;
  }

  .form-input {
    @apply block w-full px-4 py-3 text-base border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200;
  }

  /* Buttons */
  .primary-button {
    @apply w-full flex justify-center items-center space-x-2 px-4 py-3 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 transition-colors duration-200;
  }

  .print-button {
    @apply flex items-center space-x-1 px-3 py-1 text-red-600 hover:text-white hover:bg-red-600 rounded-md border border-red-600 transition-colors duration-200;
  }

  /* List Items */
  .list-item {
    @apply flex items-center justify-between p-4 bg-white rounded-lg shadow-md border border-gray-100 hover:border-red-200 transition-all duration-200;
  }

  .list-item-text {
    @apply text-base text-gray-600;
  }

  .list-item-title {
    @apply text-lg font-medium text-gray-800 mb-1;
  }

  /* Empty State */
  .empty-state {
    @apply p-8 text-center bg-gray-50 rounded-lg border-2 border-gray-200;
  }

  .empty-state-title {
    @apply text-xl font-medium text-gray-600;
  }

  .empty-state-subtitle {
    @apply mt-2 text-gray-500;
  }
}
