import React, { useState, useEffect } from 'react';
import { Printer, ChevronDown, ChevronUp, Lock } from 'lucide-react';
import type { GiftCard } from '../types';
import logoImage from '../assets/logo.png';

interface GiftCardListProps {
  giftCards: GiftCard[];
  isAuthenticated: boolean;
}

interface BulkGroup {
  groupId: string;
  cards: GiftCard[];
  sampleCard: GiftCard;
  isExpanded: boolean;
}

export function GiftCardList({ giftCards, isAuthenticated }: GiftCardListProps) {
  const [currentPage, setCurrentPage] = useState(1);

  const [bulkGroups, setBulkGroups] = useState<BulkGroup[]>([]);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [individualCards, setIndividualCards] = useState<GiftCard[]>([]);
  const cardsPerPage = 10;

  useEffect(() => {
    const activeGiftCards = giftCards.filter(card => !card.used);

    const groups: Record<string, GiftCard[]> = {};
    activeGiftCards.forEach(card => {
      const firstThree = card.code.substring(0, 3);
      if (!groups[firstThree]) {
        groups[firstThree] = [];
      }
      groups[firstThree].push(card);
    });

    const individual: GiftCard[] = [];
    const bulkGroups: BulkGroup[] = [];

    Object.entries(groups).forEach(([groupId, cards]) => {
      if (cards.length === 1) {
        individual.push(cards[0]);
      } else {
        bulkGroups.push({
          groupId,
          cards,
          sampleCard: cards[0],
          isExpanded: expandedGroups.has(groupId)
        });
      }
    });

    setIndividualCards(individual);
    setBulkGroups(bulkGroups);
  }, [giftCards, expandedGroups]);

  const calculateDisplayItems = () => {
    const allItems = [
      ...individualCards.map(card => ({ type: 'individual' as const, card })),
      ...bulkGroups.flatMap(group => 
        group.isExpanded && isAuthenticated
          ? [{ type: 'group' as const, group }, ...group.cards.map(card => ({ type: 'card' as const, card }))]
          : [{ type: 'group' as const, group }]
      )
    ];
    
    const startIdx = (currentPage - 1) * cardsPerPage;
    const endIdx = startIdx + cardsPerPage;
    
    return {
      currentItems: allItems.slice(startIdx, endIdx),
      totalPages: Math.ceil(allItems.length / cardsPerPage)
    };
  };

  const displayItemsResult = calculateDisplayItems();
  const displayItems = displayItemsResult.currentItems;
  const totalPages = displayItemsResult.totalPages;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const getExpirationDate = () => {
    const today = new Date();
    let targetMonth = today.getMonth() + 2;
    let targetYear = today.getFullYear();
    
    if (targetMonth > 11) {
      targetMonth = targetMonth - 12;
      targetYear += 1;
    }
    
    const lastDay = new Date(targetYear, targetMonth + 1, 0).getDate();
    return `${lastDay.toString().padStart(2, '0')}/${(targetMonth + 1).toString().padStart(2, '0')}/${targetYear}`;
  };

  const generateCardHTML = (card: GiftCard, isBulk: boolean) => {
    const getBase64Image = (img) => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0);
      return canvas.toDataURL("image/png");
    };
    
    const img = new Image();
    img.src = logoImage;
    let logoSrc = logoImage;
    
    if (img.complete) {
      try {
        logoSrc = getBase64Image(img);
      } catch (e) {
        console.error("Error converting logo to data URL", e);
      }
    }

    return `
      <div style="
        width: ${isBulk ? '226px' : '302px'}; 
        padding: ${isBulk ? '0.75rem' : '1rem'}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${logoSrc}"
              alt="Logo" 
              style="height: ${isBulk ? '20px' : '28px'}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${isBulk ? '0.875rem' : '1.125rem'}; 
              margin: 0; 
              color: ${isBulk ? '#dc2626' : 'black'};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${card.recipientName ? `
            <div>
              <p style="font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0; color: ${isBulk ? '#4b5563' : 'black'};">Para:</p>
              <p style="font-weight: 500; font-size: ${isBulk ? '0.875rem' : '1rem'}; margin: 0; color: ${isBulk ? '#1f2937' : 'black'}">
                ${card.recipientName}
              </p>
            </div>
          ` : ''}
          <div style="text-align: ${card.recipientName ? 'right' : 'center'}; ${!card.recipientName ? 'width: 100%;' : ''}">
            <p style="font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0; color: ${isBulk ? '#4b5563' : 'black'};">Valor:</p>
            <p style="font-weight: 700; font-size: ${isBulk ? '1.25rem' : '1.5rem'}; margin: 0; color: ${isBulk ? '#dc2626' : 'black'}">
              R$ ${card.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${isBulk ? 100 : 130}&data=${encodeURIComponent(card.code)}" 
               style="width: ${isBulk ? '100px' : '130px'}; height: ${isBulk ? '100px' : '130px'};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${isBulk ? '1rem' : '1.125rem'}; 
            margin: 0; 
            color: ${isBulk ? '#1f2937' : 'black'};
          ">
            ${card.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${isBulk ? '#6b7280' : 'black'};">
          <p style="font-weight: 500; font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0;">Um presente de: ${card.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${isBulk ? '0.625rem' : '0.75rem'}; margin: 0;">
            Válido até ${getExpirationDate()}
          </p>
        </div>
      </div>
    `;
  };

  const handlePrint = (card: GiftCard | GiftCard[], isBulk: boolean = false) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = `
        <html>
          <head>
            <title>${isBulk ? 'Lote de Vale-Presentes' : 'Vale-Presente'}</title>
            <style>
              @media print {
                @page {
                  ${isBulk ? 'size: A4; margin: 10mm;' : 'size: 80mm auto; margin: 0;'}
                }
                body {
                  margin: 0;
                  padding: ${isBulk ? '10mm' : '4mm'};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
              
              /* Grid layout for bulk cards */
              .bulk-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
              }
              
              .card-container {
                margin-bottom: 16px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${Array.isArray(card) ? `
                <div class="bulk-grid">
                  ${card.map(c => `
                    <div class="card-container">
                      ${generateCardHTML(c, true)}
                    </div>
                  `).join('')}
                </div>
              ` : `
                <div style="display: flex; justify-content: center;">
                  ${generateCardHTML(card, false)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            </script>
          </body>
        </html>
      `;
      
      printWindow.document.write(printContent);
      printWindow.document.close();
    }
  };

  const toggleGroupExpansion = (groupId: string) => {
    if (!isAuthenticated) {
      return;
    }
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(groupId)) {
        newSet.delete(groupId);
      } else {
        newSet.add(groupId);
      }
      return newSet;
    });
  };

  const maskCode = (code: string) => {
    return code.substring(0, 3) + '•••••••••••' + code.substring(code.length - 2);
  };

  return (
    <div className="w-full mx-auto space-y-6 bg-white p-6 rounded-lg shadow-md">
      <h2 className="section-header">Vale-Presentes Ativos</h2>

      {/* List Section */}
      <div className="space-y-3">
        {displayItems.length === 0 && individualCards.length === 0 && bulkGroups.length === 0 ? (
          <div className="empty-state">
            <p className="empty-state-title">Nenhum Vale-Presente ativo</p>
            <p className="empty-state-subtitle">Gere novos vale-presentes na aba "Vender Vale-Presente" ou "Gerar em Lote"</p>
          </div>
        ) : (
          displayItems.map((item, index) => {
            if (item.type === 'individual') {
              return (
                <div key={item.card.id} className="list-item">
                  <div>
                    <p className="list-item-title">{item.card.recipientName || `Comprador: ${item.card.buyerName}`}</p>
                    <p className="list-item-text">Código do Vale-Presente: {isAuthenticated ? item.card.code : maskCode(item.card.code)}</p>
                    <p className="list-item-text">Valor: R$ {item.card.value.toFixed(2)}</p>
                  </div>
                  {isAuthenticated ? (
                    <button
                      onClick={() => handlePrint([item.card], false)}
                      className="print-button"
                    >
                      <Printer className="h-4 w-4" />
                      <span>Imprimir</span>
                    </button>
                  ) : (
                    <div className="text-gray-400 flex items-center">
                      <Lock className="h-4 w-4 mr-1" />
                      <span className="text-sm">Bloqueado</span>
                    </div>
                  )}
                </div>
              );
            } else if (item.type === 'group') {
              const group = item.group;
              return (
                <div key={group.groupId} className="list-item">
                  <div className="flex items-center justify-between w-full">
                    <div>
                      <p className="list-item-title">Grupo {group.groupId} - {group.sampleCard.buyerName}</p>
                      <p className="list-item-text">{group.cards.length} vale-presentes</p>
                      <p className="list-item-text">Valor: R$ {group.sampleCard.value.toFixed(2)}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {isAuthenticated ? (
                        <>
                          <button
                            onClick={() => handlePrint(group.cards, true)}
                            className="print-button"
                          >
                            <Printer className="h-4 w-4" />
                            <span>Imprimir Todos</span>
                          </button>
                          <button
                            onClick={() => toggleGroupExpansion(group.groupId)}
                            className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200"
                          >
                            {group.isExpanded ? (
                              <ChevronUp className="h-5 w-5" />
                            ) : (
                              <ChevronDown className="h-5 w-5" />
                            )}
                          </button>
                        </>
                      ) : (
                        <div className="text-gray-400 flex items-center">
                          <Lock className="h-4 w-4 mr-1" />
                          <span className="text-sm">Bloqueado</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            } else {
              return (
                <div key={item.card.id} className="ml-6 p-3 border-l-2 border-red-200 bg-white rounded-r-lg">
                  <div className="flex items-center justify-between">
                    <p className="list-item-text">Código do Vale-Presente: {isAuthenticated ? item.card.code : maskCode(item.card.code)}</p>
                    <button
                      onClick={() => handlePrint([item.card], false)}
                      className="text-sm text-red-600 hover:text-red-700 px-2 py-1 rounded-md hover:bg-red-50 transition-colors duration-200"
                    >
                      Imprimir Individual
                    </button>
                  </div>
                </div>
              );
            }
          })
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center space-x-2 mt-4">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`px-3 py-1 rounded ${
                currentPage === page
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {page}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
