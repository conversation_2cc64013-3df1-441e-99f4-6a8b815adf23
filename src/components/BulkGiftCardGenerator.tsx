import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Gift } from 'lucide-react';
import type { GiftCard } from '../types';
import { GiftCardPrint } from './GiftCardPrint';

interface BulkGiftCardGeneratorProps {
  onGenerate: (giftCards: GiftCard[]) => void;
}

export function BulkGiftCardGenerator({ onGenerate }: BulkGiftCardGeneratorProps) {
  const [formData, setFormData] = useState({
    quantity: '2',
    value: '',
    buyerName: '',
  });

  const [generatedCards, setGeneratedCards] = useState<GiftCard[]>([]);
  const [lastGeneratedValue, setLastGeneratedValue] = useState<string>('0.00');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    const quantity = parseInt(formData.quantity, 10);
    if (quantity < 2 || quantity > 100) {
      setError('A quantidade deve estar entre 2 e 100');
      return;
    }

    if (!formData.value || !formData.buyerName) {
      setError('Preencha todos os campos obrigatórios');
      return;
    }

    try {
      setIsGenerating(true);
      
      const firstThreeDigits = generateRandomThreeDigits();
      
      const newGiftCards: GiftCard[] = [];
      
      for (let i = 0; i < quantity; i++) {
        const giftCard: GiftCard = {
          id: uuidv4(),
          code: generateBulkCode(firstThreeDigits),
          value: Number(formData.value),
          recipientName: '', // Empty recipient name for bulk cards
          buyerName: formData.buyerName,
          createdAt: new Date(),
          used: false,
          isBulkGenerated: true
        };
        
        newGiftCards.push(giftCard);
      }
      
      onGenerate(newGiftCards);
      setLastGeneratedValue(formData.value);
      setFormData({ ...formData, quantity: '2', value: '', buyerName: '' });
      setGeneratedCards(newGiftCards);
    } catch (err) {
      console.error('Error generating bulk cards:', err);
      setError('Erro ao gerar vale-presentes em lote');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateRandomThreeDigits = () => {
    let digits;
    do {
      digits = Math.floor(Math.random() * 900 + 100).toString();
    } while (digits.startsWith('N'));
    return digits;
  };

  const generateBulkCode = (firstThreeDigits: string) => {
    const randomPart = uuidv4().replace(/-/g, '').substring(0, 13).toUpperCase();
    return `${firstThreeDigits}${randomPart}`.substring(0, 16);
  };

  return (
    <div className="space-y-8">
      {/* Preview Section - Moved to the top */}
      {generatedCards.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-medium text-gray-900 mb-4 text-center">Vale-Presentes Gerados</h3>
          <div className="mb-4 p-4 bg-gray-50 rounded-md border border-gray-200">
            <p className="text-sm text-gray-600">Foram gerados <span className="font-medium">{generatedCards.length}</span> vale-presentes no valor de <span className="font-medium">R$ {Number(lastGeneratedValue).toFixed(2)}</span> cada.</p>
          </div>
          <GiftCardPrint giftCards={generatedCards} isBulk={true} />
        </div>
      )}

      {/* Form Section */}
      <div className="w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <h2 className="section-header">Gerar em Lote</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200">
              {error}
            </div>
          )}
          {isGenerating && (
            <div className="p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200">
              Gerando vale-presentes...
            </div>
          )}
          <div className="space-y-1">
            <label className="form-label">
              Quantidade
            </label>
            <input
              type="number"
              min="2"
              max="100"
              value={formData.quantity}
              onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
              className="form-input"
              placeholder="Quantidade de vale-presentes a gerar"
            />
          </div>

          <div className="space-y-1">
            <label className="form-label">
              Valor (R$)
            </label>
            <input
              type="number"
              step="0.01"
              required
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              className="form-input"
              placeholder="0.00"
            />
          </div>

          <div className="space-y-1">
            <label className="form-label">
              Nome do Comprador
            </label>
            <input
              type="text"
              required
              value={formData.buyerName}
              onChange={(e) => setFormData({ ...formData, buyerName: e.target.value })}
              className="form-input"
              placeholder="Nome de quem está comprando"
            />
          </div>

          <button
            type="submit"
            disabled={isGenerating}
            className="primary-button mt-4"
          >
            <Gift className="h-5 w-5" />
            <span>{isGenerating ? 'Gerando...' : 'Gerar Vale-Presentes'}</span>
          </button>
        </form>
      </div>
    </div>
  );
}
