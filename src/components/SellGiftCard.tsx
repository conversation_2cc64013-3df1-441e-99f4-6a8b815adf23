import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Gift } from 'lucide-react';
import type { GiftCard } from '../types';
import { GiftCardPrint } from './GiftCardPrint';

interface SellGiftCardProps {
  onSell: (giftCard: GiftCard) => void;
}

export function SellGiftCard({ onSell }: SellGiftCardProps) {
  const [formData, setFormData] = useState({
    value: '',
    recipientName: '',
    buyerName: '',
  });

  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCard, setGeneratedCard] = useState<GiftCard | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (!formData.value || !formData.buyerName) {
      setError('Preencha todos os campos obrigatórios');
      return;
    }

    try {
      setIsGenerating(true);
      
      const giftCard: GiftCard = {
        id: uuidv4(),
        code: generateSecureCode(),
        value: Number(formData.value),
        recipientName: formData.recipientName,
        buyerName: formData.buyerName,
        createdAt: new Date(),
        used: false,
      };

      await onSell(giftCard);
      setGeneratedCard(giftCard);
      setFormData({ value: '', recipientName: '', buyerName: '' });
    } catch (err) {
      console.error('Error generating gift card:', err);
      setError('Erro ao gerar vale-presente');
    } finally {
      setIsGenerating(false);
    }
  };

  const generateSecureCode = () => {
    // Always start with 'N' for individually generated cards
    const randomPart = uuidv4().replace(/-/g, '').substring(0, 15).toUpperCase();
    return `N${randomPart}`;
  };

  return (
    <div className="space-y-8">
      <div className="w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <h2 className="section-header">Vender Vale-Presente</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200">
              {error}
            </div>
          )}
          {isGenerating && (
            <div className="p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200">
              Gerando vale-presente...
            </div>
          )}
          <div className="space-y-1">
            <label className="form-label">
              Valor do Vale-Presente (R$)
            </label>
            <input
              type="number"
              step="0.01"
              required
              value={formData.value}
              onChange={(e) => setFormData({ ...formData, value: e.target.value })}
              className="form-input"
              placeholder="0.00"
            />
          </div>
          <div className="space-y-1">
            <label className="form-label">
              Nome do Destinatário
            </label>
            <input
              type="text"
              value={formData.recipientName}
              onChange={(e) => setFormData({ ...formData, recipientName: e.target.value })}
              className="form-input"
              placeholder="Nome de quem vai receber o vale (opcional)"
            />
          </div>
          <div className="space-y-1">
            <label className="form-label">
              Nome do Comprador
            </label>
            <input
              type="text"
              required
              value={formData.buyerName}
              onChange={(e) => setFormData({ ...formData, buyerName: e.target.value })}
              className="form-input"
              placeholder="Nome de quem está comprando"
            />
          </div>
          <button
            type="submit"
            disabled={isGenerating}
            className="primary-button mt-4"
          >
            <Gift className="h-5 w-5" />
            <span>{isGenerating ? 'Gerando...' : 'Gerar Vale-Presente'}</span>
          </button>
        </form>
      </div>

      {/* Preview Section removed to avoid duplication with App.tsx */}
    </div>
  );
}