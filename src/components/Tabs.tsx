import React from 'react';
import { CreditCard, QrCode, List, Package } from 'lucide-react';

interface TabsProps {
  activeTab: 'sell' | 'redeem' | 'list' | 'bulk';
  onTabChange: (tab: 'sell' | 'redeem' | 'list' | 'bulk') => void;
  isAuthenticated: boolean;
}

export function Tabs({ activeTab, onTabChange, isAuthenticated }: TabsProps) {
  // Define available tabs based on authentication status
  const availableTabs = isAuthenticated 
    ? ['sell', 'bulk', 'redeem', 'list']
    : ['sell', 'redeem', 'list'];

  return (
    <div style={{ display: 'flex', gap: '0.5rem' }} key="tabs-container-updated">
      {availableTabs.map(tab => (
        <button
          key={tab}
          onClick={() => onTabChange(tab as 'sell' | 'redeem' | 'list' | 'bulk')}
          style={{
            backgroundColor: 'black',
            color: activeTab === tab ? '#dc2626' : 'white',
            border: activeTab === tab ? '2px solid #dc2626' : 'none',
            padding: '0.5rem 1rem',
            borderRadius: '0.375rem',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            transition: 'all 300ms'
          }}
        >
          {tab === 'sell' && <CreditCard className="h-5 w-5 mr-2" />}
          {tab === 'bulk' && <Package className="h-5 w-5 mr-2" />}
          {tab === 'redeem' && <QrCode className="h-5 w-5 mr-2" />}
          {tab === 'list' && <List className="h-5 w-5 mr-2" />}
          <span>
            {tab === 'sell' && 'Vender Vale-Presente'}
            {tab === 'bulk' && 'Gerar em Lote'}
            {tab === 'redeem' && 'Resgatar Vale-Presente'}
            {tab === 'list' && 'Listar Vale-Presentes'}
          </span>
        </button>
      ))}
    </div>
  );
}