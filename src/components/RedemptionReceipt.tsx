import React from 'react';
import { Printer } from 'lucide-react';
import type { GiftCard } from '../types';
import logoImage from '../assets/logo.png';

interface RedemptionReceiptProps {
  giftCard: GiftCard;
}

export function RedemptionReceipt({ giftCard }: RedemptionReceiptProps) {
  const handlePrint = () => {
    const printContent = document.getElementById('redemption-receipt');
    if (printContent) {
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert('Por favor, permita pop-ups para imprimir o comprovante.');
        return;
      }
      
      // Create the print content with proper styling for 80mm receipt
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Comprovante de Resgate</title>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
                padding: 0;
              }
              body {
                margin: 0;
                padding: 4mm;
                font-family: Arial, sans-serif;
                width: 72mm; /* 80mm - 8mm padding */
                box-sizing: border-box;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              .receipt {
                width: 100%;
                background: white;
              }
              .logo {
                height: 8mm;
                width: auto;
                object-fit: contain;
                display: block;
                margin: 0 auto 4mm auto;
                visibility: visible !important;
              }
              .header {
                text-align: center;
                margin-bottom: 4mm;
              }
              .title {
                font-size: 5mm;
                font-weight: bold;
                margin: 0;
              }
              .date {
                font-size: 3mm;
                color: #666;
                margin: 1mm 0 0 0;
              }
              .info {
                margin-bottom: 3mm;
              }
              .label {
                font-size: 3mm;
                color: #666;
                margin: 0;
              }
              .value {
                font-size: 3.5mm;
                margin: 1mm 0 0 0;
              }
              .amount {
                font-size: 6mm;
                font-weight: bold;
                margin: 1mm 0 0 0;
              }
              .signature {
                margin-top: 8mm;
                padding-top: 8mm;
                border-top: 1px solid #ddd;
              }
              .signature-line {
                border-bottom: 1px solid #666;
                margin-bottom: 2mm;
              }
              .signature-label {
                text-align: center;
                font-size: 3mm;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <img src="${logoImage}" alt="Logo" class="logo" />
              
              <div class="header">
                <h2 class="title">Comprovante de Resgate</h2>
                <p class="date">${new Date().toLocaleDateString('pt-BR')}</p>
              </div>
              
              <div class="info">
                <p class="label">Vale-Presente:</p>
                <p class="value">${giftCard?.code || 'N/A'}</p>
              </div>
              
              <div class="info">
                <p class="label">Valor Resgatado:</p>
                <p class="amount">R$ ${formattedValue}</p>
              </div>
              
              <div class="signature">
                <div class="signature-line"></div>
                <p class="signature-label">Assinatura</p>
              </div>
            </div>
          </body>
        </html>
      `);
      
      // Print and close the window
      printWindow.document.close();
      printWindow.focus();
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);
    }
  };

  // Safely format the value, handling undefined
  const formattedValue = typeof giftCard?.value === 'number' 
    ? giftCard.value.toFixed(2)
    : '0.00';

  // Log the gift card data to debug
  console.log('RedemptionReceipt received giftCard:', giftCard);

  return (
    <div>
      {/* Print Button */}
      <button
        onClick={handlePrint}
        className="mb-4 flex items-center space-x-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
      >
        <Printer className="h-5 w-5" />
        <span>Imprimir Comprovante</span>
      </button>

      {/* Receipt Content - Styled for 80mm width */}
      <div className="flex justify-center">
        <div id="redemption-receipt" className="w-[302px] p-4 bg-white print:w-[80mm] print:p-2 print:border-none print:shadow-none print-content print:text-black">
          {/* Header with logo and title */}
          <div className="flex items-center mb-3">
            <div className="flex-shrink-0 w-1/2">
              <img 
                src={logoImage} 
                alt="Logo" 
                style={{
                  height: '28px',
                  width: 'auto',
                  objectFit: 'contain',
                  visibility: 'visible'
                }}
              />
            </div>
            <div className="flex-grow text-right">
              <h2 className="font-bold text-black text-lg print:text-black" style={{color: 'black !important'}}>
                Comprovante de Resgate
              </h2>
            </div>
          </div>

          <div className="flex justify-between items-center mb-3">
            <div>
              <p className="text-sm print:text-black" style={{color: 'black !important'}}>Data:</p>
              <p className="font-medium text-base print:text-black" style={{color: 'black !important'}}>
                {new Date().toLocaleDateString('pt-BR')}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm print:text-black" style={{color: 'black !important'}}>Valor:</p>
              <p className="font-bold text-2xl" style={{color: 'black !important'}}>
                R$ {formattedValue}
              </p>
            </div>
          </div>

          <div className="text-center mb-3">
            <p className="text-sm print:text-black" style={{color: 'black !important'}}>Vale-Presente:</p>
            <p className="font-mono tracking-wider text-lg print:text-black" style={{color: 'black !important'}}>
              {giftCard?.code || 'N/A'}
            </p>
          </div>

          {/* Signature Area */}
          <div className="mt-8 pt-4 border-t border-gray-200">
            <p className="text-center text-sm text-gray-600" style={{color: 'black !important'}}>Assinatura</p>
          </div>
        </div>
      </div>
    </div>
  );
}
