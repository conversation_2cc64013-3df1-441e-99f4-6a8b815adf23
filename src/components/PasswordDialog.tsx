import React, { useState } from 'react';
import { Lock, Unlock, X } from 'lucide-react';
import { useAuth } from '../lib/AuthContext';

interface PasswordDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PasswordDialog({ isOpen, onClose }: PasswordDialogProps) {
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, login, logout } = useAuth();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    if (isAuthenticated) {
      logout();
      onClose();
      return;
    }
    
    if (!password.trim()) {
      setError('Por favor, insira a senha');
      return;
    }
    
    const success = login(password);
    if (success) {
      setPassword('');
      onClose();
    } else {
      setError('Senha incorreta');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            {isAuthenticated ? 'Bloquear Acesso' : 'Acesso Restrito'}
          </h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit}>
          {!isAuthenticated ? (
            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Senha
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                placeholder="Digite a senha"
                autoFocus
              />
            </div>
          ) : (
            <div className="mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-md">
              <p>Você tem acesso às funcionalidades restritas. Deseja bloquear o acesso?</p>
            </div>
          )}
          
          <div className="flex justify-end">
            <button
              type="submit"
              className={`flex items-center space-x-2 px-4 py-2 rounded-md ${isAuthenticated 
                ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                : 'bg-red-600 hover:bg-red-700 text-white'}`}
            >
              {isAuthenticated ? (
                <>
                  <Lock className="h-4 w-4" />
                  <span>Bloquear Acesso</span>
                </>
              ) : (
                <>
                  <Unlock className="h-4 w-4" />
                  <span>Desbloquear</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
