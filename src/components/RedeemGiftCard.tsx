import React, { useState } from 'react';
import { QrCode, AlertCircle } from 'lucide-react';
import type { GiftCard } from '../types';
import { RedemptionReceipt } from './RedemptionReceipt';

interface RedeemGiftCardProps {
  onRedeem: (code: string) => Promise<{ success: boolean; message: string; giftCard?: GiftCard }>;
}

export function RedeemGiftCard({ onRedeem }: RedeemGiftCardProps) {
  const [code, setCode] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<GiftCard | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setLoading(true);

    try {
      const result = await onRedeem(code);
      if (result.success && result.giftCard) {
        setSuccess(result.giftCard);
        setCode('');
      } else {
        setError(result.message || 'Vale-presente inválido ou já utilizado');
      }
    } catch (err) {
      console.error('Error redeeming gift card:', err);
      setError('Erro ao resgatar vale-presente. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Redemption Receipt - Now at the top */}
      {success && (
        <div className="w-full bg-white p-6 rounded-lg shadow-md">
          <RedemptionReceipt giftCard={success} />
        </div>
      )}
      
      {/* Redemption Form */}
      <div className="w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <h2 className="section-header">Resgatar Vale-Presente</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-1">
            <label className="form-label">
              Código do Vale-Presente
            </label>
            <input
              type="text"
              name="code"
              required
              className="form-input"
              value={code}
              onChange={(e) => setCode(e.target.value.toUpperCase())}
              placeholder="Digite o código manualmente"
              disabled={loading}
            />
          </div>

          {error && (
            <div className="rounded-md bg-red-50 p-4 border border-red-200">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          <button
            type="submit"
            className="primary-button mt-4"
            disabled={loading}
          >
            <QrCode className="h-5 w-5" />
            <span>{loading ? 'Resgatando...' : 'Resgatar Vale-Presente'}</span>
          </button>
        </form>
      </div>
    </div>
  );
}