import React from 'react';
import { Printer } from 'lucide-react';
import type { GiftCard } from '../types';
import logoImage from '../assets/logo.png';

interface GiftCardPrintProps {
  giftCards: GiftCard[];
  isBulk?: boolean;
  isFromVenderTab?: boolean;
}

const GiftCardContent: React.FC<{ giftCard: GiftCard; isBulk?: boolean }> = ({ giftCard, isBulk }) => {
  const containerClasses = isBulk
    ? 'w-[226px] p-3' // ~60mm width for bulk
    : 'w-[302px] p-4'; // ~80mm width for individual

  const getExpirationDate = () => {
    const today = new Date();
    // Get the month after next (current month + 2)
    let targetMonth = today.getMonth() + 2;
    let targetYear = today.getFullYear();
    
    // Adjust if we cross into a new year
    if (targetMonth > 11) {
      targetMonth = targetMonth - 12;
      targetYear += 1;
    }
    
    // Get the last day of the target month
    // Setting day to 0 gives the last day of the previous month
    const lastDay = new Date(targetYear, targetMonth + 1, 0).getDate();
    
    // Format the date as DD/MM/YYYY
    return `${lastDay.toString().padStart(2, '0')}/${(targetMonth + 1).toString().padStart(2, '0')}/${targetYear}`;
  };

  return (
    <div className={`${containerClasses} bg-white`}>
      {/* Header with logo and title */}
      <div className="flex items-center mb-3">
        <div className="flex-shrink-0 w-1/2">
          <img 
            src={logoImage}
            alt="Logo" 
            style={{ 
              height: isBulk ? '20px' : '28px',
              width: 'auto',
              objectFit: 'contain',
              visibility: 'visible',
            }}
          />
        </div>
        <div className="flex-grow text-right">
          <h2 
            className={`font-bold ${isBulk ? 'text-red-600' : 'text-black'} whitespace-nowrap ${isBulk ? 'text-sm' : 'text-lg'}`}
          >
            Vale-Presente
          </h2>
        </div>
      </div>
      
      {/* Recipient and Value */}
      <div className="flex justify-between items-center mb-3">
        {giftCard.recipientName && (
          <div>
            <p className={`${isBulk ? 'text-gray-600' : ''} ${isBulk ? 'text-xs' : 'text-sm'}`}>Para:</p>
            <p className={`font-medium ${isBulk ? 'text-gray-800' : ''} ${isBulk ? 'text-sm' : 'text-base'}`}>
              {giftCard.recipientName}
            </p>
          </div>
        )}
        <div className={`text-${giftCard.recipientName ? 'right' : 'center'} ${!giftCard.recipientName ? 'w-full' : ''}`}>
          <p className={`${isBulk ? 'text-gray-600' : ''} ${isBulk ? 'text-xs' : 'text-sm'}`}>Valor:</p>
          <p className={`font-bold ${isBulk ? 'text-red-600' : ''} ${isBulk ? 'text-xl' : 'text-2xl'}`}>
            R$ {giftCard.value.toFixed(2)}
          </p>
        </div>
      </div>

      {/* QR Code */}
      <div className="flex justify-center mb-2">
        <img 
          src={`https://api.qrserver.com/v1/create-qr-code/?size=${isBulk ? 100 : 130}&data=${encodeURIComponent(giftCard.code)}`}
          alt="QR Code"
          className={`w-[${isBulk ? 100 : 130}px] h-[${isBulk ? 100 : 130}px]`}
        />
      </div>

      {/* Code */}
      <div className="text-center">
        <p className={`font-mono tracking-wider ${isBulk ? 'text-gray-800' : ''} ${isBulk ? 'text-base' : 'text-lg'}`}>
          {giftCard.code}
        </p>
      </div>

      <div className={`mt-3 text-center ${isBulk ? 'text-gray-500' : ''}`}>
        <p className={`font-medium ${isBulk ? 'text-xs' : 'text-sm'}`}>Um presente de: {giftCard.buyerName}</p>
        <p className={`mt-1 ${isBulk ? 'text-[10px]' : 'text-xs'}`}>
          Válido até {getExpirationDate()}
        </p>
      </div>
    </div>
  );
};

export function GiftCardPrint({ giftCards = [], isBulk = false, isFromVenderTab = false }: GiftCardPrintProps) {
  if (!giftCards || giftCards.length === 0) {
    return null;
  }

  const getExpirationDate = () => {
    const today = new Date();
    let targetMonth = today.getMonth() + 2;
    let targetYear = today.getFullYear();
    
    if (targetMonth > 11) {
      targetMonth = targetMonth - 12;
      targetYear += 1;
    }
    
    const lastDay = new Date(targetYear, targetMonth + 1, 0).getDate();
    return `${lastDay.toString().padStart(2, '0')}/${(targetMonth + 1).toString().padStart(2, '0')}/${targetYear}`;
  };

  const generateCardHTML = (card: GiftCard, isBulk: boolean) => {
    // Use the imported logo directly
    // Convert the imported image to a data URL to ensure it works in the print window
    const getBase64Image = (img) => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0);
      return canvas.toDataURL("image/png");
    };
    
    // Create an image element to load the logo
    const img = new Image();
    img.src = logoImage;
    // Default logo path as fallback
    let logoSrc = logoImage;
    
    // Try to convert to data URL if the image is loaded
    if (img.complete) {
      try {
        logoSrc = getBase64Image(img);
      } catch (e) {
        console.error("Error converting logo to data URL", e);
      }
    }

    return `
      <div style="
        width: ${isBulk ? '226px' : '302px'}; 
        padding: ${isBulk ? '0.75rem' : '1rem'}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${logoSrc}"
              alt="Logo" 
              style="height: ${isBulk ? '20px' : '28px'}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${isBulk ? '0.875rem' : '1.125rem'}; 
              margin: 0; 
              color: ${isBulk ? '#dc2626' : 'black'};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${card.recipientName ? `
            <div>
              <p style="font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0; color: ${isBulk ? '#4b5563' : 'black'};">Para:</p>
              <p style="font-weight: 500; font-size: ${isBulk ? '0.875rem' : '1rem'}; margin: 0; color: ${isBulk ? '#1f2937' : 'black'}">
                ${card.recipientName}
              </p>
            </div>
          ` : ''}
          <div style="text-align: ${card.recipientName ? 'right' : 'center'}; ${!card.recipientName ? 'width: 100%;' : ''}">
            <p style="font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0; color: ${isBulk ? '#4b5563' : 'black'};">Valor:</p>
            <p style="font-weight: 700; font-size: ${isBulk ? '1.25rem' : '1.5rem'}; margin: 0; color: ${isBulk ? '#dc2626' : 'black'}">
              R$ ${card.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${isBulk ? 100 : 130}&data=${encodeURIComponent(card.code)}" 
               style="width: ${isBulk ? '100px' : '130px'}; height: ${isBulk ? '100px' : '130px'};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${isBulk ? '1rem' : '1.125rem'}; 
            margin: 0; 
            color: ${isBulk ? '#1f2937' : 'black'};
          ">
            ${card.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${isBulk ? '#6b7280' : 'black'};">
          <p style="font-weight: 500; font-size: ${isBulk ? '0.75rem' : '0.875rem'}; margin: 0;">Um presente de: ${card.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${isBulk ? '0.625rem' : '0.75rem'}; margin: 0;">
            Válido até ${getExpirationDate()}
          </p>
        </div>
      </div>
    `;
  };

  const handlePrint = () => {
    // First print the gift card
    printGiftCard();
    
    // If this is from the Vender tab, also print the receipt after a short delay
    if (isFromVenderTab && !isBulk && giftCards.length === 1) {
      setTimeout(() => {
        printReceipt(giftCards[0]);
      }, 1000);
    }
  };
  
  const printGiftCard = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = `
        <html>
          <head>
            <title>${isBulk ? 'Lote de Vale-Presentes' : 'Vale-Presente'}</title>
            <style>
              @media print {
                @page {
                  ${isBulk ? 'size: A4; margin: 10mm;' : 'size: 80mm auto; margin: 0;'}
                }
                body {
                  margin: 0;
                  padding: ${isBulk ? '10mm' : '4mm'};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${isBulk ? `
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                  ${giftCards.map(card => `
                    <div style="margin-bottom: 16px;">
                      ${generateCardHTML(card, true)}
                    </div>
                  `).join('')}
                </div>
              ` : `
                <div style="${isFromVenderTab ? '' : 'display: flex; justify-content: center;'}">
                  ${generateCardHTML(giftCards[0], false)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            </script>
          </body>
        </html>
      `;
      
      printWindow.document.write(printContent);
      printWindow.document.close();
    }
  };
  
  const printReceipt = (card: GiftCard) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const currentDate = new Date();
      const formattedDate = currentDate.toLocaleDateString('pt-BR');
      const formattedTime = currentDate.toLocaleTimeString('pt-BR');
      
      // Mask the gift card code (show first 3 and last 3 characters)
      const maskCode = (code) => {
        if (code.length <= 6) return code;
        return code.substring(0, 3) + '*'.repeat(code.length - 6) + code.substring(code.length - 3);
      };
      
      const maskedCode = maskCode(card.code);
      
      // Convert the imported image to a data URL to ensure it works in the print window
      const getBase64Image = (img) => {
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0);
        return canvas.toDataURL("image/png");
      };
      
      // Create an image element to load the logo
      const img = new Image();
      img.src = logoImage;
      // Default logo path as fallback
      let logoSrc = logoImage;
      
      // Try to convert to data URL if the image is loaded
      if (img.complete) {
        try {
          logoSrc = getBase64Image(img);
        } catch (e) {
          console.error("Error converting logo to data URL", e);
        }
      }
      
      const receiptContent = `
        <html>
          <head>
            <title>Controle Interno - Vale-Presente</title>
            <style>
              @media print {
                @page {
                  size: 80mm auto;
                  margin: 0;
                }
                body {
                  margin: 0;
                  padding: 4mm;
                }
              }
              
              body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
              }
              
              .receipt {
                width: 72mm;
              }
              
              .header {
                text-align: center;
                margin-bottom: 10px;
              }
              
              .divider {
                border-top: 1px dashed #000;
                margin: 10px 0;
              }
              
              .info-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
              }
              
              .footer {
                text-align: center;
                margin-top: 10px;
                font-size: 10px;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <div class="header">
                <img src="${logoSrc}" alt="Logo" style="height: 30px; margin-bottom: 5px;" />
                <h3 style="margin: 5px 0;">CONTROLE INTERNO - VALE-PRESENTE</h3>
              </div>
              
              <div class="divider"></div>
              
              <div class="info-row">
                <span>Data:</span>
                <span>${formattedDate}</span>
              </div>
              
              <div class="info-row">
                <span>Hora:</span>
                <span>${formattedTime}</span>
              </div>
              
              <div class="info-row">
                <span>Código:</span>
                <span>${maskedCode}</span>
              </div>
              
              <div class="info-row">
                <span>Valor:</span>
                <span>R$ ${card.value.toFixed(2)}</span>
              </div>
              
              ${card.recipientName ? `
              <div class="info-row">
                <span>Destinatário:</span>
                <span>${card.recipientName}</span>
              </div>
              ` : ''}
              
              <div class="info-row">
                <span>Comprador:</span>
                <span>${card.buyerName}</span>
              </div>
              
              <div class="divider"></div>
              
              <p style="text-align: center; margin: 10px 0;">Válido até ${getExpirationDate()}</p>
              
              <div class="divider"></div>
              
              <div class="footer">
                <p>CONTROLE INTERNO - NÃO ENTREGAR AO CLIENTE</p>
              </div>
            </div>
            
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            </script>
          </body>
        </html>
      `;
      
      printWindow.document.write(receiptContent);
      printWindow.document.close();
    }
  };

  return (
    <div>
      <button
        onClick={handlePrint}
        className="mb-4 flex items-center gap-2 px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors"
      >
        <Printer size={20} />
        Imprimir Vale-Presente{giftCards.length > 1 ? 's' : ''}
      </button>

      <div className="print:m-0">
        {isBulk ? (
          <div className="grid grid-cols-3 gap-4">
            {giftCards.map((giftCard) => (
              <div key={giftCard.id || giftCard.code} className="mb-4 print:mb-0">
                <div className="w-[226px] p-3 bg-white">
                  <div className="flex items-center mb-3">
                    <div className="flex-shrink-0 w-1/2">
                      <img src={logoImage} alt="Logo" className="h-5 w-auto object-contain" />
                    </div>
                    <div className="flex-grow text-right">
                      <h2 className="font-bold text-red-600 whitespace-nowrap text-sm">Vale-Presente</h2>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center mb-3">
                    {giftCard.recipientName && (
                      <div>
                        <p className="text-gray-600 text-xs">Para:</p>
                        <p className="font-medium text-gray-800 text-sm">{giftCard.recipientName}</p>
                      </div>
                    )}
                    <div className={`text-${giftCard.recipientName ? 'right' : 'center'} ${!giftCard.recipientName ? 'w-full' : ''}`}>
                      <p className="text-gray-600 text-xs">Valor:</p>
                      <p className="font-bold text-red-600 text-xl">R$ {giftCard.value.toFixed(2)}</p>
                    </div>
                  </div>

                  <div className="flex justify-center mb-2">
                    <img 
                      src={`https://api.qrserver.com/v1/create-qr-code/?size=100&data=${encodeURIComponent(giftCard.code)}`}
                      alt="QR Code"
                      className="w-[100px] h-[100px]"
                    />
                  </div>

                  <div className="text-center">
                    <p className="font-mono tracking-wider text-gray-800 text-base">{giftCard.code}</p>
                  </div>

                  <div className="mt-3 text-center text-gray-500">
                    <p className="font-medium text-xs">Um presente de: {giftCard.buyerName}</p>
                    <p className="mt-1 text-[10px]">Válido até {getExpirationDate()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="w-[302px] p-4 bg-white">
              <div className="flex items-center mb-3">
                <div className="flex-shrink-0 w-1/2">
                  <img src={logoImage} alt="Logo" className="h-7 w-auto object-contain" />
                </div>
                <div className="flex-grow text-right">
                  <h2 className="font-bold text-black whitespace-nowrap text-lg">Vale-Presente</h2>
                </div>
              </div>
              
              <div className="flex justify-between items-center mb-3">
                {giftCards[0].recipientName && (
                  <div>
                    <p className="text-sm">Para:</p>
                    <p className="font-medium text-base">{giftCards[0].recipientName}</p>
                  </div>
                )}
                <div className={`text-${giftCards[0].recipientName ? 'right' : 'center'} ${!giftCards[0].recipientName ? 'w-full' : ''}`}>
                  <p className="text-sm">Valor:</p>
                  <p className="font-bold text-2xl">R$ {giftCards[0].value.toFixed(2)}</p>
                </div>
              </div>

              <div className="flex justify-center mb-2">
                <img 
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=130&data=${encodeURIComponent(giftCards[0].code)}`}
                  alt="QR Code"
                  className="w-[130px] h-[130px]"
                />
              </div>

              <div className="text-center">
                <p className="font-mono tracking-wider text-lg">{giftCards[0].code}</p>
              </div>

              <div className="mt-3 text-center">
                <p className="font-medium text-sm">Um presente de: {giftCards[0].buyerName}</p>
                <p className="mt-1 text-xs">Válido até {getExpirationDate()}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}