import { supabase } from './supabase';
import { initializeHeartbeatTable } from './migrate-heartbeat';

export class HeartbeatService {
  private intervalId: NodeJS.Timeout | null = null;
  private readonly HEARTBEAT_INTERVAL = 24 * 60 * 60 * 1000; // 24 horas (1 dia)
  private readonly MAX_RECORDS = 7; // Manter apenas os últimos 7 registros (1 semana)

  /**
   * Inicia o serviço de heartbeat
   */
  async start(): Promise<void> {
    if (this.intervalId) {
      console.log('Heartbeat service já está rodando');
      return;
    }

    console.log('Iniciando heartbeat service...');

    // Inicializa a tabela se necessário
    try {
      await initializeHeartbeatTable();
    } catch (error) {
      console.error('Erro ao inicializar tabela heartbeat:', error);
      // Continua mesmo se houver erro na inicialização
    }

    // Executa imediatamente
    this.sendHeartbeat();

    // Configura execução periódica
    this.intervalId = setInterval(() => {
      this.sendHeartbeat();
    }, this.HEARTBEAT_INTERVAL);
  }

  /**
   * Para o serviço de heartbeat
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
      console.log('Heartbeat service parado');
    }
  }

  /**
   * Envia um heartbeat para o banco de dados
   */
  private async sendHeartbeat(): Promise<void> {
    try {
      // Insere novo registro de heartbeat
      const { error: insertError } = await supabase
        .from('heartbeat')
        .insert({
          status: 'alive',
          timestamp: new Date().toISOString()
        });

      if (insertError) {
        console.error('Erro ao inserir heartbeat:', insertError);
        return;
      }

      console.log('Heartbeat enviado com sucesso:', new Date().toISOString());

      // Limpa registros antigos para não acumular dados desnecessários
      await this.cleanupOldRecords();

    } catch (error) {
      console.error('Erro no heartbeat service:', error);
    }
  }

  /**
   * Remove registros antigos mantendo apenas os mais recentes
   */
  private async cleanupOldRecords(): Promise<void> {
    try {
      // Busca todos os registros ordenados por timestamp (mais recente primeiro)
      const { data: records, error: selectError } = await supabase
        .from('heartbeat')
        .select('id, timestamp')
        .order('timestamp', { ascending: false });

      if (selectError) {
        console.error('Erro ao buscar registros de heartbeat:', selectError);
        return;
      }

      // Se temos mais registros que o máximo permitido, remove os mais antigos
      if (records && records.length > this.MAX_RECORDS) {
        const recordsToDelete = records.slice(this.MAX_RECORDS);
        const idsToDelete = recordsToDelete.map(record => record.id);

        const { error: deleteError } = await supabase
          .from('heartbeat')
          .delete()
          .in('id', idsToDelete);

        if (deleteError) {
          console.error('Erro ao limpar registros antigos:', deleteError);
        } else {
          console.log(`Removidos ${recordsToDelete.length} registros antigos de heartbeat`);
        }
      }
    } catch (error) {
      console.error('Erro na limpeza de registros antigos:', error);
    }
  }

  /**
   * Verifica se o serviço está rodando
   */
  isRunning(): boolean {
    return this.intervalId !== null;
  }

  /**
   * Força o envio de um heartbeat imediatamente
   */
  async forceHeartbeat(): Promise<void> {
    await this.sendHeartbeat();
  }
}

// Instância singleton do serviço
export const heartbeatService = new HeartbeatService();
