export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      gift_cards: {
        Row: {
          id: string
          code: string
          value: number
          recipient_name: string
          buyer_name: string
          created_at: string
          used: boolean
          used_at: string | null
        }
        Insert: {
          id?: string
          code: string
          value: number
          recipient_name: string
          buyer_name: string
          created_at?: string
          used?: boolean
          used_at?: string | null
        }
        Update: {
          id?: string
          code?: string
          value?: number
          recipient_name?: string
          buyer_name?: string
          created_at?: string
          used?: boolean
          used_at?: string | null
        }
      }
    }
  }
}