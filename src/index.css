/* Import shared styles */
@import './styles/shared.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Print styles */
@media print {
  * {
    color: black !important;
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  
  .print-content {
    width: 80mm !important;
    margin: 0 !important;
    padding: 4mm !important;
  }
}

/* Custom styles for red/black/white theme */
@layer base {
  body {
    @apply bg-white text-gray-800;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply text-red-600;
  }
  
  button:not(.tab-button) {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  input, select, textarea {
    @apply border-gray-300 focus:border-red-500 focus:ring-red-500;
  }
}

/* Print styles for 80mm receipt printer */
@media print {
  /* Hide everything except the gift card/receipt when printing */
  body * {
    visibility: hidden;
  }
  
  .print-content,
  .print-content * {
    visibility: visible !important;
  }
  
  .print-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 80mm;
  }

  /* Set the page size to 80mm width */
  @page {
    size: 80mm auto;
    margin: 0;
  }

  /* Remove background colors and shadows for better printing */
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    background-color: white !important;
    color: black !important;
  }

  /* Hide all buttons when printing */
  button {
    display: none !important;
  }
}
