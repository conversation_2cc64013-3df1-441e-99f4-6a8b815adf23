import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from './lib/supabase';
import { Tabs } from './components/Tabs';
import { SellGiftCard } from './components/SellGiftCard';
import { GiftCardList } from './components/GiftCardList';
import { RedeemGiftCard } from './components/RedeemGiftCard';
import { BulkGiftCardGenerator } from './components/BulkGiftCardGenerator';
import { GiftCardPrint } from './components/GiftCardPrint';
import { PasswordDialog } from './components/PasswordDialog';
import { AuthProvider, useAuth } from './lib/AuthContext';
import type { GiftCard } from './types';
import logoImage from './assets/logo.png';
import ErrorBoundary from './components/ErrorBoundary';
import { Lock, Unlock } from 'lucide-react';

function AppContent() {
  const [giftCards, setGiftCards] = useState<GiftCard[]>([]);
  const [activeTab, setActiveTab] = useState<'sell' | 'redeem' | 'list' | 'bulk'>('sell');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [printGiftCard, setPrintGiftCard] = useState<GiftCard | null>(null);
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    fetchGiftCards();
  }, []);

  const fetchGiftCards = async () => {
    setError(null);
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('gift_cards')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      if (data) {
        const formattedGiftCards: GiftCard[] = data.map(card => ({
          id: card.id,
          code: card.code,
          value: card.value,
          recipientName: card.recipient_name,
          buyerName: card.buyer_name,
          createdAt: new Date(card.created_at),
          used: card.used,
          usedAt: card.used_at ? new Date(card.used_at) : null,
          // Determine if it's bulk generated based on the code pattern
          isBulkGenerated: !card.code.startsWith('N') && card.code.length === 16
        }));
        setGiftCards(formattedGiftCards);
      }
    } catch (error) {
      console.error('Error fetching gift cards:', error);
      setError('Erro ao carregar os vale-presentes. Por favor, tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSellGiftCard = async (giftCard: GiftCard) => {
    setError(null);
    setIsLoading(true);
    try {
      const { error } = await supabase.from('gift_cards').insert({
        id: giftCard.id,
        code: giftCard.code,
        value: giftCard.value,
        recipient_name: giftCard.recipientName,
        buyer_name: giftCard.buyerName,
        created_at: giftCard.createdAt.toISOString(),
        used: false
      });

      if (error) {
        throw error;
      }

      setGiftCards(prev => [giftCard, ...prev]);
      setPrintGiftCard(giftCard);
      return true;
    } catch (err) {
      console.error('Error selling gift card:', err);
      setError('Erro ao salvar vale-presente');
      throw err; // Propagate error to component for proper handling
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkGenerate = async (bulkGiftCards: GiftCard[]) => {
    setError(null);
    setIsLoading(true);
    try {
      // Prepare the data for insertion
      const cardsToInsert = bulkGiftCards.map(card => ({
        id: card.id,
        code: card.code,
        value: card.value,
        recipient_name: card.recipientName,
        buyer_name: card.buyerName,
        created_at: card.createdAt.toISOString(),
        used: false
      }));

      // Insert all cards in a single batch
      const { error } = await supabase
        .from('gift_cards')
        .insert(cardsToInsert);

      if (error) {
        throw error;
      }

      // Update the local state with the new cards
      setGiftCards(prev => [...bulkGiftCards, ...prev]);
      
      // Refresh gift cards from the database to ensure we have the latest data
      fetchGiftCards();
      
      // Set the first card for printing (this will be ignored as bulk printing is handled in the component)
      if (bulkGiftCards.length > 0) {
        setPrintGiftCard(bulkGiftCards[0]);
      }
      
      return true;
    } catch (err) {
      console.error('Error generating bulk gift cards:', err);
      setError('Erro ao gerar vale-presentes em lote');
      throw err; // Propagate error to component for proper handling
    } finally {
      setIsLoading(false);
    }
  };

  const handleRedeemGiftCard = async (code: string) => {
    setError(null);
    setIsLoading(true);
    try {
      // Find the gift card by code
      const giftCard = giftCards.find(card => card.code === code);
      
      if (!giftCard) {
        console.log('Gift card not found with code:', code);
        return { success: false, message: 'Vale-presente não encontrado.' };
      }
      
      if (giftCard.used) {
        console.log('Gift card already used:', giftCard);
        return { success: false, message: 'Este vale-presente já foi utilizado.' };
      }
      
      const now = new Date();
      
      // Update the gift card in the database
      const { error } = await supabase
        .from('gift_cards')
        .update({
          used: true,
          used_at: now.toISOString()
        })
        .eq('code', code);
      
      if (error) {
        console.error('Error updating gift card in database:', error);
        throw error;
      }
      
      // Update the local state
      setGiftCards(prev => 
        prev.map(card => 
          card.code === code 
            ? { ...card, used: true, usedAt: now } 
            : card
        )
      );
      
      const updatedGiftCard = { ...giftCard, used: true, usedAt: now };
      console.log('Successfully redeemed gift card:', updatedGiftCard);
      
      return { 
        success: true, 
        message: 'Vale-presente resgatado com sucesso!',
        giftCard: updatedGiftCard
      };
    } catch (err) {
      console.error('Error redeeming gift card:', err);
      setError('Erro ao resgatar o vale-presente. Por favor, tente novamente.');
      throw err; // Propagate error to component for proper handling
    } finally {
      setIsLoading(false);
    }
  };

  const handleTabChange = (tab: 'sell' | 'redeem' | 'list' | 'bulk') => {
    setActiveTab(tab);
    setPrintGiftCard(null);
  };

  const togglePasswordDialog = () => {
    setIsPasswordDialogOpen(!isPasswordDialogOpen);
  };

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        {error && (
          <div className="mb-4 p-4 bg-red-50 text-red-600 rounded-md">
            {error}
          </div>
        )}
        
        <header className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center mb-8">
              <div className="flex-shrink-0">
                <img src={logoImage} alt="New Look Logo" className="h-16" />
              </div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-800 mr-4">Sistema de Vale-Presentes</h1>
                <button 
                  onClick={togglePasswordDialog}
                  className="p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
                  title={isAuthenticated ? "Bloquear acesso" : "Desbloquear acesso"}
                >
                  {isAuthenticated ? (
                    <Unlock className="h-5 w-5 text-green-600" />
                  ) : (
                    <Lock className="h-5 w-5 text-gray-500" />
                  )}
                </button>
              </div>
            </div>
          </div>
        </header>
        
        <main className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <Tabs activeTab={activeTab} onTabChange={handleTabChange} isAuthenticated={isAuthenticated} />
          
          {/* Print Preview Section - Only for the sell tab */}
          {(printGiftCard && activeTab === 'sell') && (
            <div className="mb-8 bg-white shadow-sm rounded-lg p-6">
              <h3 className="text-xl font-medium text-gray-900 mb-4 text-center">Vale-Presente Gerado</h3>
              <GiftCardPrint giftCards={[printGiftCard]} isFromVenderTab={true} />
            </div>
          )}
          
          <div className="mt-6 bg-white shadow-sm rounded-lg p-6">
            {isLoading && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white p-4 rounded-md shadow-lg">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                </div>
              </div>
            )}
            
            {activeTab === 'sell' && (
              <SellGiftCard onSell={handleSellGiftCard} />
            )}
            
            {activeTab === 'bulk' && isAuthenticated && (
              <BulkGiftCardGenerator onGenerate={handleBulkGenerate} />
            )}
            
            {activeTab === 'list' && (
              <GiftCardList giftCards={giftCards} isAuthenticated={isAuthenticated} />
            )}
            
            {activeTab === 'redeem' && (
              <RedeemGiftCard onRedeem={handleRedeemGiftCard} />
            )}
          </div>
        </main>

        <PasswordDialog 
          isOpen={isPasswordDialogOpen} 
          onClose={() => setIsPasswordDialogOpen(false)} 
        />
      </div>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;