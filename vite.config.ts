import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: [],
  },
  server: {
    host: true, // Expose to local network
  },
  preview: {
    host: true,
    port: 4173,
  },
  build: {
    // Generate source maps for better debugging
    sourcemap: true,
    // Ensure assets are properly processed
    assetsInlineLimit: 4096,
  }
});
