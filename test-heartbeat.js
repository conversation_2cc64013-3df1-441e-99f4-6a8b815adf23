// Script de teste para verificar o sistema de heartbeat
// Execute com: node test-heartbeat.js

import { createClient } from '@supabase/supabase-js';

// Configurações do Supabase (lidas do .env)
const supabaseUrl = 'https://nzluxtdhyiwmyuhmiili.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56bHV4dGRoeWl3bXl1aG1paWxpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA1ODUyNjIsImV4cCI6MjA1NjE2MTI2Mn0.d8dIrFP5nsY2ZHHAvnMnacPDkvix9-xHEvgPfSrmbyI';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Erro: Configurações do Supabase não encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testHeartbeat() {
  console.log('🔄 Testando sistema de heartbeat...\n');

  try {
    // 1. Verificar se a tabela heartbeat existe
    console.log('1. Verificando se a tabela heartbeat existe...');
    const { data: existingData, error: checkError } = await supabase
      .from('heartbeat')
      .select('id')
      .limit(1);

    if (checkError && checkError.code === '42P01') {
      console.log('⚠️  Tabela heartbeat não existe. Tentando criar...');
      
      // Tentar criar a tabela (isso pode falhar se não tiver permissões)
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS heartbeat (
          id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
          timestamp timestamptz DEFAULT now(),
          status text DEFAULT 'alive'
        );

        ALTER TABLE heartbeat ENABLE ROW LEVEL SECURITY;

        DROP POLICY IF EXISTS "Anyone can read heartbeat" ON heartbeat;
        DROP POLICY IF EXISTS "Anyone can insert heartbeat" ON heartbeat;
        DROP POLICY IF EXISTS "Anyone can delete heartbeat" ON heartbeat;

        CREATE POLICY "Anyone can read heartbeat"
          ON heartbeat FOR SELECT TO authenticated USING (true);

        CREATE POLICY "Anyone can insert heartbeat"
          ON heartbeat FOR INSERT TO authenticated WITH CHECK (true);

        CREATE POLICY "Anyone can delete heartbeat"
          ON heartbeat FOR DELETE TO authenticated USING (true);

        CREATE INDEX IF NOT EXISTS idx_heartbeat_timestamp ON heartbeat(timestamp);
      `;
      
      console.log('📝 Execute este SQL no painel do Supabase:');
      console.log('---');
      console.log(createTableSQL);
      console.log('---\n');
      return;
    } else if (checkError) {
      throw checkError;
    }

    console.log('✅ Tabela heartbeat existe!');

    // 2. Inserir um registro de teste
    console.log('2. Inserindo registro de teste...');
    const { data: insertData, error: insertError } = await supabase
      .from('heartbeat')
      .insert({
        status: 'test',
        timestamp: new Date().toISOString()
      })
      .select()
      .single();

    if (insertError) {
      throw insertError;
    }

    console.log('✅ Registro inserido com sucesso:', insertData.id);

    // 3. Buscar registros existentes
    console.log('3. Buscando registros existentes...');
    const { data: allData, error: selectError } = await supabase
      .from('heartbeat')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(5);

    if (selectError) {
      throw selectError;
    }

    console.log(`✅ Encontrados ${allData.length} registros:`);
    allData.forEach((record, index) => {
      const timestamp = new Date(record.timestamp).toLocaleString('pt-BR');
      console.log(`   ${index + 1}. ID: ${record.id.slice(0, 8)}... | Status: ${record.status} | Timestamp: ${timestamp}`);
    });

    // 4. Limpar registro de teste
    console.log('4. Limpando registro de teste...');
    const { error: deleteError } = await supabase
      .from('heartbeat')
      .delete()
      .eq('id', insertData.id);

    if (deleteError) {
      console.log('⚠️  Erro ao limpar registro de teste:', deleteError.message);
    } else {
      console.log('✅ Registro de teste removido');
    }

    console.log('\n🎉 Teste concluído com sucesso!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Inicie sua aplicação: npm run dev');
    console.log('2. O heartbeat será enviado automaticamente a cada 5 minutos');
    console.log('3. Monitore os logs do console para ver os heartbeats');
    console.log('4. Opcional: Adicione o componente HeartbeatMonitor à sua UI');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
    
    if (error.message.includes('JWT')) {
      console.log('\n💡 Dica: Verifique se as chaves do Supabase estão corretas');
    }
    
    if (error.message.includes('permission')) {
      console.log('\n💡 Dica: Verifique as políticas RLS da tabela heartbeat');
    }
  }
}

testHeartbeat();
