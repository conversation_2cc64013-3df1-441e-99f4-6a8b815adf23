-- SQL corrigido para criar a tabela heartbeat no Supabase
-- Execute este SQL no painel do Supabase (SQL Editor)

-- 1. <PERSON>riar a tabela
CREATE TABLE IF NOT EXISTS heartbeat (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  timestamp timestamptz DEFAULT now(),
  status text DEFAULT 'alive'
);

-- 2. Habilitar Row Level Security
ALTER TABLE heartbeat ENABLE ROW LEVEL SECURITY;

-- 3. Remover políticas existentes (se houver)
DROP POLICY IF EXISTS "Anyone can read heartbeat" ON heartbeat;
DROP POLICY IF EXISTS "Anyone can insert heartbeat" ON heartbeat;
DROP POLICY IF EXISTS "Anyone can delete heartbeat" ON heartbeat;

-- 4. <PERSON><PERSON><PERSON> polí<PERSON>s
CREATE POLICY "Anyone can read heartbeat"
  ON heartbeat
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Anyone can insert heartbeat"
  ON heartbeat
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Anyone can delete heartbeat"
  ON heartbeat
  FOR DELETE
  TO authenticated
  USING (true);

-- 5. <PERSON><PERSON><PERSON> índice para performance
CREATE INDEX IF NOT EXISTS idx_heartbeat_timestamp ON heartbeat(timestamp);
