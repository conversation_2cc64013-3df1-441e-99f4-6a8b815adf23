/*
  # Create gift cards table

  1. New Tables
    - `gift_cards`
      - `id` (uuid, primary key)
      - `code` (text, unique)
      - `value` (numeric)
      - `recipient_name` (text)
      - `buyer_name` (text)
      - `created_at` (timestamp)
      - `used` (boolean)
      - `used_at` (timestamp)
  2. Security
    - Enable RLS on `gift_cards` table
    - Add policy for authenticated users to read all gift cards
    - Add policy for authenticated users to insert gift cards
    - Add policy for authenticated users to update gift cards
*/

CREATE TABLE IF NOT EXISTS gift_cards (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  code text UNIQUE NOT NULL,
  value numeric NOT NULL,
  recipient_name text NOT NULL,
  buyer_name text NOT NULL,
  created_at timestamptz DEFAULT now(),
  used boolean DEFAULT false,
  used_at timestamptz
);

ALTER TABLE gift_cards ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to read all gift cards
CREATE POLICY "Anyone can read gift cards"
  ON gift_cards
  FOR SELECT
  TO authenticated
  USING (true);

-- Allow all authenticated users to insert gift cards
CREATE POLICY "Anyone can insert gift cards"
  ON gift_cards
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

-- Allow all authenticated users to update gift cards
CREATE POLICY "Anyone can update gift cards"
  ON gift_cards
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);