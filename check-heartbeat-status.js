// Script para verificar se o heartbeat está funcionando
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://nzluxtdhyiwmyuhmiili.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im56bHV4dGRoeWl3bXl1aG1paWxpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDA1ODUyNjIsImV4cCI6MjA1NjE2MTI2Mn0.d8dIrFP5nsY2ZHHAvnMnacPDkvix9-xHEvgPfSrmbyI';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function checkHeartbeatStatus() {
  console.log('🔍 Verificando status do heartbeat...\n');

  try {
    // Buscar registros de heartbeat
    const { data, error } = await supabase
      .from('heartbeat')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(10);

    if (error) {
      throw error;
    }

    if (!data || data.length === 0) {
      console.log('⚠️  Nenhum registro de heartbeat encontrado');
      console.log('💡 O sistema ainda não enviou nenhum heartbeat automático');
      console.log('📋 Você precisa fazer o deploy da nova versão');
      return;
    }

    console.log(`✅ Encontrados ${data.length} registros de heartbeat:`);
    
    data.forEach((record, index) => {
      const timestamp = new Date(record.timestamp);
      const now = new Date();
      const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));
      
      console.log(`   ${index + 1}. Status: ${record.status} | ${diffMinutes}min atrás | ${timestamp.toLocaleString('pt-BR')}`);
    });

    // Verificar se há heartbeats recentes (últimos 10 minutos)
    const recentHeartbeats = data.filter(record => {
      const timestamp = new Date(record.timestamp);
      const now = new Date();
      const diffMinutes = Math.floor((now - timestamp) / (1000 * 60));
      return diffMinutes <= 10;
    });

    if (recentHeartbeats.length > 0) {
      console.log('\n🎉 Sistema de heartbeat está ATIVO!');
      console.log('✅ Seu banco Supabase não entrará em suspensão');
    } else {
      console.log('\n⚠️  Nenhum heartbeat recente (últimos 10 minutos)');
      console.log('💡 Pode ser que o sistema antigo ainda esteja rodando');
      console.log('📋 Considere fazer o deploy da nova versão');
    }

  } catch (error) {
    console.error('❌ Erro ao verificar heartbeat:', error.message);
  }
}

checkHeartbeatStatus();
