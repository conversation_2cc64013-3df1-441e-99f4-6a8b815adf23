-- SQL 100% SEGURO para criar a tabela heartbeat
-- Este SQL NÃO remove nem altera nada existente
-- Execute este SQL no painel do Supabase (SQL Editor)

-- 1. Criar APENAS a tabela (se não existir)
CREATE TABLE IF NOT EXISTS heartbeat (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  timestamp timestamptz DEFAULT now(),
  status text DEFAULT 'alive'
);

-- 2. Habilitar Row Level Security APENAS na tabela heartbeat
ALTER TABLE heartbeat ENABLE ROW LEVEL SECURITY;

-- 3. <PERSON>riar políticas APENAS se não existirem (usando nomes únicos)
DO $$
BEGIN
    -- Política de leitura
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'heartbeat' 
        AND policyname = 'heartbeat_read_policy'
    ) THEN
        CREATE POLICY "heartbeat_read_policy"
          ON heartbeat
          FOR SELECT
          TO authenticated
          USING (true);
    END IF;

    -- Política de inserção
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'heartbeat' 
        AND policyname = 'heartbeat_insert_policy'
    ) THEN
        CREATE POLICY "heartbeat_insert_policy"
          ON heartbeat
          FOR INSERT
          TO authenticated
          WITH CHECK (true);
    END IF;

    -- Política de exclusão
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'heartbeat' 
        AND policyname = 'heartbeat_delete_policy'
    ) THEN
        CREATE POLICY "heartbeat_delete_policy"
          ON heartbeat
          FOR DELETE
          TO authenticated
          USING (true);
    END IF;
END
$$;

-- 4. Criar índice APENAS se não existir
CREATE INDEX IF NOT EXISTS idx_heartbeat_timestamp ON heartbeat(timestamp);
