<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loading...</title>
    <script>
      // Dynamically set the page title from pagename.txt
      fetch('/pagename.txt')
        .then(response => response.text())
        .then(text => {
          document.title = text.trim();
        })
        .catch(err => {
          console.error('Error loading page title:', err);
        });
    </script>
    <script type="module" crossorigin src="/assets/index-BS0-NtgM.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-UH_1Iy52.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
