var Vh=Object.defineProperty;var Bh=(c,s,i)=>s in c?Vh(c,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):c[s]=i;var yi=(c,s,i)=>Bh(c,typeof s!="symbol"?s+"":s,i);(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const d of a)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function i(a){const d={};return a.integrity&&(d.integrity=a.integrity),a.referrerPolicy&&(d.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?d.credentials="include":a.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(a){if(a.ep)return;a.ep=!0;const d=i(a);fetch(a.href,d)}})();function Hh(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}function qh(c){if(c.__esModule)return c;var s=c.default;if(typeof s=="function"){var i=function l(){return this instanceof l?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};i.prototype=s.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(c).forEach(function(l){var a=Object.getOwnPropertyDescriptor(c,l);Object.defineProperty(i,l,a.get?a:{enumerable:!0,get:function(){return c[l]}})}),i}var jl={exports:{}},es={},Cl={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bc;function Wh(){if(Bc)return te;Bc=1;var c=Symbol.for("react.element"),s=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),E=Symbol.iterator;function P(k){return k===null||typeof k!="object"?null:(k=E&&k[E]||k["@@iterator"],typeof k=="function"?k:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},L=Object.assign,F={};function D(k,T,ee){this.props=k,this.context=T,this.refs=F,this.updater=ee||O}D.prototype.isReactComponent={},D.prototype.setState=function(k,T){if(typeof k!="object"&&typeof k!="function"&&k!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,k,T,"setState")},D.prototype.forceUpdate=function(k){this.updater.enqueueForceUpdate(this,k,"forceUpdate")};function re(){}re.prototype=D.prototype;function ae(k,T,ee){this.props=k,this.context=T,this.refs=F,this.updater=ee||O}var U=ae.prototype=new re;U.constructor=ae,L(U,D.prototype),U.isPureReactComponent=!0;var q=Array.isArray,$=Object.prototype.hasOwnProperty,A={current:null},X={key:!0,ref:!0,__self:!0,__source:!0};function oe(k,T,ee){var ne,ue={},ce=null,me=null;if(T!=null)for(ne in T.ref!==void 0&&(me=T.ref),T.key!==void 0&&(ce=""+T.key),T)$.call(T,ne)&&!X.hasOwnProperty(ne)&&(ue[ne]=T[ne]);var fe=arguments.length-2;if(fe===1)ue.children=ee;else if(1<fe){for(var xe=Array(fe),tt=0;tt<fe;tt++)xe[tt]=arguments[tt+2];ue.children=xe}if(k&&k.defaultProps)for(ne in fe=k.defaultProps,fe)ue[ne]===void 0&&(ue[ne]=fe[ne]);return{$$typeof:c,type:k,key:ce,ref:me,props:ue,_owner:A.current}}function pe(k,T){return{$$typeof:c,type:k.type,key:T,ref:k.ref,props:k.props,_owner:k._owner}}function _e(k){return typeof k=="object"&&k!==null&&k.$$typeof===c}function lt(k){var T={"=":"=0",":":"=2"};return"$"+k.replace(/[=:]/g,function(ee){return T[ee]})}var gt=/\/+/g;function et(k,T){return typeof k=="object"&&k!==null&&k.key!=null?lt(""+k.key):T.toString(36)}function vt(k,T,ee,ne,ue){var ce=typeof k;(ce==="undefined"||ce==="boolean")&&(k=null);var me=!1;if(k===null)me=!0;else switch(ce){case"string":case"number":me=!0;break;case"object":switch(k.$$typeof){case c:case s:me=!0}}if(me)return me=k,ue=ue(me),k=ne===""?"."+et(me,0):ne,q(ue)?(ee="",k!=null&&(ee=k.replace(gt,"$&/")+"/"),vt(ue,T,ee,"",function(tt){return tt})):ue!=null&&(_e(ue)&&(ue=pe(ue,ee+(!ue.key||me&&me.key===ue.key?"":(""+ue.key).replace(gt,"$&/")+"/")+k)),T.push(ue)),1;if(me=0,ne=ne===""?".":ne+":",q(k))for(var fe=0;fe<k.length;fe++){ce=k[fe];var xe=ne+et(ce,fe);me+=vt(ce,T,ee,xe,ue)}else if(xe=P(k),typeof xe=="function")for(k=xe.call(k),fe=0;!(ce=k.next()).done;)ce=ce.value,xe=ne+et(ce,fe++),me+=vt(ce,T,ee,xe,ue);else if(ce==="object")throw T=String(k),Error("Objects are not valid as a React child (found: "+(T==="[object Object]"?"object with keys {"+Object.keys(k).join(", ")+"}":T)+"). If you meant to render a collection of children, use an array instead.");return me}function Pt(k,T,ee){if(k==null)return k;var ne=[],ue=0;return vt(k,ne,"","",function(ce){return T.call(ee,ce,ue++)}),ne}function We(k){if(k._status===-1){var T=k._result;T=T(),T.then(function(ee){(k._status===0||k._status===-1)&&(k._status=1,k._result=ee)},function(ee){(k._status===0||k._status===-1)&&(k._status=2,k._result=ee)}),k._status===-1&&(k._status=0,k._result=T)}if(k._status===1)return k._result.default;throw k._result}var je={current:null},z={transition:null},J={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:z,ReactCurrentOwner:A};function B(){throw Error("act(...) is not supported in production builds of React.")}return te.Children={map:Pt,forEach:function(k,T,ee){Pt(k,function(){T.apply(this,arguments)},ee)},count:function(k){var T=0;return Pt(k,function(){T++}),T},toArray:function(k){return Pt(k,function(T){return T})||[]},only:function(k){if(!_e(k))throw Error("React.Children.only expected to receive a single React element child.");return k}},te.Component=D,te.Fragment=i,te.Profiler=a,te.PureComponent=ae,te.StrictMode=l,te.Suspense=m,te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,te.act=B,te.cloneElement=function(k,T,ee){if(k==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+k+".");var ne=L({},k.props),ue=k.key,ce=k.ref,me=k._owner;if(T!=null){if(T.ref!==void 0&&(ce=T.ref,me=A.current),T.key!==void 0&&(ue=""+T.key),k.type&&k.type.defaultProps)var fe=k.type.defaultProps;for(xe in T)$.call(T,xe)&&!X.hasOwnProperty(xe)&&(ne[xe]=T[xe]===void 0&&fe!==void 0?fe[xe]:T[xe])}var xe=arguments.length-2;if(xe===1)ne.children=ee;else if(1<xe){fe=Array(xe);for(var tt=0;tt<xe;tt++)fe[tt]=arguments[tt+2];ne.children=fe}return{$$typeof:c,type:k.type,key:ue,ref:ce,props:ne,_owner:me}},te.createContext=function(k){return k={$$typeof:f,_currentValue:k,_currentValue2:k,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},k.Provider={$$typeof:d,_context:k},k.Consumer=k},te.createElement=oe,te.createFactory=function(k){var T=oe.bind(null,k);return T.type=k,T},te.createRef=function(){return{current:null}},te.forwardRef=function(k){return{$$typeof:p,render:k}},te.isValidElement=_e,te.lazy=function(k){return{$$typeof:y,_payload:{_status:-1,_result:k},_init:We}},te.memo=function(k,T){return{$$typeof:g,type:k,compare:T===void 0?null:T}},te.startTransition=function(k){var T=z.transition;z.transition={};try{k()}finally{z.transition=T}},te.unstable_act=B,te.useCallback=function(k,T){return je.current.useCallback(k,T)},te.useContext=function(k){return je.current.useContext(k)},te.useDebugValue=function(){},te.useDeferredValue=function(k){return je.current.useDeferredValue(k)},te.useEffect=function(k,T){return je.current.useEffect(k,T)},te.useId=function(){return je.current.useId()},te.useImperativeHandle=function(k,T,ee){return je.current.useImperativeHandle(k,T,ee)},te.useInsertionEffect=function(k,T){return je.current.useInsertionEffect(k,T)},te.useLayoutEffect=function(k,T){return je.current.useLayoutEffect(k,T)},te.useMemo=function(k,T){return je.current.useMemo(k,T)},te.useReducer=function(k,T,ee){return je.current.useReducer(k,T,ee)},te.useRef=function(k){return je.current.useRef(k)},te.useState=function(k){return je.current.useState(k)},te.useSyncExternalStore=function(k,T,ee){return je.current.useSyncExternalStore(k,T,ee)},te.useTransition=function(){return je.current.useTransition()},te.version="18.3.1",te}var Hc;function Bl(){return Hc||(Hc=1,Cl.exports=Wh()),Cl.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qc;function Gh(){if(qc)return es;qc=1;var c=Bl(),s=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(p,m,g){var y,E={},P=null,O=null;g!==void 0&&(P=""+g),m.key!==void 0&&(P=""+m.key),m.ref!==void 0&&(O=m.ref);for(y in m)l.call(m,y)&&!d.hasOwnProperty(y)&&(E[y]=m[y]);if(p&&p.defaultProps)for(y in m=p.defaultProps,m)E[y]===void 0&&(E[y]=m[y]);return{$$typeof:s,type:p,key:P,ref:O,props:E,_owner:a.current}}return es.Fragment=i,es.jsx=f,es.jsxs=f,es}var Wc;function Qh(){return Wc||(Wc=1,jl.exports=Gh()),jl.exports}var w=Qh(),se=Bl(),wi={},Pl={exports:{}},Xe={},Nl={exports:{}},Tl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gc;function Kh(){return Gc||(Gc=1,function(c){function s(z,J){var B=z.length;z.push(J);e:for(;0<B;){var k=B-1>>>1,T=z[k];if(0<a(T,J))z[k]=J,z[B]=T,B=k;else break e}}function i(z){return z.length===0?null:z[0]}function l(z){if(z.length===0)return null;var J=z[0],B=z.pop();if(B!==J){z[0]=B;e:for(var k=0,T=z.length,ee=T>>>1;k<ee;){var ne=2*(k+1)-1,ue=z[ne],ce=ne+1,me=z[ce];if(0>a(ue,B))ce<T&&0>a(me,ue)?(z[k]=me,z[ce]=B,k=ce):(z[k]=ue,z[ne]=B,k=ne);else if(ce<T&&0>a(me,B))z[k]=me,z[ce]=B,k=ce;else break e}}return J}function a(z,J){var B=z.sortIndex-J.sortIndex;return B!==0?B:z.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;c.unstable_now=function(){return d.now()}}else{var f=Date,p=f.now();c.unstable_now=function(){return f.now()-p}}var m=[],g=[],y=1,E=null,P=3,O=!1,L=!1,F=!1,D=typeof setTimeout=="function"?setTimeout:null,re=typeof clearTimeout=="function"?clearTimeout:null,ae=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function U(z){for(var J=i(g);J!==null;){if(J.callback===null)l(g);else if(J.startTime<=z)l(g),J.sortIndex=J.expirationTime,s(m,J);else break;J=i(g)}}function q(z){if(F=!1,U(z),!L)if(i(m)!==null)L=!0,We($);else{var J=i(g);J!==null&&je(q,J.startTime-z)}}function $(z,J){L=!1,F&&(F=!1,re(oe),oe=-1),O=!0;var B=P;try{for(U(J),E=i(m);E!==null&&(!(E.expirationTime>J)||z&&!lt());){var k=E.callback;if(typeof k=="function"){E.callback=null,P=E.priorityLevel;var T=k(E.expirationTime<=J);J=c.unstable_now(),typeof T=="function"?E.callback=T:E===i(m)&&l(m),U(J)}else l(m);E=i(m)}if(E!==null)var ee=!0;else{var ne=i(g);ne!==null&&je(q,ne.startTime-J),ee=!1}return ee}finally{E=null,P=B,O=!1}}var A=!1,X=null,oe=-1,pe=5,_e=-1;function lt(){return!(c.unstable_now()-_e<pe)}function gt(){if(X!==null){var z=c.unstable_now();_e=z;var J=!0;try{J=X(!0,z)}finally{J?et():(A=!1,X=null)}}else A=!1}var et;if(typeof ae=="function")et=function(){ae(gt)};else if(typeof MessageChannel<"u"){var vt=new MessageChannel,Pt=vt.port2;vt.port1.onmessage=gt,et=function(){Pt.postMessage(null)}}else et=function(){D(gt,0)};function We(z){X=z,A||(A=!0,et())}function je(z,J){oe=D(function(){z(c.unstable_now())},J)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(z){z.callback=null},c.unstable_continueExecution=function(){L||O||(L=!0,We($))},c.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):pe=0<z?Math.floor(1e3/z):5},c.unstable_getCurrentPriorityLevel=function(){return P},c.unstable_getFirstCallbackNode=function(){return i(m)},c.unstable_next=function(z){switch(P){case 1:case 2:case 3:var J=3;break;default:J=P}var B=P;P=J;try{return z()}finally{P=B}},c.unstable_pauseExecution=function(){},c.unstable_requestPaint=function(){},c.unstable_runWithPriority=function(z,J){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var B=P;P=z;try{return J()}finally{P=B}},c.unstable_scheduleCallback=function(z,J,B){var k=c.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?k+B:k):B=k,z){case 1:var T=-1;break;case 2:T=250;break;case 5:T=**********;break;case 4:T=1e4;break;default:T=5e3}return T=B+T,z={id:y++,callback:J,priorityLevel:z,startTime:B,expirationTime:T,sortIndex:-1},B>k?(z.sortIndex=B,s(g,z),i(m)===null&&z===i(g)&&(F?(re(oe),oe=-1):F=!0,je(q,B-k))):(z.sortIndex=T,s(m,z),L||O||(L=!0,We($))),z},c.unstable_shouldYield=lt,c.unstable_wrapCallback=function(z){var J=P;return function(){var B=P;P=J;try{return z.apply(this,arguments)}finally{P=B}}}}(Tl)),Tl}var Qc;function Jh(){return Qc||(Qc=1,Nl.exports=Kh()),Nl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kc;function Yh(){if(Kc)return Xe;Kc=1;var c=Bl(),s=Jh();function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,a={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(a[e]=t,e=0;e<t.length;e++)l.add(t[e])}var p=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),m=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,y={},E={};function P(e){return m.call(E,e)?!0:m.call(y,e)?!1:g.test(e)?E[e]=!0:(y[e]=!0,!1)}function O(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function L(e,t,r,n){if(t===null||typeof t>"u"||O(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function F(e,t,r,n,o,u,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=o,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=u,this.removeEmptyString=h}var D={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){D[e]=new F(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];D[t]=new F(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){D[e]=new F(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){D[e]=new F(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){D[e]=new F(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){D[e]=new F(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){D[e]=new F(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){D[e]=new F(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){D[e]=new F(e,5,!1,e.toLowerCase(),null,!1,!1)});var re=/[\-:]([a-z])/g;function ae(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(re,ae);D[t]=new F(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(re,ae);D[t]=new F(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(re,ae);D[t]=new F(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){D[e]=new F(e,1,!1,e.toLowerCase(),null,!1,!1)}),D.xlinkHref=new F("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){D[e]=new F(e,1,!1,e.toLowerCase(),null,!0,!0)});function U(e,t,r,n){var o=D.hasOwnProperty(t)?D[t]:null;(o!==null?o.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(L(t,r,o,n)&&(r=null),n||o===null?P(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):o.mustUseProperty?e[o.propertyName]=r===null?o.type===3?!1:"":r:(t=o.attributeName,n=o.attributeNamespace,r===null?e.removeAttribute(t):(o=o.type,r=o===3||o===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var q=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$=Symbol.for("react.element"),A=Symbol.for("react.portal"),X=Symbol.for("react.fragment"),oe=Symbol.for("react.strict_mode"),pe=Symbol.for("react.profiler"),_e=Symbol.for("react.provider"),lt=Symbol.for("react.context"),gt=Symbol.for("react.forward_ref"),et=Symbol.for("react.suspense"),vt=Symbol.for("react.suspense_list"),Pt=Symbol.for("react.memo"),We=Symbol.for("react.lazy"),je=Symbol.for("react.offscreen"),z=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=z&&e[z]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,k;function T(e){if(k===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);k=t&&t[1]||""}return`
`+k+e}var ee=!1;function ne(e,t){if(!e||ee)return"";ee=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(C){var n=C}Reflect.construct(e,[],t)}else{try{t.call()}catch(C){n=C}e.call(t.prototype)}else{try{throw Error()}catch(C){n=C}e()}}catch(C){if(C&&n&&typeof C.stack=="string"){for(var o=C.stack.split(`
`),u=n.stack.split(`
`),h=o.length-1,v=u.length-1;1<=h&&0<=v&&o[h]!==u[v];)v--;for(;1<=h&&0<=v;h--,v--)if(o[h]!==u[v]){if(h!==1||v!==1)do if(h--,v--,0>v||o[h]!==u[v]){var _=`
`+o[h].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=h&&0<=v);break}}}finally{ee=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?T(e):""}function ue(e){switch(e.tag){case 5:return T(e.type);case 16:return T("Lazy");case 13:return T("Suspense");case 19:return T("SuspenseList");case 0:case 2:case 15:return e=ne(e.type,!1),e;case 11:return e=ne(e.type.render,!1),e;case 1:return e=ne(e.type,!0),e;default:return""}}function ce(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case X:return"Fragment";case A:return"Portal";case pe:return"Profiler";case oe:return"StrictMode";case et:return"Suspense";case vt:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case lt:return(e.displayName||"Context")+".Consumer";case _e:return(e._context.displayName||"Context")+".Provider";case gt:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Pt:return t=e.displayName||null,t!==null?t:ce(e.type)||"Memo";case We:t=e._payload,e=e._init;try{return ce(e(t))}catch{}}return null}function me(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ce(t);case 8:return t===oe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tt(e){var t=xe(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var o=r.get,u=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(h){n=""+h,u.call(this,h)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(h){n=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function as(e){e._valueTracker||(e._valueTracker=tt(e))}function Kl(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=xe(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function us(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ri(e,t){var r=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Jl(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=fe(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Yl(e,t){t=t.checked,t!=null&&U(e,"checked",t,!1)}function Oi(e,t){Yl(e,t);var r=fe(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$i(e,t.type,r):t.hasOwnProperty("defaultValue")&&$i(e,t.type,fe(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Xl(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function $i(e,t,r){(t!=="number"||us(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var mn=Array.isArray;function br(e,t,r,n){if(e=e.options,t){t={};for(var o=0;o<r.length;o++)t["$"+r[o]]=!0;for(r=0;r<e.length;r++)o=t.hasOwnProperty("$"+e[r].value),e[r].selected!==o&&(e[r].selected=o),o&&n&&(e[r].defaultSelected=!0)}else{for(r=""+fe(r),t=null,o=0;o<e.length;o++){if(e[o].value===r){e[o].selected=!0,n&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ii(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(i(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Zl(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(i(92));if(mn(r)){if(1<r.length)throw Error(i(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:fe(r)}}function ea(e,t){var r=fe(t.value),n=fe(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function ta(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ra(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Li(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ra(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var cs,na=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,o){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(cs=cs||document.createElement("div"),cs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=cs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function gn(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var vn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},qd=["Webkit","ms","Moz","O"];Object.keys(vn).forEach(function(e){qd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),vn[t]=vn[e]})});function sa(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||vn.hasOwnProperty(e)&&vn[e]?(""+t).trim():t+"px"}function ia(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,o=sa(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,o):e[r]=o}}var Wd=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ai(e,t){if(t){if(Wd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(i(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(t.style!=null&&typeof t.style!="object")throw Error(i(62))}}function Di(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zi=null;function Ui(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Mi=null,Rr=null,Or=null;function oa(e){if(e=Un(e)){if(typeof Mi!="function")throw Error(i(280));var t=e.stateNode;t&&(t=$s(t),Mi(e.stateNode,e.type,t))}}function la(e){Rr?Or?Or.push(e):Or=[e]:Rr=e}function aa(){if(Rr){var e=Rr,t=Or;if(Or=Rr=null,oa(e),t)for(e=0;e<t.length;e++)oa(t[e])}}function ua(e,t){return e(t)}function ca(){}var Fi=!1;function da(e,t,r){if(Fi)return e(t,r);Fi=!0;try{return ua(e,t,r)}finally{Fi=!1,(Rr!==null||Or!==null)&&(ca(),aa())}}function yn(e,t){var r=e.stateNode;if(r===null)return null;var n=$s(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(i(231,t,typeof r));return r}var Vi=!1;if(p)try{var wn={};Object.defineProperty(wn,"passive",{get:function(){Vi=!0}}),window.addEventListener("test",wn,wn),window.removeEventListener("test",wn,wn)}catch{Vi=!1}function Gd(e,t,r,n,o,u,h,v,_){var C=Array.prototype.slice.call(arguments,3);try{t.apply(r,C)}catch(b){this.onError(b)}}var _n=!1,ds=null,fs=!1,Bi=null,Qd={onError:function(e){_n=!0,ds=e}};function Kd(e,t,r,n,o,u,h,v,_){_n=!1,ds=null,Gd.apply(Qd,arguments)}function Jd(e,t,r,n,o,u,h,v,_){if(Kd.apply(this,arguments),_n){if(_n){var C=ds;_n=!1,ds=null}else throw Error(i(198));fs||(fs=!0,Bi=C)}}function fr(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function fa(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ha(e){if(fr(e)!==e)throw Error(i(188))}function Yd(e){var t=e.alternate;if(!t){if(t=fr(e),t===null)throw Error(i(188));return t!==e?null:e}for(var r=e,n=t;;){var o=r.return;if(o===null)break;var u=o.alternate;if(u===null){if(n=o.return,n!==null){r=n;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===r)return ha(o),e;if(u===n)return ha(o),t;u=u.sibling}throw Error(i(188))}if(r.return!==n.return)r=o,n=u;else{for(var h=!1,v=o.child;v;){if(v===r){h=!0,r=o,n=u;break}if(v===n){h=!0,n=o,r=u;break}v=v.sibling}if(!h){for(v=u.child;v;){if(v===r){h=!0,r=u,n=o;break}if(v===n){h=!0,n=u,r=o;break}v=v.sibling}if(!h)throw Error(i(189))}}if(r.alternate!==n)throw Error(i(190))}if(r.tag!==3)throw Error(i(188));return r.stateNode.current===r?e:t}function pa(e){return e=Yd(e),e!==null?ma(e):null}function ma(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ma(e);if(t!==null)return t;e=e.sibling}return null}var ga=s.unstable_scheduleCallback,va=s.unstable_cancelCallback,Xd=s.unstable_shouldYield,Zd=s.unstable_requestPaint,Pe=s.unstable_now,ef=s.unstable_getCurrentPriorityLevel,Hi=s.unstable_ImmediatePriority,ya=s.unstable_UserBlockingPriority,hs=s.unstable_NormalPriority,tf=s.unstable_LowPriority,wa=s.unstable_IdlePriority,ps=null,Nt=null;function rf(e){if(Nt&&typeof Nt.onCommitFiberRoot=="function")try{Nt.onCommitFiberRoot(ps,e,void 0,(e.current.flags&128)===128)}catch{}}var yt=Math.clz32?Math.clz32:of,nf=Math.log,sf=Math.LN2;function of(e){return e>>>=0,e===0?32:31-(nf(e)/sf|0)|0}var ms=64,gs=4194304;function xn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vs(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,o=e.suspendedLanes,u=e.pingedLanes,h=r&268435455;if(h!==0){var v=h&~o;v!==0?n=xn(v):(u&=h,u!==0&&(n=xn(u)))}else h=r&~o,h!==0?n=xn(h):u!==0&&(n=xn(u));if(n===0)return 0;if(t!==0&&t!==n&&(t&o)===0&&(o=n&-n,u=t&-t,o>=u||o===16&&(u&4194240)!==0))return t;if((n&4)!==0&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-yt(t),o=1<<r,n|=e[r],t&=~o;return n}function lf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function af(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,o=e.expirationTimes,u=e.pendingLanes;0<u;){var h=31-yt(u),v=1<<h,_=o[h];_===-1?((v&r)===0||(v&n)!==0)&&(o[h]=lf(v,t)):_<=t&&(e.expiredLanes|=v),u&=~v}}function qi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function _a(){var e=ms;return ms<<=1,(ms&4194240)===0&&(ms=64),e}function Wi(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function kn(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-yt(t),e[t]=r}function uf(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var o=31-yt(r),u=1<<o;t[o]=0,n[o]=-1,e[o]=-1,r&=~u}}function Gi(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-yt(r),o=1<<n;o&t|e[n]&t&&(e[n]|=t),r&=~o}}var he=0;function xa(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var ka,Qi,Sa,Ea,ja,Ki=!1,ys=[],Bt=null,Ht=null,qt=null,Sn=new Map,En=new Map,Wt=[],cf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ca(e,t){switch(e){case"focusin":case"focusout":Bt=null;break;case"dragenter":case"dragleave":Ht=null;break;case"mouseover":case"mouseout":qt=null;break;case"pointerover":case"pointerout":Sn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":En.delete(t.pointerId)}}function jn(e,t,r,n,o,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:u,targetContainers:[o]},t!==null&&(t=Un(t),t!==null&&Qi(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function df(e,t,r,n,o){switch(t){case"focusin":return Bt=jn(Bt,e,t,r,n,o),!0;case"dragenter":return Ht=jn(Ht,e,t,r,n,o),!0;case"mouseover":return qt=jn(qt,e,t,r,n,o),!0;case"pointerover":var u=o.pointerId;return Sn.set(u,jn(Sn.get(u)||null,e,t,r,n,o)),!0;case"gotpointercapture":return u=o.pointerId,En.set(u,jn(En.get(u)||null,e,t,r,n,o)),!0}return!1}function Pa(e){var t=hr(e.target);if(t!==null){var r=fr(t);if(r!==null){if(t=r.tag,t===13){if(t=fa(r),t!==null){e.blockedOn=t,ja(e.priority,function(){Sa(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ws(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Yi(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);zi=n,r.target.dispatchEvent(n),zi=null}else return t=Un(r),t!==null&&Qi(t),e.blockedOn=r,!1;t.shift()}return!0}function Na(e,t,r){ws(e)&&r.delete(t)}function ff(){Ki=!1,Bt!==null&&ws(Bt)&&(Bt=null),Ht!==null&&ws(Ht)&&(Ht=null),qt!==null&&ws(qt)&&(qt=null),Sn.forEach(Na),En.forEach(Na)}function Cn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ki||(Ki=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,ff)))}function Pn(e){function t(o){return Cn(o,e)}if(0<ys.length){Cn(ys[0],e);for(var r=1;r<ys.length;r++){var n=ys[r];n.blockedOn===e&&(n.blockedOn=null)}}for(Bt!==null&&Cn(Bt,e),Ht!==null&&Cn(Ht,e),qt!==null&&Cn(qt,e),Sn.forEach(t),En.forEach(t),r=0;r<Wt.length;r++)n=Wt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<Wt.length&&(r=Wt[0],r.blockedOn===null);)Pa(r),r.blockedOn===null&&Wt.shift()}var $r=q.ReactCurrentBatchConfig,_s=!0;function hf(e,t,r,n){var o=he,u=$r.transition;$r.transition=null;try{he=1,Ji(e,t,r,n)}finally{he=o,$r.transition=u}}function pf(e,t,r,n){var o=he,u=$r.transition;$r.transition=null;try{he=4,Ji(e,t,r,n)}finally{he=o,$r.transition=u}}function Ji(e,t,r,n){if(_s){var o=Yi(e,t,r,n);if(o===null)mo(e,t,n,xs,r),Ca(e,n);else if(df(o,e,t,r,n))n.stopPropagation();else if(Ca(e,n),t&4&&-1<cf.indexOf(e)){for(;o!==null;){var u=Un(o);if(u!==null&&ka(u),u=Yi(e,t,r,n),u===null&&mo(e,t,n,xs,r),u===o)break;o=u}o!==null&&n.stopPropagation()}else mo(e,t,n,null,r)}}var xs=null;function Yi(e,t,r,n){if(xs=null,e=Ui(n),e=hr(e),e!==null)if(t=fr(e),t===null)e=null;else if(r=t.tag,r===13){if(e=fa(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return xs=e,null}function Ta(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ef()){case Hi:return 1;case ya:return 4;case hs:case tf:return 16;case wa:return 536870912;default:return 16}default:return 16}}var Gt=null,Xi=null,ks=null;function ba(){if(ks)return ks;var e,t=Xi,r=t.length,n,o="value"in Gt?Gt.value:Gt.textContent,u=o.length;for(e=0;e<r&&t[e]===o[e];e++);var h=r-e;for(n=1;n<=h&&t[r-n]===o[u-n];n++);return ks=o.slice(e,1<n?1-n:void 0)}function Ss(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Es(){return!0}function Ra(){return!1}function rt(e){function t(r,n,o,u,h){this._reactName=r,this._targetInst=o,this.type=n,this.nativeEvent=u,this.target=h,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(r=e[v],this[v]=r?r(u):u[v]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Es:Ra,this.isPropagationStopped=Ra,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=Es)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=Es)},persist:function(){},isPersistent:Es}),t}var Ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zi=rt(Ir),Nn=B({},Ir,{view:0,detail:0}),mf=rt(Nn),eo,to,Tn,js=B({},Nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:no,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Tn&&(Tn&&e.type==="mousemove"?(eo=e.screenX-Tn.screenX,to=e.screenY-Tn.screenY):to=eo=0,Tn=e),eo)},movementY:function(e){return"movementY"in e?e.movementY:to}}),Oa=rt(js),gf=B({},js,{dataTransfer:0}),vf=rt(gf),yf=B({},Nn,{relatedTarget:0}),ro=rt(yf),wf=B({},Ir,{animationName:0,elapsedTime:0,pseudoElement:0}),_f=rt(wf),xf=B({},Ir,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),kf=rt(xf),Sf=B({},Ir,{data:0}),$a=rt(Sf),Ef={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Cf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Pf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Cf[e])?!!t[e]:!1}function no(){return Pf}var Nf=B({},Nn,{key:function(e){if(e.key){var t=Ef[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ss(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:no,charCode:function(e){return e.type==="keypress"?Ss(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ss(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Tf=rt(Nf),bf=B({},js,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ia=rt(bf),Rf=B({},Nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:no}),Of=rt(Rf),$f=B({},Ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),If=rt($f),Lf=B({},js,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Af=rt(Lf),Df=[9,13,27,32],so=p&&"CompositionEvent"in window,bn=null;p&&"documentMode"in document&&(bn=document.documentMode);var zf=p&&"TextEvent"in window&&!bn,La=p&&(!so||bn&&8<bn&&11>=bn),Aa=" ",Da=!1;function za(e,t){switch(e){case"keyup":return Df.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ua(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Lr=!1;function Uf(e,t){switch(e){case"compositionend":return Ua(t);case"keypress":return t.which!==32?null:(Da=!0,Aa);case"textInput":return e=t.data,e===Aa&&Da?null:e;default:return null}}function Mf(e,t){if(Lr)return e==="compositionend"||!so&&za(e,t)?(e=ba(),ks=Xi=Gt=null,Lr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return La&&t.locale!=="ko"?null:t.data;default:return null}}var Ff={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ma(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Ff[e.type]:t==="textarea"}function Fa(e,t,r,n){la(n),t=bs(t,"onChange"),0<t.length&&(r=new Zi("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Rn=null,On=null;function Vf(e){iu(e,0)}function Cs(e){var t=Mr(e);if(Kl(t))return e}function Bf(e,t){if(e==="change")return t}var Va=!1;if(p){var io;if(p){var oo="oninput"in document;if(!oo){var Ba=document.createElement("div");Ba.setAttribute("oninput","return;"),oo=typeof Ba.oninput=="function"}io=oo}else io=!1;Va=io&&(!document.documentMode||9<document.documentMode)}function Ha(){Rn&&(Rn.detachEvent("onpropertychange",qa),On=Rn=null)}function qa(e){if(e.propertyName==="value"&&Cs(On)){var t=[];Fa(t,On,e,Ui(e)),da(Vf,t)}}function Hf(e,t,r){e==="focusin"?(Ha(),Rn=t,On=r,Rn.attachEvent("onpropertychange",qa)):e==="focusout"&&Ha()}function qf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Cs(On)}function Wf(e,t){if(e==="click")return Cs(t)}function Gf(e,t){if(e==="input"||e==="change")return Cs(t)}function Qf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:Qf;function $n(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var o=r[n];if(!m.call(t,o)||!wt(e[o],t[o]))return!1}return!0}function Wa(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ga(e,t){var r=Wa(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Wa(r)}}function Qa(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Qa(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ka(){for(var e=window,t=us();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=us(e.document)}return t}function lo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Kf(e){var t=Ka(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&Qa(r.ownerDocument.documentElement,r)){if(n!==null&&lo(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=r.textContent.length,u=Math.min(n.start,o);n=n.end===void 0?u:Math.min(n.end,o),!e.extend&&u>n&&(o=n,n=u,u=o),o=Ga(r,u);var h=Ga(r,n);o&&h&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),u>n?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Jf=p&&"documentMode"in document&&11>=document.documentMode,Ar=null,ao=null,In=null,uo=!1;function Ja(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;uo||Ar==null||Ar!==us(n)||(n=Ar,"selectionStart"in n&&lo(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),In&&$n(In,n)||(In=n,n=bs(ao,"onSelect"),0<n.length&&(t=new Zi("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=Ar)))}function Ps(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var Dr={animationend:Ps("Animation","AnimationEnd"),animationiteration:Ps("Animation","AnimationIteration"),animationstart:Ps("Animation","AnimationStart"),transitionend:Ps("Transition","TransitionEnd")},co={},Ya={};p&&(Ya=document.createElement("div").style,"AnimationEvent"in window||(delete Dr.animationend.animation,delete Dr.animationiteration.animation,delete Dr.animationstart.animation),"TransitionEvent"in window||delete Dr.transitionend.transition);function Ns(e){if(co[e])return co[e];if(!Dr[e])return e;var t=Dr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in Ya)return co[e]=t[r];return e}var Xa=Ns("animationend"),Za=Ns("animationiteration"),eu=Ns("animationstart"),tu=Ns("transitionend"),ru=new Map,nu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Qt(e,t){ru.set(e,t),d(t,[e])}for(var fo=0;fo<nu.length;fo++){var ho=nu[fo],Yf=ho.toLowerCase(),Xf=ho[0].toUpperCase()+ho.slice(1);Qt(Yf,"on"+Xf)}Qt(Xa,"onAnimationEnd"),Qt(Za,"onAnimationIteration"),Qt(eu,"onAnimationStart"),Qt("dblclick","onDoubleClick"),Qt("focusin","onFocus"),Qt("focusout","onBlur"),Qt(tu,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ln="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Zf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ln));function su(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,Jd(n,t,void 0,e),e.currentTarget=null}function iu(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],o=n.event;n=n.listeners;e:{var u=void 0;if(t)for(var h=n.length-1;0<=h;h--){var v=n[h],_=v.instance,C=v.currentTarget;if(v=v.listener,_!==u&&o.isPropagationStopped())break e;su(o,v,C),u=_}else for(h=0;h<n.length;h++){if(v=n[h],_=v.instance,C=v.currentTarget,v=v.listener,_!==u&&o.isPropagationStopped())break e;su(o,v,C),u=_}}}if(fs)throw e=Bi,fs=!1,Bi=null,e}function ye(e,t){var r=t[xo];r===void 0&&(r=t[xo]=new Set);var n=e+"__bubble";r.has(n)||(ou(t,e,2,!1),r.add(n))}function po(e,t,r){var n=0;t&&(n|=4),ou(r,e,n,t)}var Ts="_reactListening"+Math.random().toString(36).slice(2);function An(e){if(!e[Ts]){e[Ts]=!0,l.forEach(function(r){r!=="selectionchange"&&(Zf.has(r)||po(r,!1,e),po(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ts]||(t[Ts]=!0,po("selectionchange",!1,t))}}function ou(e,t,r,n){switch(Ta(t)){case 1:var o=hf;break;case 4:o=pf;break;default:o=Ji}r=o.bind(null,t,r,e),o=void 0,!Vi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),n?o!==void 0?e.addEventListener(t,r,{capture:!0,passive:o}):e.addEventListener(t,r,!0):o!==void 0?e.addEventListener(t,r,{passive:o}):e.addEventListener(t,r,!1)}function mo(e,t,r,n,o){var u=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var h=n.tag;if(h===3||h===4){var v=n.stateNode.containerInfo;if(v===o||v.nodeType===8&&v.parentNode===o)break;if(h===4)for(h=n.return;h!==null;){var _=h.tag;if((_===3||_===4)&&(_=h.stateNode.containerInfo,_===o||_.nodeType===8&&_.parentNode===o))return;h=h.return}for(;v!==null;){if(h=hr(v),h===null)return;if(_=h.tag,_===5||_===6){n=u=h;continue e}v=v.parentNode}}n=n.return}da(function(){var C=u,b=Ui(r),R=[];e:{var N=ru.get(e);if(N!==void 0){var M=Zi,H=e;switch(e){case"keypress":if(Ss(r)===0)break e;case"keydown":case"keyup":M=Tf;break;case"focusin":H="focus",M=ro;break;case"focusout":H="blur",M=ro;break;case"beforeblur":case"afterblur":M=ro;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=Oa;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=vf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=Of;break;case Xa:case Za:case eu:M=_f;break;case tu:M=If;break;case"scroll":M=mf;break;case"wheel":M=Af;break;case"copy":case"cut":case"paste":M=kf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=Ia}var W=(t&4)!==0,Ne=!W&&e==="scroll",S=W?N!==null?N+"Capture":null:N;W=[];for(var x=C,j;x!==null;){j=x;var I=j.stateNode;if(j.tag===5&&I!==null&&(j=I,S!==null&&(I=yn(x,S),I!=null&&W.push(Dn(x,I,j)))),Ne)break;x=x.return}0<W.length&&(N=new M(N,H,null,r,b),R.push({event:N,listeners:W}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",N&&r!==zi&&(H=r.relatedTarget||r.fromElement)&&(hr(H)||H[It]))break e;if((M||N)&&(N=b.window===b?b:(N=b.ownerDocument)?N.defaultView||N.parentWindow:window,M?(H=r.relatedTarget||r.toElement,M=C,H=H?hr(H):null,H!==null&&(Ne=fr(H),H!==Ne||H.tag!==5&&H.tag!==6)&&(H=null)):(M=null,H=C),M!==H)){if(W=Oa,I="onMouseLeave",S="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(W=Ia,I="onPointerLeave",S="onPointerEnter",x="pointer"),Ne=M==null?N:Mr(M),j=H==null?N:Mr(H),N=new W(I,x+"leave",M,r,b),N.target=Ne,N.relatedTarget=j,I=null,hr(b)===C&&(W=new W(S,x+"enter",H,r,b),W.target=j,W.relatedTarget=Ne,I=W),Ne=I,M&&H)t:{for(W=M,S=H,x=0,j=W;j;j=zr(j))x++;for(j=0,I=S;I;I=zr(I))j++;for(;0<x-j;)W=zr(W),x--;for(;0<j-x;)S=zr(S),j--;for(;x--;){if(W===S||S!==null&&W===S.alternate)break t;W=zr(W),S=zr(S)}W=null}else W=null;M!==null&&lu(R,N,M,W,!1),H!==null&&Ne!==null&&lu(R,Ne,H,W,!0)}}e:{if(N=C?Mr(C):window,M=N.nodeName&&N.nodeName.toLowerCase(),M==="select"||M==="input"&&N.type==="file")var G=Bf;else if(Ma(N))if(Va)G=Gf;else{G=qf;var Q=Hf}else(M=N.nodeName)&&M.toLowerCase()==="input"&&(N.type==="checkbox"||N.type==="radio")&&(G=Wf);if(G&&(G=G(e,C))){Fa(R,G,r,b);break e}Q&&Q(e,N,C),e==="focusout"&&(Q=N._wrapperState)&&Q.controlled&&N.type==="number"&&$i(N,"number",N.value)}switch(Q=C?Mr(C):window,e){case"focusin":(Ma(Q)||Q.contentEditable==="true")&&(Ar=Q,ao=C,In=null);break;case"focusout":In=ao=Ar=null;break;case"mousedown":uo=!0;break;case"contextmenu":case"mouseup":case"dragend":uo=!1,Ja(R,r,b);break;case"selectionchange":if(Jf)break;case"keydown":case"keyup":Ja(R,r,b)}var K;if(so)e:{switch(e){case"compositionstart":var Y="onCompositionStart";break e;case"compositionend":Y="onCompositionEnd";break e;case"compositionupdate":Y="onCompositionUpdate";break e}Y=void 0}else Lr?za(e,r)&&(Y="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(Y="onCompositionStart");Y&&(La&&r.locale!=="ko"&&(Lr||Y!=="onCompositionStart"?Y==="onCompositionEnd"&&Lr&&(K=ba()):(Gt=b,Xi="value"in Gt?Gt.value:Gt.textContent,Lr=!0)),Q=bs(C,Y),0<Q.length&&(Y=new $a(Y,e,null,r,b),R.push({event:Y,listeners:Q}),K?Y.data=K:(K=Ua(r),K!==null&&(Y.data=K)))),(K=zf?Uf(e,r):Mf(e,r))&&(C=bs(C,"onBeforeInput"),0<C.length&&(b=new $a("onBeforeInput","beforeinput",null,r,b),R.push({event:b,listeners:C}),b.data=K))}iu(R,t)})}function Dn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function bs(e,t){for(var r=t+"Capture",n=[];e!==null;){var o=e,u=o.stateNode;o.tag===5&&u!==null&&(o=u,u=yn(e,r),u!=null&&n.unshift(Dn(e,u,o)),u=yn(e,t),u!=null&&n.push(Dn(e,u,o))),e=e.return}return n}function zr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function lu(e,t,r,n,o){for(var u=t._reactName,h=[];r!==null&&r!==n;){var v=r,_=v.alternate,C=v.stateNode;if(_!==null&&_===n)break;v.tag===5&&C!==null&&(v=C,o?(_=yn(r,u),_!=null&&h.unshift(Dn(r,_,v))):o||(_=yn(r,u),_!=null&&h.push(Dn(r,_,v)))),r=r.return}h.length!==0&&e.push({event:t,listeners:h})}var eh=/\r\n?/g,th=/\u0000|\uFFFD/g;function au(e){return(typeof e=="string"?e:""+e).replace(eh,`
`).replace(th,"")}function Rs(e,t,r){if(t=au(t),au(e)!==t&&r)throw Error(i(425))}function Os(){}var go=null,vo=null;function yo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var wo=typeof setTimeout=="function"?setTimeout:void 0,rh=typeof clearTimeout=="function"?clearTimeout:void 0,uu=typeof Promise=="function"?Promise:void 0,nh=typeof queueMicrotask=="function"?queueMicrotask:typeof uu<"u"?function(e){return uu.resolve(null).then(e).catch(sh)}:wo;function sh(e){setTimeout(function(){throw e})}function _o(e,t){var r=t,n=0;do{var o=r.nextSibling;if(e.removeChild(r),o&&o.nodeType===8)if(r=o.data,r==="/$"){if(n===0){e.removeChild(o),Pn(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=o}while(r);Pn(t)}function Kt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function cu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Ur=Math.random().toString(36).slice(2),Tt="__reactFiber$"+Ur,zn="__reactProps$"+Ur,It="__reactContainer$"+Ur,xo="__reactEvents$"+Ur,ih="__reactListeners$"+Ur,oh="__reactHandles$"+Ur;function hr(e){var t=e[Tt];if(t)return t;for(var r=e.parentNode;r;){if(t=r[It]||r[Tt]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=cu(e);e!==null;){if(r=e[Tt])return r;e=cu(e)}return t}e=r,r=e.parentNode}return null}function Un(e){return e=e[Tt]||e[It],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function $s(e){return e[zn]||null}var ko=[],Fr=-1;function Jt(e){return{current:e}}function we(e){0>Fr||(e.current=ko[Fr],ko[Fr]=null,Fr--)}function ge(e,t){Fr++,ko[Fr]=e.current,e.current=t}var Yt={},Me=Jt(Yt),Ge=Jt(!1),pr=Yt;function Vr(e,t){var r=e.type.contextTypes;if(!r)return Yt;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var o={},u;for(u in r)o[u]=t[u];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Qe(e){return e=e.childContextTypes,e!=null}function Is(){we(Ge),we(Me)}function du(e,t,r){if(Me.current!==Yt)throw Error(i(168));ge(Me,t),ge(Ge,r)}function fu(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var o in n)if(!(o in t))throw Error(i(108,me(e)||"Unknown",o));return B({},r,n)}function Ls(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Yt,pr=Me.current,ge(Me,e),ge(Ge,Ge.current),!0}function hu(e,t,r){var n=e.stateNode;if(!n)throw Error(i(169));r?(e=fu(e,t,pr),n.__reactInternalMemoizedMergedChildContext=e,we(Ge),we(Me),ge(Me,e)):we(Ge),ge(Ge,r)}var Lt=null,As=!1,So=!1;function pu(e){Lt===null?Lt=[e]:Lt.push(e)}function lh(e){As=!0,pu(e)}function Xt(){if(!So&&Lt!==null){So=!0;var e=0,t=he;try{var r=Lt;for(he=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Lt=null,As=!1}catch(o){throw Lt!==null&&(Lt=Lt.slice(e+1)),ga(Hi,Xt),o}finally{he=t,So=!1}}return null}var Br=[],Hr=0,Ds=null,zs=0,at=[],ut=0,mr=null,At=1,Dt="";function gr(e,t){Br[Hr++]=zs,Br[Hr++]=Ds,Ds=e,zs=t}function mu(e,t,r){at[ut++]=At,at[ut++]=Dt,at[ut++]=mr,mr=e;var n=At;e=Dt;var o=32-yt(n)-1;n&=~(1<<o),r+=1;var u=32-yt(t)+o;if(30<u){var h=o-o%5;u=(n&(1<<h)-1).toString(32),n>>=h,o-=h,At=1<<32-yt(t)+o|r<<o|n,Dt=u+e}else At=1<<u|r<<o|n,Dt=e}function Eo(e){e.return!==null&&(gr(e,1),mu(e,1,0))}function jo(e){for(;e===Ds;)Ds=Br[--Hr],Br[Hr]=null,zs=Br[--Hr],Br[Hr]=null;for(;e===mr;)mr=at[--ut],at[ut]=null,Dt=at[--ut],at[ut]=null,At=at[--ut],at[ut]=null}var nt=null,st=null,ke=!1,_t=null;function gu(e,t){var r=ht(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function vu(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,st=Kt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,st=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=mr!==null?{id:At,overflow:Dt}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=ht(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,nt=e,st=null,!0):!1;default:return!1}}function Co(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Po(e){if(ke){var t=st;if(t){var r=t;if(!vu(e,t)){if(Co(e))throw Error(i(418));t=Kt(r.nextSibling);var n=nt;t&&vu(e,t)?gu(n,r):(e.flags=e.flags&-4097|2,ke=!1,nt=e)}}else{if(Co(e))throw Error(i(418));e.flags=e.flags&-4097|2,ke=!1,nt=e}}}function yu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function Us(e){if(e!==nt)return!1;if(!ke)return yu(e),ke=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!yo(e.type,e.memoizedProps)),t&&(t=st)){if(Co(e))throw wu(),Error(i(418));for(;t;)gu(e,t),t=Kt(t.nextSibling)}if(yu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){st=Kt(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}st=null}}else st=nt?Kt(e.stateNode.nextSibling):null;return!0}function wu(){for(var e=st;e;)e=Kt(e.nextSibling)}function qr(){st=nt=null,ke=!1}function No(e){_t===null?_t=[e]:_t.push(e)}var ah=q.ReactCurrentBatchConfig;function Mn(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(i(309));var n=r.stateNode}if(!n)throw Error(i(147,e));var o=n,u=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===u?t.ref:(t=function(h){var v=o.refs;h===null?delete v[u]:v[u]=h},t._stringRef=u,t)}if(typeof e!="string")throw Error(i(284));if(!r._owner)throw Error(i(290,e))}return e}function Ms(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _u(e){var t=e._init;return t(e._payload)}function xu(e){function t(S,x){if(e){var j=S.deletions;j===null?(S.deletions=[x],S.flags|=16):j.push(x)}}function r(S,x){if(!e)return null;for(;x!==null;)t(S,x),x=x.sibling;return null}function n(S,x){for(S=new Map;x!==null;)x.key!==null?S.set(x.key,x):S.set(x.index,x),x=x.sibling;return S}function o(S,x){return S=or(S,x),S.index=0,S.sibling=null,S}function u(S,x,j){return S.index=j,e?(j=S.alternate,j!==null?(j=j.index,j<x?(S.flags|=2,x):j):(S.flags|=2,x)):(S.flags|=1048576,x)}function h(S){return e&&S.alternate===null&&(S.flags|=2),S}function v(S,x,j,I){return x===null||x.tag!==6?(x=wl(j,S.mode,I),x.return=S,x):(x=o(x,j),x.return=S,x)}function _(S,x,j,I){var G=j.type;return G===X?b(S,x,j.props.children,I,j.key):x!==null&&(x.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===We&&_u(G)===x.type)?(I=o(x,j.props),I.ref=Mn(S,x,j),I.return=S,I):(I=ci(j.type,j.key,j.props,null,S.mode,I),I.ref=Mn(S,x,j),I.return=S,I)}function C(S,x,j,I){return x===null||x.tag!==4||x.stateNode.containerInfo!==j.containerInfo||x.stateNode.implementation!==j.implementation?(x=_l(j,S.mode,I),x.return=S,x):(x=o(x,j.children||[]),x.return=S,x)}function b(S,x,j,I,G){return x===null||x.tag!==7?(x=Er(j,S.mode,I,G),x.return=S,x):(x=o(x,j),x.return=S,x)}function R(S,x,j){if(typeof x=="string"&&x!==""||typeof x=="number")return x=wl(""+x,S.mode,j),x.return=S,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case $:return j=ci(x.type,x.key,x.props,null,S.mode,j),j.ref=Mn(S,null,x),j.return=S,j;case A:return x=_l(x,S.mode,j),x.return=S,x;case We:var I=x._init;return R(S,I(x._payload),j)}if(mn(x)||J(x))return x=Er(x,S.mode,j,null),x.return=S,x;Ms(S,x)}return null}function N(S,x,j,I){var G=x!==null?x.key:null;if(typeof j=="string"&&j!==""||typeof j=="number")return G!==null?null:v(S,x,""+j,I);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case $:return j.key===G?_(S,x,j,I):null;case A:return j.key===G?C(S,x,j,I):null;case We:return G=j._init,N(S,x,G(j._payload),I)}if(mn(j)||J(j))return G!==null?null:b(S,x,j,I,null);Ms(S,j)}return null}function M(S,x,j,I,G){if(typeof I=="string"&&I!==""||typeof I=="number")return S=S.get(j)||null,v(x,S,""+I,G);if(typeof I=="object"&&I!==null){switch(I.$$typeof){case $:return S=S.get(I.key===null?j:I.key)||null,_(x,S,I,G);case A:return S=S.get(I.key===null?j:I.key)||null,C(x,S,I,G);case We:var Q=I._init;return M(S,x,j,Q(I._payload),G)}if(mn(I)||J(I))return S=S.get(j)||null,b(x,S,I,G,null);Ms(x,I)}return null}function H(S,x,j,I){for(var G=null,Q=null,K=x,Y=x=0,Ie=null;K!==null&&Y<j.length;Y++){K.index>Y?(Ie=K,K=null):Ie=K.sibling;var de=N(S,K,j[Y],I);if(de===null){K===null&&(K=Ie);break}e&&K&&de.alternate===null&&t(S,K),x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de,K=Ie}if(Y===j.length)return r(S,K),ke&&gr(S,Y),G;if(K===null){for(;Y<j.length;Y++)K=R(S,j[Y],I),K!==null&&(x=u(K,x,Y),Q===null?G=K:Q.sibling=K,Q=K);return ke&&gr(S,Y),G}for(K=n(S,K);Y<j.length;Y++)Ie=M(K,S,Y,j[Y],I),Ie!==null&&(e&&Ie.alternate!==null&&K.delete(Ie.key===null?Y:Ie.key),x=u(Ie,x,Y),Q===null?G=Ie:Q.sibling=Ie,Q=Ie);return e&&K.forEach(function(lr){return t(S,lr)}),ke&&gr(S,Y),G}function W(S,x,j,I){var G=J(j);if(typeof G!="function")throw Error(i(150));if(j=G.call(j),j==null)throw Error(i(151));for(var Q=G=null,K=x,Y=x=0,Ie=null,de=j.next();K!==null&&!de.done;Y++,de=j.next()){K.index>Y?(Ie=K,K=null):Ie=K.sibling;var lr=N(S,K,de.value,I);if(lr===null){K===null&&(K=Ie);break}e&&K&&lr.alternate===null&&t(S,K),x=u(lr,x,Y),Q===null?G=lr:Q.sibling=lr,Q=lr,K=Ie}if(de.done)return r(S,K),ke&&gr(S,Y),G;if(K===null){for(;!de.done;Y++,de=j.next())de=R(S,de.value,I),de!==null&&(x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de);return ke&&gr(S,Y),G}for(K=n(S,K);!de.done;Y++,de=j.next())de=M(K,S,Y,de.value,I),de!==null&&(e&&de.alternate!==null&&K.delete(de.key===null?Y:de.key),x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de);return e&&K.forEach(function(Fh){return t(S,Fh)}),ke&&gr(S,Y),G}function Ne(S,x,j,I){if(typeof j=="object"&&j!==null&&j.type===X&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case $:e:{for(var G=j.key,Q=x;Q!==null;){if(Q.key===G){if(G=j.type,G===X){if(Q.tag===7){r(S,Q.sibling),x=o(Q,j.props.children),x.return=S,S=x;break e}}else if(Q.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===We&&_u(G)===Q.type){r(S,Q.sibling),x=o(Q,j.props),x.ref=Mn(S,Q,j),x.return=S,S=x;break e}r(S,Q);break}else t(S,Q);Q=Q.sibling}j.type===X?(x=Er(j.props.children,S.mode,I,j.key),x.return=S,S=x):(I=ci(j.type,j.key,j.props,null,S.mode,I),I.ref=Mn(S,x,j),I.return=S,S=I)}return h(S);case A:e:{for(Q=j.key;x!==null;){if(x.key===Q)if(x.tag===4&&x.stateNode.containerInfo===j.containerInfo&&x.stateNode.implementation===j.implementation){r(S,x.sibling),x=o(x,j.children||[]),x.return=S,S=x;break e}else{r(S,x);break}else t(S,x);x=x.sibling}x=_l(j,S.mode,I),x.return=S,S=x}return h(S);case We:return Q=j._init,Ne(S,x,Q(j._payload),I)}if(mn(j))return H(S,x,j,I);if(J(j))return W(S,x,j,I);Ms(S,j)}return typeof j=="string"&&j!==""||typeof j=="number"?(j=""+j,x!==null&&x.tag===6?(r(S,x.sibling),x=o(x,j),x.return=S,S=x):(r(S,x),x=wl(j,S.mode,I),x.return=S,S=x),h(S)):r(S,x)}return Ne}var Wr=xu(!0),ku=xu(!1),Fs=Jt(null),Vs=null,Gr=null,To=null;function bo(){To=Gr=Vs=null}function Ro(e){var t=Fs.current;we(Fs),e._currentValue=t}function Oo(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Qr(e,t){Vs=e,To=Gr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ke=!0),e.firstContext=null)}function ct(e){var t=e._currentValue;if(To!==e)if(e={context:e,memoizedValue:t,next:null},Gr===null){if(Vs===null)throw Error(i(308));Gr=e,Vs.dependencies={lanes:0,firstContext:e}}else Gr=Gr.next=e;return t}var vr=null;function $o(e){vr===null?vr=[e]:vr.push(e)}function Su(e,t,r,n){var o=t.interleaved;return o===null?(r.next=r,$o(t)):(r.next=o.next,o.next=r),t.interleaved=r,zt(e,n)}function zt(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var Zt=!1;function Io(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Eu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ut(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function er(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(le&2)!==0){var o=n.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),n.pending=t,zt(e,r)}return o=n.interleaved,o===null?(t.next=t,$o(n)):(t.next=o.next,o.next=t),n.interleaved=t,zt(e,r)}function Bs(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Gi(e,r)}}function ju(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var o=null,u=null;if(r=r.firstBaseUpdate,r!==null){do{var h={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};u===null?o=u=h:u=u.next=h,r=r.next}while(r!==null);u===null?o=u=t:u=u.next=t}else o=u=t;r={baseState:n.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Hs(e,t,r,n){var o=e.updateQueue;Zt=!1;var u=o.firstBaseUpdate,h=o.lastBaseUpdate,v=o.shared.pending;if(v!==null){o.shared.pending=null;var _=v,C=_.next;_.next=null,h===null?u=C:h.next=C,h=_;var b=e.alternate;b!==null&&(b=b.updateQueue,v=b.lastBaseUpdate,v!==h&&(v===null?b.firstBaseUpdate=C:v.next=C,b.lastBaseUpdate=_))}if(u!==null){var R=o.baseState;h=0,b=C=_=null,v=u;do{var N=v.lane,M=v.eventTime;if((n&N)===N){b!==null&&(b=b.next={eventTime:M,lane:0,tag:v.tag,payload:v.payload,callback:v.callback,next:null});e:{var H=e,W=v;switch(N=t,M=r,W.tag){case 1:if(H=W.payload,typeof H=="function"){R=H.call(M,R,N);break e}R=H;break e;case 3:H.flags=H.flags&-65537|128;case 0:if(H=W.payload,N=typeof H=="function"?H.call(M,R,N):H,N==null)break e;R=B({},R,N);break e;case 2:Zt=!0}}v.callback!==null&&v.lane!==0&&(e.flags|=64,N=o.effects,N===null?o.effects=[v]:N.push(v))}else M={eventTime:M,lane:N,tag:v.tag,payload:v.payload,callback:v.callback,next:null},b===null?(C=b=M,_=R):b=b.next=M,h|=N;if(v=v.next,v===null){if(v=o.shared.pending,v===null)break;N=v,v=N.next,N.next=null,o.lastBaseUpdate=N,o.shared.pending=null}}while(!0);if(b===null&&(_=R),o.baseState=_,o.firstBaseUpdate=C,o.lastBaseUpdate=b,t=o.shared.interleaved,t!==null){o=t;do h|=o.lane,o=o.next;while(o!==t)}else u===null&&(o.shared.lanes=0);_r|=h,e.lanes=h,e.memoizedState=R}}function Cu(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],o=n.callback;if(o!==null){if(n.callback=null,n=r,typeof o!="function")throw Error(i(191,o));o.call(n)}}}var Fn={},bt=Jt(Fn),Vn=Jt(Fn),Bn=Jt(Fn);function yr(e){if(e===Fn)throw Error(i(174));return e}function Lo(e,t){switch(ge(Bn,t),ge(Vn,e),ge(bt,Fn),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Li(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Li(t,e)}we(bt),ge(bt,t)}function Kr(){we(bt),we(Vn),we(Bn)}function Pu(e){yr(Bn.current);var t=yr(bt.current),r=Li(t,e.type);t!==r&&(ge(Vn,e),ge(bt,r))}function Ao(e){Vn.current===e&&(we(bt),we(Vn))}var Se=Jt(0);function qs(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Do=[];function zo(){for(var e=0;e<Do.length;e++)Do[e]._workInProgressVersionPrimary=null;Do.length=0}var Ws=q.ReactCurrentDispatcher,Uo=q.ReactCurrentBatchConfig,wr=0,Ee=null,be=null,Oe=null,Gs=!1,Hn=!1,qn=0,uh=0;function Fe(){throw Error(i(321))}function Mo(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!wt(e[r],t[r]))return!1;return!0}function Fo(e,t,r,n,o,u){if(wr=u,Ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ws.current=e===null||e.memoizedState===null?hh:ph,e=r(n,o),Hn){u=0;do{if(Hn=!1,qn=0,25<=u)throw Error(i(301));u+=1,Oe=be=null,t.updateQueue=null,Ws.current=mh,e=r(n,o)}while(Hn)}if(Ws.current=Js,t=be!==null&&be.next!==null,wr=0,Oe=be=Ee=null,Gs=!1,t)throw Error(i(300));return e}function Vo(){var e=qn!==0;return qn=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Oe===null?Ee.memoizedState=Oe=e:Oe=Oe.next=e,Oe}function dt(){if(be===null){var e=Ee.alternate;e=e!==null?e.memoizedState:null}else e=be.next;var t=Oe===null?Ee.memoizedState:Oe.next;if(t!==null)Oe=t,be=e;else{if(e===null)throw Error(i(310));be=e,e={memoizedState:be.memoizedState,baseState:be.baseState,baseQueue:be.baseQueue,queue:be.queue,next:null},Oe===null?Ee.memoizedState=Oe=e:Oe=Oe.next=e}return Oe}function Wn(e,t){return typeof t=="function"?t(e):t}function Bo(e){var t=dt(),r=t.queue;if(r===null)throw Error(i(311));r.lastRenderedReducer=e;var n=be,o=n.baseQueue,u=r.pending;if(u!==null){if(o!==null){var h=o.next;o.next=u.next,u.next=h}n.baseQueue=o=u,r.pending=null}if(o!==null){u=o.next,n=n.baseState;var v=h=null,_=null,C=u;do{var b=C.lane;if((wr&b)===b)_!==null&&(_=_.next={lane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),n=C.hasEagerState?C.eagerState:e(n,C.action);else{var R={lane:b,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null};_===null?(v=_=R,h=n):_=_.next=R,Ee.lanes|=b,_r|=b}C=C.next}while(C!==null&&C!==u);_===null?h=n:_.next=v,wt(n,t.memoizedState)||(Ke=!0),t.memoizedState=n,t.baseState=h,t.baseQueue=_,r.lastRenderedState=n}if(e=r.interleaved,e!==null){o=e;do u=o.lane,Ee.lanes|=u,_r|=u,o=o.next;while(o!==e)}else o===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Ho(e){var t=dt(),r=t.queue;if(r===null)throw Error(i(311));r.lastRenderedReducer=e;var n=r.dispatch,o=r.pending,u=t.memoizedState;if(o!==null){r.pending=null;var h=o=o.next;do u=e(u,h.action),h=h.next;while(h!==o);wt(u,t.memoizedState)||(Ke=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),r.lastRenderedState=u}return[u,n]}function Nu(){}function Tu(e,t){var r=Ee,n=dt(),o=t(),u=!wt(n.memoizedState,o);if(u&&(n.memoizedState=o,Ke=!0),n=n.queue,qo(Ou.bind(null,r,n,e),[e]),n.getSnapshot!==t||u||Oe!==null&&Oe.memoizedState.tag&1){if(r.flags|=2048,Gn(9,Ru.bind(null,r,n,o,t),void 0,null),$e===null)throw Error(i(349));(wr&30)!==0||bu(r,t,o)}return o}function bu(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Ru(e,t,r,n){t.value=r,t.getSnapshot=n,$u(t)&&Iu(e)}function Ou(e,t,r){return r(function(){$u(t)&&Iu(e)})}function $u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!wt(e,r)}catch{return!0}}function Iu(e){var t=zt(e,1);t!==null&&Et(t,e,1,-1)}function Lu(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Wn,lastRenderedState:e},t.queue=e,e=e.dispatch=fh.bind(null,Ee,e),[t.memoizedState,e]}function Gn(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Au(){return dt().memoizedState}function Qs(e,t,r,n){var o=Rt();Ee.flags|=e,o.memoizedState=Gn(1|t,r,void 0,n===void 0?null:n)}function Ks(e,t,r,n){var o=dt();n=n===void 0?null:n;var u=void 0;if(be!==null){var h=be.memoizedState;if(u=h.destroy,n!==null&&Mo(n,h.deps)){o.memoizedState=Gn(t,r,u,n);return}}Ee.flags|=e,o.memoizedState=Gn(1|t,r,u,n)}function Du(e,t){return Qs(8390656,8,e,t)}function qo(e,t){return Ks(2048,8,e,t)}function zu(e,t){return Ks(4,2,e,t)}function Uu(e,t){return Ks(4,4,e,t)}function Mu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fu(e,t,r){return r=r!=null?r.concat([e]):null,Ks(4,4,Mu.bind(null,t,e),r)}function Wo(){}function Vu(e,t){var r=dt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Mo(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Bu(e,t){var r=dt();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&Mo(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Hu(e,t,r){return(wr&21)===0?(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=r):(wt(r,t)||(r=_a(),Ee.lanes|=r,_r|=r,e.baseState=!0),t)}function ch(e,t){var r=he;he=r!==0&&4>r?r:4,e(!0);var n=Uo.transition;Uo.transition={};try{e(!1),t()}finally{he=r,Uo.transition=n}}function qu(){return dt().memoizedState}function dh(e,t,r){var n=sr(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Wu(e))Gu(t,r);else if(r=Su(e,t,r,n),r!==null){var o=qe();Et(r,e,n,o),Qu(r,t,n)}}function fh(e,t,r){var n=sr(e),o={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Wu(e))Gu(t,o);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var h=t.lastRenderedState,v=u(h,r);if(o.hasEagerState=!0,o.eagerState=v,wt(v,h)){var _=t.interleaved;_===null?(o.next=o,$o(t)):(o.next=_.next,_.next=o),t.interleaved=o;return}}catch{}finally{}r=Su(e,t,o,n),r!==null&&(o=qe(),Et(r,e,n,o),Qu(r,t,n))}}function Wu(e){var t=e.alternate;return e===Ee||t!==null&&t===Ee}function Gu(e,t){Hn=Gs=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Qu(e,t,r){if((r&4194240)!==0){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Gi(e,r)}}var Js={readContext:ct,useCallback:Fe,useContext:Fe,useEffect:Fe,useImperativeHandle:Fe,useInsertionEffect:Fe,useLayoutEffect:Fe,useMemo:Fe,useReducer:Fe,useRef:Fe,useState:Fe,useDebugValue:Fe,useDeferredValue:Fe,useTransition:Fe,useMutableSource:Fe,useSyncExternalStore:Fe,useId:Fe,unstable_isNewReconciler:!1},hh={readContext:ct,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:ct,useEffect:Du,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,Qs(4194308,4,Mu.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Qs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Qs(4,2,e,t)},useMemo:function(e,t){var r=Rt();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Rt();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=dh.bind(null,Ee,e),[n.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:Lu,useDebugValue:Wo,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=Lu(!1),t=e[0];return e=ch.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=Ee,o=Rt();if(ke){if(r===void 0)throw Error(i(407));r=r()}else{if(r=t(),$e===null)throw Error(i(349));(wr&30)!==0||bu(n,t,r)}o.memoizedState=r;var u={value:r,getSnapshot:t};return o.queue=u,Du(Ou.bind(null,n,u,e),[e]),n.flags|=2048,Gn(9,Ru.bind(null,n,u,r,t),void 0,null),r},useId:function(){var e=Rt(),t=$e.identifierPrefix;if(ke){var r=Dt,n=At;r=(n&~(1<<32-yt(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=qn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=uh++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ph={readContext:ct,useCallback:Vu,useContext:ct,useEffect:qo,useImperativeHandle:Fu,useInsertionEffect:zu,useLayoutEffect:Uu,useMemo:Bu,useReducer:Bo,useRef:Au,useState:function(){return Bo(Wn)},useDebugValue:Wo,useDeferredValue:function(e){var t=dt();return Hu(t,be.memoizedState,e)},useTransition:function(){var e=Bo(Wn)[0],t=dt().memoizedState;return[e,t]},useMutableSource:Nu,useSyncExternalStore:Tu,useId:qu,unstable_isNewReconciler:!1},mh={readContext:ct,useCallback:Vu,useContext:ct,useEffect:qo,useImperativeHandle:Fu,useInsertionEffect:zu,useLayoutEffect:Uu,useMemo:Bu,useReducer:Ho,useRef:Au,useState:function(){return Ho(Wn)},useDebugValue:Wo,useDeferredValue:function(e){var t=dt();return be===null?t.memoizedState=e:Hu(t,be.memoizedState,e)},useTransition:function(){var e=Ho(Wn)[0],t=dt().memoizedState;return[e,t]},useMutableSource:Nu,useSyncExternalStore:Tu,useId:qu,unstable_isNewReconciler:!1};function xt(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function Go(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:B({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Ys={isMounted:function(e){return(e=e._reactInternals)?fr(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=qe(),o=sr(e),u=Ut(n,o);u.payload=t,r!=null&&(u.callback=r),t=er(e,u,o),t!==null&&(Et(t,e,o,n),Bs(t,e,o))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=qe(),o=sr(e),u=Ut(n,o);u.tag=1,u.payload=t,r!=null&&(u.callback=r),t=er(e,u,o),t!==null&&(Et(t,e,o,n),Bs(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=qe(),n=sr(e),o=Ut(r,n);o.tag=2,t!=null&&(o.callback=t),t=er(e,o,n),t!==null&&(Et(t,e,n,r),Bs(t,e,n))}};function Ku(e,t,r,n,o,u,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,u,h):t.prototype&&t.prototype.isPureReactComponent?!$n(r,n)||!$n(o,u):!0}function Ju(e,t,r){var n=!1,o=Yt,u=t.contextType;return typeof u=="object"&&u!==null?u=ct(u):(o=Qe(t)?pr:Me.current,n=t.contextTypes,u=(n=n!=null)?Vr(e,o):Yt),t=new t(r,u),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ys,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=u),t}function Yu(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&Ys.enqueueReplaceState(t,t.state,null)}function Qo(e,t,r,n){var o=e.stateNode;o.props=r,o.state=e.memoizedState,o.refs={},Io(e);var u=t.contextType;typeof u=="object"&&u!==null?o.context=ct(u):(u=Qe(t)?pr:Me.current,o.context=Vr(e,u)),o.state=e.memoizedState,u=t.getDerivedStateFromProps,typeof u=="function"&&(Go(e,t,u,r),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Ys.enqueueReplaceState(o,o.state,null),Hs(e,r,o,n),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Jr(e,t){try{var r="",n=t;do r+=ue(n),n=n.return;while(n);var o=r}catch(u){o=`
Error generating stack: `+u.message+`
`+u.stack}return{value:e,source:t,stack:o,digest:null}}function Ko(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function Jo(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var gh=typeof WeakMap=="function"?WeakMap:Map;function Xu(e,t,r){r=Ut(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){si||(si=!0,dl=n),Jo(e,t)},r}function Zu(e,t,r){r=Ut(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var o=t.value;r.payload=function(){return n(o)},r.callback=function(){Jo(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(r.callback=function(){Jo(e,t),typeof n!="function"&&(rr===null?rr=new Set([this]):rr.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),r}function ec(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new gh;var o=new Set;n.set(t,o)}else o=n.get(t),o===void 0&&(o=new Set,n.set(t,o));o.has(r)||(o.add(r),e=bh.bind(null,e,t,r),t.then(e,e))}function tc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function rc(e,t,r,n,o){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=Ut(-1,1),t.tag=2,er(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var vh=q.ReactCurrentOwner,Ke=!1;function He(e,t,r,n){t.child=e===null?ku(t,null,r,n):Wr(t,e.child,r,n)}function nc(e,t,r,n,o){r=r.render;var u=t.ref;return Qr(t,o),n=Fo(e,t,r,n,u,o),r=Vo(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(ke&&r&&Eo(t),t.flags|=1,He(e,t,n,o),t.child)}function sc(e,t,r,n,o){if(e===null){var u=r.type;return typeof u=="function"&&!yl(u)&&u.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=u,ic(e,t,u,n,o)):(e=ci(r.type,null,n,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,(e.lanes&o)===0){var h=u.memoizedProps;if(r=r.compare,r=r!==null?r:$n,r(h,n)&&e.ref===t.ref)return Mt(e,t,o)}return t.flags|=1,e=or(u,n),e.ref=t.ref,e.return=t,t.child=e}function ic(e,t,r,n,o){if(e!==null){var u=e.memoizedProps;if($n(u,n)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=n=u,(e.lanes&o)!==0)(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,Mt(e,t,o)}return Yo(e,t,r,n,o)}function oc(e,t,r){var n=t.pendingProps,o=n.children,u=e!==null?e.memoizedState:null;if(n.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ge(Xr,it),it|=r;else{if((r&1073741824)===0)return e=u!==null?u.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ge(Xr,it),it|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=u!==null?u.baseLanes:r,ge(Xr,it),it|=n}else u!==null?(n=u.baseLanes|r,t.memoizedState=null):n=r,ge(Xr,it),it|=n;return He(e,t,o,r),t.child}function lc(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function Yo(e,t,r,n,o){var u=Qe(r)?pr:Me.current;return u=Vr(t,u),Qr(t,o),r=Fo(e,t,r,n,u,o),n=Vo(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Mt(e,t,o)):(ke&&n&&Eo(t),t.flags|=1,He(e,t,r,o),t.child)}function ac(e,t,r,n,o){if(Qe(r)){var u=!0;Ls(t)}else u=!1;if(Qr(t,o),t.stateNode===null)Zs(e,t),Ju(t,r,n),Qo(t,r,n,o),n=!0;else if(e===null){var h=t.stateNode,v=t.memoizedProps;h.props=v;var _=h.context,C=r.contextType;typeof C=="object"&&C!==null?C=ct(C):(C=Qe(r)?pr:Me.current,C=Vr(t,C));var b=r.getDerivedStateFromProps,R=typeof b=="function"||typeof h.getSnapshotBeforeUpdate=="function";R||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==n||_!==C)&&Yu(t,h,n,C),Zt=!1;var N=t.memoizedState;h.state=N,Hs(t,n,h,o),_=t.memoizedState,v!==n||N!==_||Ge.current||Zt?(typeof b=="function"&&(Go(t,r,b,n),_=t.memoizedState),(v=Zt||Ku(t,r,v,n,N,_,C))?(R||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=_),h.props=n,h.state=_,h.context=C,n=v):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{h=t.stateNode,Eu(e,t),v=t.memoizedProps,C=t.type===t.elementType?v:xt(t.type,v),h.props=C,R=t.pendingProps,N=h.context,_=r.contextType,typeof _=="object"&&_!==null?_=ct(_):(_=Qe(r)?pr:Me.current,_=Vr(t,_));var M=r.getDerivedStateFromProps;(b=typeof M=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==R||N!==_)&&Yu(t,h,n,_),Zt=!1,N=t.memoizedState,h.state=N,Hs(t,n,h,o);var H=t.memoizedState;v!==R||N!==H||Ge.current||Zt?(typeof M=="function"&&(Go(t,r,M,n),H=t.memoizedState),(C=Zt||Ku(t,r,C,n,N,H,_)||!1)?(b||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(n,H,_),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(n,H,_)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=H),h.props=n,h.state=H,h.context=_,n=C):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),n=!1)}return Xo(e,t,r,n,u,o)}function Xo(e,t,r,n,o,u){lc(e,t);var h=(t.flags&128)!==0;if(!n&&!h)return o&&hu(t,r,!1),Mt(e,t,u);n=t.stateNode,vh.current=t;var v=h&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&h?(t.child=Wr(t,e.child,null,u),t.child=Wr(t,null,v,u)):He(e,t,v,u),t.memoizedState=n.state,o&&hu(t,r,!0),t.child}function uc(e){var t=e.stateNode;t.pendingContext?du(e,t.pendingContext,t.pendingContext!==t.context):t.context&&du(e,t.context,!1),Lo(e,t.containerInfo)}function cc(e,t,r,n,o){return qr(),No(o),t.flags|=256,He(e,t,r,n),t.child}var Zo={dehydrated:null,treeContext:null,retryLane:0};function el(e){return{baseLanes:e,cachePool:null,transitions:null}}function dc(e,t,r){var n=t.pendingProps,o=Se.current,u=!1,h=(t.flags&128)!==0,v;if((v=h)||(v=e!==null&&e.memoizedState===null?!1:(o&2)!==0),v?(u=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ge(Se,o&1),e===null)return Po(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=n.children,e=n.fallback,u?(n=t.mode,u=t.child,h={mode:"hidden",children:h},(n&1)===0&&u!==null?(u.childLanes=0,u.pendingProps=h):u=di(h,n,0,null),e=Er(e,n,r,null),u.return=t,e.return=t,u.sibling=e,t.child=u,t.child.memoizedState=el(r),t.memoizedState=Zo,e):tl(t,h));if(o=e.memoizedState,o!==null&&(v=o.dehydrated,v!==null))return yh(e,t,h,n,v,o,r);if(u){u=n.fallback,h=t.mode,o=e.child,v=o.sibling;var _={mode:"hidden",children:n.children};return(h&1)===0&&t.child!==o?(n=t.child,n.childLanes=0,n.pendingProps=_,t.deletions=null):(n=or(o,_),n.subtreeFlags=o.subtreeFlags&14680064),v!==null?u=or(v,u):(u=Er(u,h,r,null),u.flags|=2),u.return=t,n.return=t,n.sibling=u,t.child=n,n=u,u=t.child,h=e.child.memoizedState,h=h===null?el(r):{baseLanes:h.baseLanes|r,cachePool:null,transitions:h.transitions},u.memoizedState=h,u.childLanes=e.childLanes&~r,t.memoizedState=Zo,n}return u=e.child,e=u.sibling,n=or(u,{mode:"visible",children:n.children}),(t.mode&1)===0&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function tl(e,t){return t=di({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Xs(e,t,r,n){return n!==null&&No(n),Wr(t,e.child,null,r),e=tl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function yh(e,t,r,n,o,u,h){if(r)return t.flags&256?(t.flags&=-257,n=Ko(Error(i(422))),Xs(e,t,h,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(u=n.fallback,o=t.mode,n=di({mode:"visible",children:n.children},o,0,null),u=Er(u,o,h,null),u.flags|=2,n.return=t,u.return=t,n.sibling=u,t.child=n,(t.mode&1)!==0&&Wr(t,e.child,null,h),t.child.memoizedState=el(h),t.memoizedState=Zo,u);if((t.mode&1)===0)return Xs(e,t,h,null);if(o.data==="$!"){if(n=o.nextSibling&&o.nextSibling.dataset,n)var v=n.dgst;return n=v,u=Error(i(419)),n=Ko(u,n,void 0),Xs(e,t,h,n)}if(v=(h&e.childLanes)!==0,Ke||v){if(n=$e,n!==null){switch(h&-h){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=(o&(n.suspendedLanes|h))!==0?0:o,o!==0&&o!==u.retryLane&&(u.retryLane=o,zt(e,o),Et(n,e,o,-1))}return vl(),n=Ko(Error(i(421))),Xs(e,t,h,n)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Rh.bind(null,e),o._reactRetry=t,null):(e=u.treeContext,st=Kt(o.nextSibling),nt=t,ke=!0,_t=null,e!==null&&(at[ut++]=At,at[ut++]=Dt,at[ut++]=mr,At=e.id,Dt=e.overflow,mr=t),t=tl(t,n.children),t.flags|=4096,t)}function fc(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Oo(e.return,t,r)}function rl(e,t,r,n,o){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:o}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=n,u.tail=r,u.tailMode=o)}function hc(e,t,r){var n=t.pendingProps,o=n.revealOrder,u=n.tail;if(He(e,t,n.children,r),n=Se.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&fc(e,r,t);else if(e.tag===19)fc(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(ge(Se,n),(t.mode&1)===0)t.memoizedState=null;else switch(o){case"forwards":for(r=t.child,o=null;r!==null;)e=r.alternate,e!==null&&qs(e)===null&&(o=r),r=r.sibling;r=o,r===null?(o=t.child,t.child=null):(o=r.sibling,r.sibling=null),rl(t,!1,o,r,u);break;case"backwards":for(r=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&qs(e)===null){t.child=o;break}e=o.sibling,o.sibling=r,r=o,o=e}rl(t,!0,r,null,u);break;case"together":rl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zs(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Mt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),_r|=t.lanes,(r&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,r=or(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=or(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function wh(e,t,r){switch(t.tag){case 3:uc(t),qr();break;case 5:Pu(t);break;case 1:Qe(t.type)&&Ls(t);break;case 4:Lo(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,o=t.memoizedProps.value;ge(Fs,n._currentValue),n._currentValue=o;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?(ge(Se,Se.current&1),t.flags|=128,null):(r&t.child.childLanes)!==0?dc(e,t,r):(ge(Se,Se.current&1),e=Mt(e,t,r),e!==null?e.sibling:null);ge(Se,Se.current&1);break;case 19:if(n=(r&t.childLanes)!==0,(e.flags&128)!==0){if(n)return hc(e,t,r);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ge(Se,Se.current),n)break;return null;case 22:case 23:return t.lanes=0,oc(e,t,r)}return Mt(e,t,r)}var pc,nl,mc,gc;pc=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},nl=function(){},mc=function(e,t,r,n){var o=e.memoizedProps;if(o!==n){e=t.stateNode,yr(bt.current);var u=null;switch(r){case"input":o=Ri(e,o),n=Ri(e,n),u=[];break;case"select":o=B({},o,{value:void 0}),n=B({},n,{value:void 0}),u=[];break;case"textarea":o=Ii(e,o),n=Ii(e,n),u=[];break;default:typeof o.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=Os)}Ai(r,n);var h;r=null;for(C in o)if(!n.hasOwnProperty(C)&&o.hasOwnProperty(C)&&o[C]!=null)if(C==="style"){var v=o[C];for(h in v)v.hasOwnProperty(h)&&(r||(r={}),r[h]="")}else C!=="dangerouslySetInnerHTML"&&C!=="children"&&C!=="suppressContentEditableWarning"&&C!=="suppressHydrationWarning"&&C!=="autoFocus"&&(a.hasOwnProperty(C)?u||(u=[]):(u=u||[]).push(C,null));for(C in n){var _=n[C];if(v=o!=null?o[C]:void 0,n.hasOwnProperty(C)&&_!==v&&(_!=null||v!=null))if(C==="style")if(v){for(h in v)!v.hasOwnProperty(h)||_&&_.hasOwnProperty(h)||(r||(r={}),r[h]="");for(h in _)_.hasOwnProperty(h)&&v[h]!==_[h]&&(r||(r={}),r[h]=_[h])}else r||(u||(u=[]),u.push(C,r)),r=_;else C==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,v=v?v.__html:void 0,_!=null&&v!==_&&(u=u||[]).push(C,_)):C==="children"?typeof _!="string"&&typeof _!="number"||(u=u||[]).push(C,""+_):C!=="suppressContentEditableWarning"&&C!=="suppressHydrationWarning"&&(a.hasOwnProperty(C)?(_!=null&&C==="onScroll"&&ye("scroll",e),u||v===_||(u=[])):(u=u||[]).push(C,_))}r&&(u=u||[]).push("style",r);var C=u;(t.updateQueue=C)&&(t.flags|=4)}},gc=function(e,t,r,n){r!==n&&(t.flags|=4)};function Qn(e,t){if(!ke)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var o=e.child;o!==null;)r|=o.lanes|o.childLanes,n|=o.subtreeFlags&14680064,n|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)r|=o.lanes|o.childLanes,n|=o.subtreeFlags,n|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function _h(e,t,r){var n=t.pendingProps;switch(jo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ve(t),null;case 1:return Qe(t.type)&&Is(),Ve(t),null;case 3:return n=t.stateNode,Kr(),we(Ge),we(Me),zo(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Us(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,_t!==null&&(pl(_t),_t=null))),nl(e,t),Ve(t),null;case 5:Ao(t);var o=yr(Bn.current);if(r=t.type,e!==null&&t.stateNode!=null)mc(e,t,r,n,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(i(166));return Ve(t),null}if(e=yr(bt.current),Us(t)){n=t.stateNode,r=t.type;var u=t.memoizedProps;switch(n[Tt]=t,n[zn]=u,e=(t.mode&1)!==0,r){case"dialog":ye("cancel",n),ye("close",n);break;case"iframe":case"object":case"embed":ye("load",n);break;case"video":case"audio":for(o=0;o<Ln.length;o++)ye(Ln[o],n);break;case"source":ye("error",n);break;case"img":case"image":case"link":ye("error",n),ye("load",n);break;case"details":ye("toggle",n);break;case"input":Jl(n,u),ye("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!u.multiple},ye("invalid",n);break;case"textarea":Zl(n,u),ye("invalid",n)}Ai(r,u),o=null;for(var h in u)if(u.hasOwnProperty(h)){var v=u[h];h==="children"?typeof v=="string"?n.textContent!==v&&(u.suppressHydrationWarning!==!0&&Rs(n.textContent,v,e),o=["children",v]):typeof v=="number"&&n.textContent!==""+v&&(u.suppressHydrationWarning!==!0&&Rs(n.textContent,v,e),o=["children",""+v]):a.hasOwnProperty(h)&&v!=null&&h==="onScroll"&&ye("scroll",n)}switch(r){case"input":as(n),Xl(n,u,!0);break;case"textarea":as(n),ta(n);break;case"select":case"option":break;default:typeof u.onClick=="function"&&(n.onclick=Os)}n=o,t.updateQueue=n,n!==null&&(t.flags|=4)}else{h=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ra(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=h.createElement(r,{is:n.is}):(e=h.createElement(r),r==="select"&&(h=e,n.multiple?h.multiple=!0:n.size&&(h.size=n.size))):e=h.createElementNS(e,r),e[Tt]=t,e[zn]=n,pc(e,t,!1,!1),t.stateNode=e;e:{switch(h=Di(r,n),r){case"dialog":ye("cancel",e),ye("close",e),o=n;break;case"iframe":case"object":case"embed":ye("load",e),o=n;break;case"video":case"audio":for(o=0;o<Ln.length;o++)ye(Ln[o],e);o=n;break;case"source":ye("error",e),o=n;break;case"img":case"image":case"link":ye("error",e),ye("load",e),o=n;break;case"details":ye("toggle",e),o=n;break;case"input":Jl(e,n),o=Ri(e,n),ye("invalid",e);break;case"option":o=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},o=B({},n,{value:void 0}),ye("invalid",e);break;case"textarea":Zl(e,n),o=Ii(e,n),ye("invalid",e);break;default:o=n}Ai(r,o),v=o;for(u in v)if(v.hasOwnProperty(u)){var _=v[u];u==="style"?ia(e,_):u==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,_!=null&&na(e,_)):u==="children"?typeof _=="string"?(r!=="textarea"||_!=="")&&gn(e,_):typeof _=="number"&&gn(e,""+_):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(a.hasOwnProperty(u)?_!=null&&u==="onScroll"&&ye("scroll",e):_!=null&&U(e,u,_,h))}switch(r){case"input":as(e),Xl(e,n,!1);break;case"textarea":as(e),ta(e);break;case"option":n.value!=null&&e.setAttribute("value",""+fe(n.value));break;case"select":e.multiple=!!n.multiple,u=n.value,u!=null?br(e,!!n.multiple,u,!1):n.defaultValue!=null&&br(e,!!n.multiple,n.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Os)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ve(t),null;case 6:if(e&&t.stateNode!=null)gc(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(i(166));if(r=yr(Bn.current),yr(bt.current),Us(t)){if(n=t.stateNode,r=t.memoizedProps,n[Tt]=t,(u=n.nodeValue!==r)&&(e=nt,e!==null))switch(e.tag){case 3:Rs(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Rs(n.nodeValue,r,(e.mode&1)!==0)}u&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Tt]=t,t.stateNode=n}return Ve(t),null;case 13:if(we(Se),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ke&&st!==null&&(t.mode&1)!==0&&(t.flags&128)===0)wu(),qr(),t.flags|=98560,u=!1;else if(u=Us(t),n!==null&&n.dehydrated!==null){if(e===null){if(!u)throw Error(i(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(i(317));u[Tt]=t}else qr(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ve(t),u=!1}else _t!==null&&(pl(_t),_t=null),u=!0;if(!u)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Se.current&1)!==0?Re===0&&(Re=3):vl())),t.updateQueue!==null&&(t.flags|=4),Ve(t),null);case 4:return Kr(),nl(e,t),e===null&&An(t.stateNode.containerInfo),Ve(t),null;case 10:return Ro(t.type._context),Ve(t),null;case 17:return Qe(t.type)&&Is(),Ve(t),null;case 19:if(we(Se),u=t.memoizedState,u===null)return Ve(t),null;if(n=(t.flags&128)!==0,h=u.rendering,h===null)if(n)Qn(u,!1);else{if(Re!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=qs(e),h!==null){for(t.flags|=128,Qn(u,!1),n=h.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)u=r,e=n,u.flags&=14680066,h=u.alternate,h===null?(u.childLanes=0,u.lanes=e,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=h.childLanes,u.lanes=h.lanes,u.child=h.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=h.memoizedProps,u.memoizedState=h.memoizedState,u.updateQueue=h.updateQueue,u.type=h.type,e=h.dependencies,u.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return ge(Se,Se.current&1|2),t.child}e=e.sibling}u.tail!==null&&Pe()>Zr&&(t.flags|=128,n=!0,Qn(u,!1),t.lanes=4194304)}else{if(!n)if(e=qs(h),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Qn(u,!0),u.tail===null&&u.tailMode==="hidden"&&!h.alternate&&!ke)return Ve(t),null}else 2*Pe()-u.renderingStartTime>Zr&&r!==1073741824&&(t.flags|=128,n=!0,Qn(u,!1),t.lanes=4194304);u.isBackwards?(h.sibling=t.child,t.child=h):(r=u.last,r!==null?r.sibling=h:t.child=h,u.last=h)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Pe(),t.sibling=null,r=Se.current,ge(Se,n?r&1|2:r&1),t):(Ve(t),null);case 22:case 23:return gl(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&(t.mode&1)!==0?(it&1073741824)!==0&&(Ve(t),t.subtreeFlags&6&&(t.flags|=8192)):Ve(t),null;case 24:return null;case 25:return null}throw Error(i(156,t.tag))}function xh(e,t){switch(jo(t),t.tag){case 1:return Qe(t.type)&&Is(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kr(),we(Ge),we(Me),zo(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Ao(t),null;case 13:if(we(Se),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));qr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return we(Se),null;case 4:return Kr(),null;case 10:return Ro(t.type._context),null;case 22:case 23:return gl(),null;case 24:return null;default:return null}}var ei=!1,Be=!1,kh=typeof WeakSet=="function"?WeakSet:Set,V=null;function Yr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){Ce(e,t,n)}else r.current=null}function sl(e,t,r){try{r()}catch(n){Ce(e,t,n)}}var vc=!1;function Sh(e,t){if(go=_s,e=Ka(),lo(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var o=n.anchorOffset,u=n.focusNode;n=n.focusOffset;try{r.nodeType,u.nodeType}catch{r=null;break e}var h=0,v=-1,_=-1,C=0,b=0,R=e,N=null;t:for(;;){for(var M;R!==r||o!==0&&R.nodeType!==3||(v=h+o),R!==u||n!==0&&R.nodeType!==3||(_=h+n),R.nodeType===3&&(h+=R.nodeValue.length),(M=R.firstChild)!==null;)N=R,R=M;for(;;){if(R===e)break t;if(N===r&&++C===o&&(v=h),N===u&&++b===n&&(_=h),(M=R.nextSibling)!==null)break;R=N,N=R.parentNode}R=M}r=v===-1||_===-1?null:{start:v,end:_}}else r=null}r=r||{start:0,end:0}}else r=null;for(vo={focusedElem:e,selectionRange:r},_s=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var H=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(H!==null){var W=H.memoizedProps,Ne=H.memoizedState,S=t.stateNode,x=S.getSnapshotBeforeUpdate(t.elementType===t.type?W:xt(t.type,W),Ne);S.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var j=t.stateNode.containerInfo;j.nodeType===1?j.textContent="":j.nodeType===9&&j.documentElement&&j.removeChild(j.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(I){Ce(t,t.return,I)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return H=vc,vc=!1,H}function Kn(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var o=n=n.next;do{if((o.tag&e)===e){var u=o.destroy;o.destroy=void 0,u!==void 0&&sl(t,r,u)}o=o.next}while(o!==n)}}function ti(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function il(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function yc(e){var t=e.alternate;t!==null&&(e.alternate=null,yc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Tt],delete t[zn],delete t[xo],delete t[ih],delete t[oh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function wc(e){return e.tag===5||e.tag===3||e.tag===4}function _c(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ol(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=Os));else if(n!==4&&(e=e.child,e!==null))for(ol(e,t,r),e=e.sibling;e!==null;)ol(e,t,r),e=e.sibling}function ll(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(ll(e,t,r),e=e.sibling;e!==null;)ll(e,t,r),e=e.sibling}var De=null,kt=!1;function tr(e,t,r){for(r=r.child;r!==null;)xc(e,t,r),r=r.sibling}function xc(e,t,r){if(Nt&&typeof Nt.onCommitFiberUnmount=="function")try{Nt.onCommitFiberUnmount(ps,r)}catch{}switch(r.tag){case 5:Be||Yr(r,t);case 6:var n=De,o=kt;De=null,tr(e,t,r),De=n,kt=o,De!==null&&(kt?(e=De,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):De.removeChild(r.stateNode));break;case 18:De!==null&&(kt?(e=De,r=r.stateNode,e.nodeType===8?_o(e.parentNode,r):e.nodeType===1&&_o(e,r),Pn(e)):_o(De,r.stateNode));break;case 4:n=De,o=kt,De=r.stateNode.containerInfo,kt=!0,tr(e,t,r),De=n,kt=o;break;case 0:case 11:case 14:case 15:if(!Be&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){o=n=n.next;do{var u=o,h=u.destroy;u=u.tag,h!==void 0&&((u&2)!==0||(u&4)!==0)&&sl(r,t,h),o=o.next}while(o!==n)}tr(e,t,r);break;case 1:if(!Be&&(Yr(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(v){Ce(r,t,v)}tr(e,t,r);break;case 21:tr(e,t,r);break;case 22:r.mode&1?(Be=(n=Be)||r.memoizedState!==null,tr(e,t,r),Be=n):tr(e,t,r);break;default:tr(e,t,r)}}function kc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new kh),t.forEach(function(n){var o=Oh.bind(null,e,n);r.has(n)||(r.add(n),n.then(o,o))})}}function St(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var o=r[n];try{var u=e,h=t,v=h;e:for(;v!==null;){switch(v.tag){case 5:De=v.stateNode,kt=!1;break e;case 3:De=v.stateNode.containerInfo,kt=!0;break e;case 4:De=v.stateNode.containerInfo,kt=!0;break e}v=v.return}if(De===null)throw Error(i(160));xc(u,h,o),De=null,kt=!1;var _=o.alternate;_!==null&&(_.return=null),o.return=null}catch(C){Ce(o,t,C)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Sc(t,e),t=t.sibling}function Sc(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(St(t,e),Ot(e),n&4){try{Kn(3,e,e.return),ti(3,e)}catch(W){Ce(e,e.return,W)}try{Kn(5,e,e.return)}catch(W){Ce(e,e.return,W)}}break;case 1:St(t,e),Ot(e),n&512&&r!==null&&Yr(r,r.return);break;case 5:if(St(t,e),Ot(e),n&512&&r!==null&&Yr(r,r.return),e.flags&32){var o=e.stateNode;try{gn(o,"")}catch(W){Ce(e,e.return,W)}}if(n&4&&(o=e.stateNode,o!=null)){var u=e.memoizedProps,h=r!==null?r.memoizedProps:u,v=e.type,_=e.updateQueue;if(e.updateQueue=null,_!==null)try{v==="input"&&u.type==="radio"&&u.name!=null&&Yl(o,u),Di(v,h);var C=Di(v,u);for(h=0;h<_.length;h+=2){var b=_[h],R=_[h+1];b==="style"?ia(o,R):b==="dangerouslySetInnerHTML"?na(o,R):b==="children"?gn(o,R):U(o,b,R,C)}switch(v){case"input":Oi(o,u);break;case"textarea":ea(o,u);break;case"select":var N=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!u.multiple;var M=u.value;M!=null?br(o,!!u.multiple,M,!1):N!==!!u.multiple&&(u.defaultValue!=null?br(o,!!u.multiple,u.defaultValue,!0):br(o,!!u.multiple,u.multiple?[]:"",!1))}o[zn]=u}catch(W){Ce(e,e.return,W)}}break;case 6:if(St(t,e),Ot(e),n&4){if(e.stateNode===null)throw Error(i(162));o=e.stateNode,u=e.memoizedProps;try{o.nodeValue=u}catch(W){Ce(e,e.return,W)}}break;case 3:if(St(t,e),Ot(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{Pn(t.containerInfo)}catch(W){Ce(e,e.return,W)}break;case 4:St(t,e),Ot(e);break;case 13:St(t,e),Ot(e),o=e.child,o.flags&8192&&(u=o.memoizedState!==null,o.stateNode.isHidden=u,!u||o.alternate!==null&&o.alternate.memoizedState!==null||(cl=Pe())),n&4&&kc(e);break;case 22:if(b=r!==null&&r.memoizedState!==null,e.mode&1?(Be=(C=Be)||b,St(t,e),Be=C):St(t,e),Ot(e),n&8192){if(C=e.memoizedState!==null,(e.stateNode.isHidden=C)&&!b&&(e.mode&1)!==0)for(V=e,b=e.child;b!==null;){for(R=V=b;V!==null;){switch(N=V,M=N.child,N.tag){case 0:case 11:case 14:case 15:Kn(4,N,N.return);break;case 1:Yr(N,N.return);var H=N.stateNode;if(typeof H.componentWillUnmount=="function"){n=N,r=N.return;try{t=n,H.props=t.memoizedProps,H.state=t.memoizedState,H.componentWillUnmount()}catch(W){Ce(n,r,W)}}break;case 5:Yr(N,N.return);break;case 22:if(N.memoizedState!==null){Cc(R);continue}}M!==null?(M.return=N,V=M):Cc(R)}b=b.sibling}e:for(b=null,R=e;;){if(R.tag===5){if(b===null){b=R;try{o=R.stateNode,C?(u=o.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none"):(v=R.stateNode,_=R.memoizedProps.style,h=_!=null&&_.hasOwnProperty("display")?_.display:null,v.style.display=sa("display",h))}catch(W){Ce(e,e.return,W)}}}else if(R.tag===6){if(b===null)try{R.stateNode.nodeValue=C?"":R.memoizedProps}catch(W){Ce(e,e.return,W)}}else if((R.tag!==22&&R.tag!==23||R.memoizedState===null||R===e)&&R.child!==null){R.child.return=R,R=R.child;continue}if(R===e)break e;for(;R.sibling===null;){if(R.return===null||R.return===e)break e;b===R&&(b=null),R=R.return}b===R&&(b=null),R.sibling.return=R.return,R=R.sibling}}break;case 19:St(t,e),Ot(e),n&4&&kc(e);break;case 21:break;default:St(t,e),Ot(e)}}function Ot(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(wc(r)){var n=r;break e}r=r.return}throw Error(i(160))}switch(n.tag){case 5:var o=n.stateNode;n.flags&32&&(gn(o,""),n.flags&=-33);var u=_c(e);ll(e,u,o);break;case 3:case 4:var h=n.stateNode.containerInfo,v=_c(e);ol(e,v,h);break;default:throw Error(i(161))}}catch(_){Ce(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Eh(e,t,r){V=e,Ec(e)}function Ec(e,t,r){for(var n=(e.mode&1)!==0;V!==null;){var o=V,u=o.child;if(o.tag===22&&n){var h=o.memoizedState!==null||ei;if(!h){var v=o.alternate,_=v!==null&&v.memoizedState!==null||Be;v=ei;var C=Be;if(ei=h,(Be=_)&&!C)for(V=o;V!==null;)h=V,_=h.child,h.tag===22&&h.memoizedState!==null?Pc(o):_!==null?(_.return=h,V=_):Pc(o);for(;u!==null;)V=u,Ec(u),u=u.sibling;V=o,ei=v,Be=C}jc(e)}else(o.subtreeFlags&8772)!==0&&u!==null?(u.return=o,V=u):jc(e)}}function jc(e){for(;V!==null;){var t=V;if((t.flags&8772)!==0){var r=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Be||ti(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!Be)if(r===null)n.componentDidMount();else{var o=t.elementType===t.type?r.memoizedProps:xt(t.type,r.memoizedProps);n.componentDidUpdate(o,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var u=t.updateQueue;u!==null&&Cu(t,u,n);break;case 3:var h=t.updateQueue;if(h!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}Cu(t,h,r)}break;case 5:var v=t.stateNode;if(r===null&&t.flags&4){r=v;var _=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":_.autoFocus&&r.focus();break;case"img":_.src&&(r.src=_.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var C=t.alternate;if(C!==null){var b=C.memoizedState;if(b!==null){var R=b.dehydrated;R!==null&&Pn(R)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}Be||t.flags&512&&il(t)}catch(N){Ce(t,t.return,N)}}if(t===e){V=null;break}if(r=t.sibling,r!==null){r.return=t.return,V=r;break}V=t.return}}function Cc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var r=t.sibling;if(r!==null){r.return=t.return,V=r;break}V=t.return}}function Pc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{ti(4,t)}catch(_){Ce(t,r,_)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var o=t.return;try{n.componentDidMount()}catch(_){Ce(t,o,_)}}var u=t.return;try{il(t)}catch(_){Ce(t,u,_)}break;case 5:var h=t.return;try{il(t)}catch(_){Ce(t,h,_)}}}catch(_){Ce(t,t.return,_)}if(t===e){V=null;break}var v=t.sibling;if(v!==null){v.return=t.return,V=v;break}V=t.return}}var jh=Math.ceil,ri=q.ReactCurrentDispatcher,al=q.ReactCurrentOwner,ft=q.ReactCurrentBatchConfig,le=0,$e=null,Te=null,ze=0,it=0,Xr=Jt(0),Re=0,Jn=null,_r=0,ni=0,ul=0,Yn=null,Je=null,cl=0,Zr=1/0,Ft=null,si=!1,dl=null,rr=null,ii=!1,nr=null,oi=0,Xn=0,fl=null,li=-1,ai=0;function qe(){return(le&6)!==0?Pe():li!==-1?li:li=Pe()}function sr(e){return(e.mode&1)===0?1:(le&2)!==0&&ze!==0?ze&-ze:ah.transition!==null?(ai===0&&(ai=_a()),ai):(e=he,e!==0||(e=window.event,e=e===void 0?16:Ta(e.type)),e)}function Et(e,t,r,n){if(50<Xn)throw Xn=0,fl=null,Error(i(185));kn(e,r,n),((le&2)===0||e!==$e)&&(e===$e&&((le&2)===0&&(ni|=r),Re===4&&ir(e,ze)),Ye(e,n),r===1&&le===0&&(t.mode&1)===0&&(Zr=Pe()+500,As&&Xt()))}function Ye(e,t){var r=e.callbackNode;af(e,t);var n=vs(e,e===$e?ze:0);if(n===0)r!==null&&va(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&va(r),t===1)e.tag===0?lh(Tc.bind(null,e)):pu(Tc.bind(null,e)),nh(function(){(le&6)===0&&Xt()}),r=null;else{switch(xa(n)){case 1:r=Hi;break;case 4:r=ya;break;case 16:r=hs;break;case 536870912:r=wa;break;default:r=hs}r=Dc(r,Nc.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function Nc(e,t){if(li=-1,ai=0,(le&6)!==0)throw Error(i(327));var r=e.callbackNode;if(en()&&e.callbackNode!==r)return null;var n=vs(e,e===$e?ze:0);if(n===0)return null;if((n&30)!==0||(n&e.expiredLanes)!==0||t)t=ui(e,n);else{t=n;var o=le;le|=2;var u=Rc();($e!==e||ze!==t)&&(Ft=null,Zr=Pe()+500,kr(e,t));do try{Nh();break}catch(v){bc(e,v)}while(!0);bo(),ri.current=u,le=o,Te!==null?t=0:($e=null,ze=0,t=Re)}if(t!==0){if(t===2&&(o=qi(e),o!==0&&(n=o,t=hl(e,o))),t===1)throw r=Jn,kr(e,0),ir(e,n),Ye(e,Pe()),r;if(t===6)ir(e,n);else{if(o=e.current.alternate,(n&30)===0&&!Ch(o)&&(t=ui(e,n),t===2&&(u=qi(e),u!==0&&(n=u,t=hl(e,u))),t===1))throw r=Jn,kr(e,0),ir(e,n),Ye(e,Pe()),r;switch(e.finishedWork=o,e.finishedLanes=n,t){case 0:case 1:throw Error(i(345));case 2:Sr(e,Je,Ft);break;case 3:if(ir(e,n),(n&130023424)===n&&(t=cl+500-Pe(),10<t)){if(vs(e,0)!==0)break;if(o=e.suspendedLanes,(o&n)!==n){qe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=wo(Sr.bind(null,e,Je,Ft),t);break}Sr(e,Je,Ft);break;case 4:if(ir(e,n),(n&4194240)===n)break;for(t=e.eventTimes,o=-1;0<n;){var h=31-yt(n);u=1<<h,h=t[h],h>o&&(o=h),n&=~u}if(n=o,n=Pe()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*jh(n/1960))-n,10<n){e.timeoutHandle=wo(Sr.bind(null,e,Je,Ft),n);break}Sr(e,Je,Ft);break;case 5:Sr(e,Je,Ft);break;default:throw Error(i(329))}}}return Ye(e,Pe()),e.callbackNode===r?Nc.bind(null,e):null}function hl(e,t){var r=Yn;return e.current.memoizedState.isDehydrated&&(kr(e,t).flags|=256),e=ui(e,t),e!==2&&(t=Je,Je=r,t!==null&&pl(t)),e}function pl(e){Je===null?Je=e:Je.push.apply(Je,e)}function Ch(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var o=r[n],u=o.getSnapshot;o=o.value;try{if(!wt(u(),o))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ir(e,t){for(t&=~ul,t&=~ni,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-yt(t),n=1<<r;e[r]=-1,t&=~n}}function Tc(e){if((le&6)!==0)throw Error(i(327));en();var t=vs(e,0);if((t&1)===0)return Ye(e,Pe()),null;var r=ui(e,t);if(e.tag!==0&&r===2){var n=qi(e);n!==0&&(t=n,r=hl(e,n))}if(r===1)throw r=Jn,kr(e,0),ir(e,t),Ye(e,Pe()),r;if(r===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sr(e,Je,Ft),Ye(e,Pe()),null}function ml(e,t){var r=le;le|=1;try{return e(t)}finally{le=r,le===0&&(Zr=Pe()+500,As&&Xt())}}function xr(e){nr!==null&&nr.tag===0&&(le&6)===0&&en();var t=le;le|=1;var r=ft.transition,n=he;try{if(ft.transition=null,he=1,e)return e()}finally{he=n,ft.transition=r,le=t,(le&6)===0&&Xt()}}function gl(){it=Xr.current,we(Xr)}function kr(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,rh(r)),Te!==null)for(r=Te.return;r!==null;){var n=r;switch(jo(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&Is();break;case 3:Kr(),we(Ge),we(Me),zo();break;case 5:Ao(n);break;case 4:Kr();break;case 13:we(Se);break;case 19:we(Se);break;case 10:Ro(n.type._context);break;case 22:case 23:gl()}r=r.return}if($e=e,Te=e=or(e.current,null),ze=it=t,Re=0,Jn=null,ul=ni=_r=0,Je=Yn=null,vr!==null){for(t=0;t<vr.length;t++)if(r=vr[t],n=r.interleaved,n!==null){r.interleaved=null;var o=n.next,u=r.pending;if(u!==null){var h=u.next;u.next=o,n.next=h}r.pending=n}vr=null}return e}function bc(e,t){do{var r=Te;try{if(bo(),Ws.current=Js,Gs){for(var n=Ee.memoizedState;n!==null;){var o=n.queue;o!==null&&(o.pending=null),n=n.next}Gs=!1}if(wr=0,Oe=be=Ee=null,Hn=!1,qn=0,al.current=null,r===null||r.return===null){Re=1,Jn=t,Te=null;break}e:{var u=e,h=r.return,v=r,_=t;if(t=ze,v.flags|=32768,_!==null&&typeof _=="object"&&typeof _.then=="function"){var C=_,b=v,R=b.tag;if((b.mode&1)===0&&(R===0||R===11||R===15)){var N=b.alternate;N?(b.updateQueue=N.updateQueue,b.memoizedState=N.memoizedState,b.lanes=N.lanes):(b.updateQueue=null,b.memoizedState=null)}var M=tc(h);if(M!==null){M.flags&=-257,rc(M,h,v,u,t),M.mode&1&&ec(u,C,t),t=M,_=C;var H=t.updateQueue;if(H===null){var W=new Set;W.add(_),t.updateQueue=W}else H.add(_);break e}else{if((t&1)===0){ec(u,C,t),vl();break e}_=Error(i(426))}}else if(ke&&v.mode&1){var Ne=tc(h);if(Ne!==null){(Ne.flags&65536)===0&&(Ne.flags|=256),rc(Ne,h,v,u,t),No(Jr(_,v));break e}}u=_=Jr(_,v),Re!==4&&(Re=2),Yn===null?Yn=[u]:Yn.push(u),u=h;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var S=Xu(u,_,t);ju(u,S);break e;case 1:v=_;var x=u.type,j=u.stateNode;if((u.flags&128)===0&&(typeof x.getDerivedStateFromError=="function"||j!==null&&typeof j.componentDidCatch=="function"&&(rr===null||!rr.has(j)))){u.flags|=65536,t&=-t,u.lanes|=t;var I=Zu(u,v,t);ju(u,I);break e}}u=u.return}while(u!==null)}$c(r)}catch(G){t=G,Te===r&&r!==null&&(Te=r=r.return);continue}break}while(!0)}function Rc(){var e=ri.current;return ri.current=Js,e===null?Js:e}function vl(){(Re===0||Re===3||Re===2)&&(Re=4),$e===null||(_r&268435455)===0&&(ni&268435455)===0||ir($e,ze)}function ui(e,t){var r=le;le|=2;var n=Rc();($e!==e||ze!==t)&&(Ft=null,kr(e,t));do try{Ph();break}catch(o){bc(e,o)}while(!0);if(bo(),le=r,ri.current=n,Te!==null)throw Error(i(261));return $e=null,ze=0,Re}function Ph(){for(;Te!==null;)Oc(Te)}function Nh(){for(;Te!==null&&!Xd();)Oc(Te)}function Oc(e){var t=Ac(e.alternate,e,it);e.memoizedProps=e.pendingProps,t===null?$c(e):Te=t,al.current=null}function $c(e){var t=e;do{var r=t.alternate;if(e=t.return,(t.flags&32768)===0){if(r=_h(r,t,it),r!==null){Te=r;return}}else{if(r=xh(r,t),r!==null){r.flags&=32767,Te=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Re=6,Te=null;return}}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);Re===0&&(Re=5)}function Sr(e,t,r){var n=he,o=ft.transition;try{ft.transition=null,he=1,Th(e,t,r,n)}finally{ft.transition=o,he=n}return null}function Th(e,t,r,n){do en();while(nr!==null);if((le&6)!==0)throw Error(i(327));r=e.finishedWork;var o=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var u=r.lanes|r.childLanes;if(uf(e,u),e===$e&&(Te=$e=null,ze=0),(r.subtreeFlags&2064)===0&&(r.flags&2064)===0||ii||(ii=!0,Dc(hs,function(){return en(),null})),u=(r.flags&15990)!==0,(r.subtreeFlags&15990)!==0||u){u=ft.transition,ft.transition=null;var h=he;he=1;var v=le;le|=4,al.current=null,Sh(e,r),Sc(r,e),Kf(vo),_s=!!go,vo=go=null,e.current=r,Eh(r),Zd(),le=v,he=h,ft.transition=u}else e.current=r;if(ii&&(ii=!1,nr=e,oi=o),u=e.pendingLanes,u===0&&(rr=null),rf(r.stateNode),Ye(e,Pe()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)o=t[r],n(o.value,{componentStack:o.stack,digest:o.digest});if(si)throw si=!1,e=dl,dl=null,e;return(oi&1)!==0&&e.tag!==0&&en(),u=e.pendingLanes,(u&1)!==0?e===fl?Xn++:(Xn=0,fl=e):Xn=0,Xt(),null}function en(){if(nr!==null){var e=xa(oi),t=ft.transition,r=he;try{if(ft.transition=null,he=16>e?16:e,nr===null)var n=!1;else{if(e=nr,nr=null,oi=0,(le&6)!==0)throw Error(i(331));var o=le;for(le|=4,V=e.current;V!==null;){var u=V,h=u.child;if((V.flags&16)!==0){var v=u.deletions;if(v!==null){for(var _=0;_<v.length;_++){var C=v[_];for(V=C;V!==null;){var b=V;switch(b.tag){case 0:case 11:case 15:Kn(8,b,u)}var R=b.child;if(R!==null)R.return=b,V=R;else for(;V!==null;){b=V;var N=b.sibling,M=b.return;if(yc(b),b===C){V=null;break}if(N!==null){N.return=M,V=N;break}V=M}}}var H=u.alternate;if(H!==null){var W=H.child;if(W!==null){H.child=null;do{var Ne=W.sibling;W.sibling=null,W=Ne}while(W!==null)}}V=u}}if((u.subtreeFlags&2064)!==0&&h!==null)h.return=u,V=h;else e:for(;V!==null;){if(u=V,(u.flags&2048)!==0)switch(u.tag){case 0:case 11:case 15:Kn(9,u,u.return)}var S=u.sibling;if(S!==null){S.return=u.return,V=S;break e}V=u.return}}var x=e.current;for(V=x;V!==null;){h=V;var j=h.child;if((h.subtreeFlags&2064)!==0&&j!==null)j.return=h,V=j;else e:for(h=x;V!==null;){if(v=V,(v.flags&2048)!==0)try{switch(v.tag){case 0:case 11:case 15:ti(9,v)}}catch(G){Ce(v,v.return,G)}if(v===h){V=null;break e}var I=v.sibling;if(I!==null){I.return=v.return,V=I;break e}V=v.return}}if(le=o,Xt(),Nt&&typeof Nt.onPostCommitFiberRoot=="function")try{Nt.onPostCommitFiberRoot(ps,e)}catch{}n=!0}return n}finally{he=r,ft.transition=t}}return!1}function Ic(e,t,r){t=Jr(r,t),t=Xu(e,t,1),e=er(e,t,1),t=qe(),e!==null&&(kn(e,1,t),Ye(e,t))}function Ce(e,t,r){if(e.tag===3)Ic(e,e,r);else for(;t!==null;){if(t.tag===3){Ic(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(rr===null||!rr.has(n))){e=Jr(r,e),e=Zu(t,e,1),t=er(t,e,1),e=qe(),t!==null&&(kn(t,1,e),Ye(t,e));break}}t=t.return}}function bh(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=qe(),e.pingedLanes|=e.suspendedLanes&r,$e===e&&(ze&r)===r&&(Re===4||Re===3&&(ze&130023424)===ze&&500>Pe()-cl?kr(e,0):ul|=r),Ye(e,t)}function Lc(e,t){t===0&&((e.mode&1)===0?t=1:(t=gs,gs<<=1,(gs&130023424)===0&&(gs=4194304)));var r=qe();e=zt(e,t),e!==null&&(kn(e,t,r),Ye(e,r))}function Rh(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),Lc(e,r)}function Oh(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,o=e.memoizedState;o!==null&&(r=o.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(i(314))}n!==null&&n.delete(t),Lc(e,r)}var Ac;Ac=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Ke=!0;else{if((e.lanes&r)===0&&(t.flags&128)===0)return Ke=!1,wh(e,t,r);Ke=(e.flags&131072)!==0}else Ke=!1,ke&&(t.flags&1048576)!==0&&mu(t,zs,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Zs(e,t),e=t.pendingProps;var o=Vr(t,Me.current);Qr(t,r),o=Fo(null,t,n,e,o,r);var u=Vo();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(n)?(u=!0,Ls(t)):u=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Io(t),o.updater=Ys,t.stateNode=o,o._reactInternals=t,Qo(t,n,e,r),t=Xo(null,t,n,!0,u,r)):(t.tag=0,ke&&u&&Eo(t),He(null,t,o,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Zs(e,t),e=t.pendingProps,o=n._init,n=o(n._payload),t.type=n,o=t.tag=Ih(n),e=xt(n,e),o){case 0:t=Yo(null,t,n,e,r);break e;case 1:t=ac(null,t,n,e,r);break e;case 11:t=nc(null,t,n,e,r);break e;case 14:t=sc(null,t,n,xt(n.type,e),r);break e}throw Error(i(306,n,""))}return t;case 0:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:xt(n,o),Yo(e,t,n,o,r);case 1:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:xt(n,o),ac(e,t,n,o,r);case 3:e:{if(uc(t),e===null)throw Error(i(387));n=t.pendingProps,u=t.memoizedState,o=u.element,Eu(e,t),Hs(t,n,null,r);var h=t.memoizedState;if(n=h.element,u.isDehydrated)if(u={element:n,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){o=Jr(Error(i(423)),t),t=cc(e,t,n,r,o);break e}else if(n!==o){o=Jr(Error(i(424)),t),t=cc(e,t,n,r,o);break e}else for(st=Kt(t.stateNode.containerInfo.firstChild),nt=t,ke=!0,_t=null,r=ku(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(qr(),n===o){t=Mt(e,t,r);break e}He(e,t,n,r)}t=t.child}return t;case 5:return Pu(t),e===null&&Po(t),n=t.type,o=t.pendingProps,u=e!==null?e.memoizedProps:null,h=o.children,yo(n,o)?h=null:u!==null&&yo(n,u)&&(t.flags|=32),lc(e,t),He(e,t,h,r),t.child;case 6:return e===null&&Po(t),null;case 13:return dc(e,t,r);case 4:return Lo(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=Wr(t,null,n,r):He(e,t,n,r),t.child;case 11:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:xt(n,o),nc(e,t,n,o,r);case 7:return He(e,t,t.pendingProps,r),t.child;case 8:return He(e,t,t.pendingProps.children,r),t.child;case 12:return He(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,o=t.pendingProps,u=t.memoizedProps,h=o.value,ge(Fs,n._currentValue),n._currentValue=h,u!==null)if(wt(u.value,h)){if(u.children===o.children&&!Ge.current){t=Mt(e,t,r);break e}}else for(u=t.child,u!==null&&(u.return=t);u!==null;){var v=u.dependencies;if(v!==null){h=u.child;for(var _=v.firstContext;_!==null;){if(_.context===n){if(u.tag===1){_=Ut(-1,r&-r),_.tag=2;var C=u.updateQueue;if(C!==null){C=C.shared;var b=C.pending;b===null?_.next=_:(_.next=b.next,b.next=_),C.pending=_}}u.lanes|=r,_=u.alternate,_!==null&&(_.lanes|=r),Oo(u.return,r,t),v.lanes|=r;break}_=_.next}}else if(u.tag===10)h=u.type===t.type?null:u.child;else if(u.tag===18){if(h=u.return,h===null)throw Error(i(341));h.lanes|=r,v=h.alternate,v!==null&&(v.lanes|=r),Oo(h,r,t),h=u.sibling}else h=u.child;if(h!==null)h.return=u;else for(h=u;h!==null;){if(h===t){h=null;break}if(u=h.sibling,u!==null){u.return=h.return,h=u;break}h=h.return}u=h}He(e,t,o.children,r),t=t.child}return t;case 9:return o=t.type,n=t.pendingProps.children,Qr(t,r),o=ct(o),n=n(o),t.flags|=1,He(e,t,n,r),t.child;case 14:return n=t.type,o=xt(n,t.pendingProps),o=xt(n.type,o),sc(e,t,n,o,r);case 15:return ic(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,o=t.pendingProps,o=t.elementType===n?o:xt(n,o),Zs(e,t),t.tag=1,Qe(n)?(e=!0,Ls(t)):e=!1,Qr(t,r),Ju(t,n,o),Qo(t,n,o,r),Xo(null,t,n,!0,e,r);case 19:return hc(e,t,r);case 22:return oc(e,t,r)}throw Error(i(156,t.tag))};function Dc(e,t){return ga(e,t)}function $h(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,r,n){return new $h(e,t,r,n)}function yl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ih(e){if(typeof e=="function")return yl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===gt)return 11;if(e===Pt)return 14}return 2}function or(e,t){var r=e.alternate;return r===null?(r=ht(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ci(e,t,r,n,o,u){var h=2;if(n=e,typeof e=="function")yl(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case X:return Er(r.children,o,u,t);case oe:h=8,o|=8;break;case pe:return e=ht(12,r,t,o|2),e.elementType=pe,e.lanes=u,e;case et:return e=ht(13,r,t,o),e.elementType=et,e.lanes=u,e;case vt:return e=ht(19,r,t,o),e.elementType=vt,e.lanes=u,e;case je:return di(r,o,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _e:h=10;break e;case lt:h=9;break e;case gt:h=11;break e;case Pt:h=14;break e;case We:h=16,n=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return t=ht(h,r,t,o),t.elementType=e,t.type=n,t.lanes=u,t}function Er(e,t,r,n){return e=ht(7,e,n,t),e.lanes=r,e}function di(e,t,r,n){return e=ht(22,e,n,t),e.elementType=je,e.lanes=r,e.stateNode={isHidden:!1},e}function wl(e,t,r){return e=ht(6,e,null,t),e.lanes=r,e}function _l(e,t,r){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Lh(e,t,r,n,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Wi(0),this.expirationTimes=Wi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wi(0),this.identifierPrefix=n,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function xl(e,t,r,n,o,u,h,v,_){return e=new Lh(e,t,r,v,_),t===1?(t=1,u===!0&&(t|=8)):t=0,u=ht(3,null,null,t),e.current=u,u.stateNode=e,u.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Io(u),e}function Ah(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:A,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function zc(e){if(!e)return Yt;e=e._reactInternals;e:{if(fr(e)!==e||e.tag!==1)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(i(171))}if(e.tag===1){var r=e.type;if(Qe(r))return fu(e,r,t)}return t}function Uc(e,t,r,n,o,u,h,v,_){return e=xl(r,n,!0,e,o,u,h,v,_),e.context=zc(null),r=e.current,n=qe(),o=sr(r),u=Ut(n,o),u.callback=t??null,er(r,u,o),e.current.lanes=o,kn(e,o,n),Ye(e,n),e}function fi(e,t,r,n){var o=t.current,u=qe(),h=sr(o);return r=zc(r),t.context===null?t.context=r:t.pendingContext=r,t=Ut(u,h),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=er(o,t,h),e!==null&&(Et(e,o,h,u),Bs(e,o,h)),h}function hi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Mc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function kl(e,t){Mc(e,t),(e=e.alternate)&&Mc(e,t)}function Dh(){return null}var Fc=typeof reportError=="function"?reportError:function(e){console.error(e)};function Sl(e){this._internalRoot=e}pi.prototype.render=Sl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));fi(e,t,null,null)},pi.prototype.unmount=Sl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xr(function(){fi(null,e,null,null)}),t[It]=null}};function pi(e){this._internalRoot=e}pi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ea();e={blockedOn:null,target:e,priority:t};for(var r=0;r<Wt.length&&t!==0&&t<Wt[r].priority;r++);Wt.splice(r,0,e),r===0&&Pa(e)}};function El(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function mi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Vc(){}function zh(e,t,r,n,o){if(o){if(typeof n=="function"){var u=n;n=function(){var C=hi(h);u.call(C)}}var h=Uc(t,n,e,0,null,!1,!1,"",Vc);return e._reactRootContainer=h,e[It]=h.current,An(e.nodeType===8?e.parentNode:e),xr(),h}for(;o=e.lastChild;)e.removeChild(o);if(typeof n=="function"){var v=n;n=function(){var C=hi(_);v.call(C)}}var _=xl(e,0,!1,null,null,!1,!1,"",Vc);return e._reactRootContainer=_,e[It]=_.current,An(e.nodeType===8?e.parentNode:e),xr(function(){fi(t,_,r,n)}),_}function gi(e,t,r,n,o){var u=r._reactRootContainer;if(u){var h=u;if(typeof o=="function"){var v=o;o=function(){var _=hi(h);v.call(_)}}fi(t,h,e,o)}else h=zh(r,t,e,o,n);return hi(h)}ka=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=xn(t.pendingLanes);r!==0&&(Gi(t,r|1),Ye(t,Pe()),(le&6)===0&&(Zr=Pe()+500,Xt()))}break;case 13:xr(function(){var n=zt(e,1);if(n!==null){var o=qe();Et(n,e,1,o)}}),kl(e,1)}},Qi=function(e){if(e.tag===13){var t=zt(e,134217728);if(t!==null){var r=qe();Et(t,e,134217728,r)}kl(e,134217728)}},Sa=function(e){if(e.tag===13){var t=sr(e),r=zt(e,t);if(r!==null){var n=qe();Et(r,e,t,n)}kl(e,t)}},Ea=function(){return he},ja=function(e,t){var r=he;try{return he=e,t()}finally{he=r}},Mi=function(e,t,r){switch(t){case"input":if(Oi(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var o=$s(n);if(!o)throw Error(i(90));Kl(n),Oi(n,o)}}}break;case"textarea":ea(e,r);break;case"select":t=r.value,t!=null&&br(e,!!r.multiple,t,!1)}},ua=ml,ca=xr;var Uh={usingClientEntryPoint:!1,Events:[Un,Mr,$s,la,aa,ml]},Zn={findFiberByHostInstance:hr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Mh={bundleType:Zn.bundleType,version:Zn.version,rendererPackageName:Zn.rendererPackageName,rendererConfig:Zn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=pa(e),e===null?null:e.stateNode},findFiberByHostInstance:Zn.findFiberByHostInstance||Dh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var vi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!vi.isDisabled&&vi.supportsFiber)try{ps=vi.inject(Mh),Nt=vi}catch{}}return Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Uh,Xe.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!El(t))throw Error(i(200));return Ah(e,t,null,r)},Xe.createRoot=function(e,t){if(!El(e))throw Error(i(299));var r=!1,n="",o=Fc;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=xl(e,1,!1,null,null,r,!1,n,o),e[It]=t.current,An(e.nodeType===8?e.parentNode:e),new Sl(t)},Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=pa(t),e=e===null?null:e.stateNode,e},Xe.flushSync=function(e){return xr(e)},Xe.hydrate=function(e,t,r){if(!mi(t))throw Error(i(200));return gi(null,e,t,!0,r)},Xe.hydrateRoot=function(e,t,r){if(!El(e))throw Error(i(405));var n=r!=null&&r.hydratedSources||null,o=!1,u="",h=Fc;if(r!=null&&(r.unstable_strictMode===!0&&(o=!0),r.identifierPrefix!==void 0&&(u=r.identifierPrefix),r.onRecoverableError!==void 0&&(h=r.onRecoverableError)),t=Uc(t,null,e,1,r??null,o,!1,u,h),e[It]=t.current,An(e),n)for(e=0;e<n.length;e++)r=n[e],o=r._getVersion,o=o(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,o]:t.mutableSourceEagerHydrationData.push(r,o);return new pi(t)},Xe.render=function(e,t,r){if(!mi(t))throw Error(i(200));return gi(null,e,t,!1,r)},Xe.unmountComponentAtNode=function(e){if(!mi(e))throw Error(i(40));return e._reactRootContainer?(xr(function(){gi(null,null,e,!1,function(){e._reactRootContainer=null,e[It]=null})}),!0):!1},Xe.unstable_batchedUpdates=ml,Xe.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!mi(r))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return gi(e,t,r,!1,n)},Xe.version="18.3.1-next-f1338f8080-20240426",Xe}var Jc;function Xh(){if(Jc)return Pl.exports;Jc=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Pl.exports=Yh(),Pl.exports}var Yc;function Zh(){if(Yc)return wi;Yc=1;var c=Xh();return wi.createRoot=c.createRoot,wi.hydrateRoot=c.hydrateRoot,wi}var ep=Zh();const tp="modulepreload",rp=function(c){return"/"+c},Xc={},fn=function(s,i,l){let a=Promise.resolve();if(i&&i.length>0){document.getElementsByTagName("link");const f=document.querySelector("meta[property=csp-nonce]"),p=(f==null?void 0:f.nonce)||(f==null?void 0:f.getAttribute("nonce"));a=Promise.allSettled(i.map(m=>{if(m=rp(m),m in Xc)return;Xc[m]=!0;const g=m.endsWith(".css"),y=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${y}`))return;const E=document.createElement("link");if(E.rel=g?"stylesheet":tp,g||(E.as="script"),E.crossOrigin="",E.href=m,p&&E.setAttribute("nonce",p),document.head.appendChild(E),g)return new Promise((P,O)=>{E.addEventListener("load",P),E.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${m}`)))})}))}function d(f){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=f,window.dispatchEvent(p),!p.defaultPrevented)throw f}return a.then(f=>{for(const p of f||[])p.status==="rejected"&&d(p.reason);return s().catch(d)})},np=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>fn(async()=>{const{default:l}=await Promise.resolve().then(()=>pn);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)};class Hl extends Error{constructor(s,i="FunctionsError",l){super(s),this.name=i,this.context=l}}class sp extends Hl{constructor(s){super("Failed to send a request to the Edge Function","FunctionsFetchError",s)}}class ip extends Hl{constructor(s){super("Relay Error invoking the Edge Function","FunctionsRelayError",s)}}class op extends Hl{constructor(s){super("Edge Function returned a non-2xx status code","FunctionsHttpError",s)}}var Ll;(function(c){c.Any="any",c.ApNortheast1="ap-northeast-1",c.ApNortheast2="ap-northeast-2",c.ApSouth1="ap-south-1",c.ApSoutheast1="ap-southeast-1",c.ApSoutheast2="ap-southeast-2",c.CaCentral1="ca-central-1",c.EuCentral1="eu-central-1",c.EuWest1="eu-west-1",c.EuWest2="eu-west-2",c.EuWest3="eu-west-3",c.SaEast1="sa-east-1",c.UsEast1="us-east-1",c.UsWest1="us-west-1",c.UsWest2="us-west-2"})(Ll||(Ll={}));var lp=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};class ap{constructor(s,{headers:i={},customFetch:l,region:a=Ll.Any}={}){this.url=s,this.headers=i,this.region=a,this.fetch=np(l)}setAuth(s){this.headers.Authorization=`Bearer ${s}`}invoke(s,i={}){var l;return lp(this,void 0,void 0,function*(){try{const{headers:a,method:d,body:f}=i;let p={},{region:m}=i;m||(m=this.region),m&&m!=="any"&&(p["x-region"]=m);let g;f&&(a&&!Object.prototype.hasOwnProperty.call(a,"Content-Type")||!a)&&(typeof Blob<"u"&&f instanceof Blob||f instanceof ArrayBuffer?(p["Content-Type"]="application/octet-stream",g=f):typeof f=="string"?(p["Content-Type"]="text/plain",g=f):typeof FormData<"u"&&f instanceof FormData?g=f:(p["Content-Type"]="application/json",g=JSON.stringify(f)));const y=yield this.fetch(`${this.url}/${s}`,{method:d||"POST",headers:Object.assign(Object.assign(Object.assign({},p),this.headers),a),body:g}).catch(L=>{throw new sp(L)}),E=y.headers.get("x-relay-error");if(E&&E==="true")throw new ip(y);if(!y.ok)throw new op(y);let P=((l=y.headers.get("Content-Type"))!==null&&l!==void 0?l:"text/plain").split(";")[0].trim(),O;return P==="application/json"?O=yield y.json():P==="application/octet-stream"?O=yield y.blob():P==="text/event-stream"?O=y:P==="multipart/form-data"?O=yield y.formData():O=yield y.text(),{data:O,error:null}}catch(a){return{data:null,error:a}}})}}var Le={},tn={},rn={},nn={},sn={},on={},up=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},hn=up();const cp=hn.fetch,xd=hn.fetch.bind(hn),kd=hn.Headers,dp=hn.Request,fp=hn.Response,pn=Object.freeze(Object.defineProperty({__proto__:null,Headers:kd,Request:dp,Response:fp,default:xd,fetch:cp},Symbol.toStringTag,{value:"Module"})),hp=qh(pn);var _i={},Zc;function Sd(){if(Zc)return _i;Zc=1,Object.defineProperty(_i,"__esModule",{value:!0});class c extends Error{constructor(i){super(i.message),this.name="PostgrestError",this.details=i.details,this.hint=i.hint,this.code=i.code}}return _i.default=c,_i}var ed;function Ed(){if(ed)return on;ed=1;var c=on&&on.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(on,"__esModule",{value:!0});const s=c(hp),i=c(Sd());class l{constructor(d){this.shouldThrowOnError=!1,this.method=d.method,this.url=d.url,this.headers=d.headers,this.schema=d.schema,this.body=d.body,this.shouldThrowOnError=d.shouldThrowOnError,this.signal=d.signal,this.isMaybeSingle=d.isMaybeSingle,d.fetch?this.fetch=d.fetch:typeof fetch>"u"?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(d,f){return this.headers=Object.assign({},this.headers),this.headers[d]=f,this}then(d,f){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const p=this.fetch;let m=p(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async g=>{var y,E,P;let O=null,L=null,F=null,D=g.status,re=g.statusText;if(g.ok){if(this.method!=="HEAD"){const $=await g.text();$===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?L=$:L=JSON.parse($))}const U=(y=this.headers.Prefer)===null||y===void 0?void 0:y.match(/count=(exact|planned|estimated)/),q=(E=g.headers.get("content-range"))===null||E===void 0?void 0:E.split("/");U&&q&&q.length>1&&(F=parseInt(q[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(L)&&(L.length>1?(O={code:"PGRST116",details:`Results contain ${L.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},L=null,F=null,D=406,re="Not Acceptable"):L.length===1?L=L[0]:L=null)}else{const U=await g.text();try{O=JSON.parse(U),Array.isArray(O)&&g.status===404&&(L=[],O=null,D=200,re="OK")}catch{g.status===404&&U===""?(D=204,re="No Content"):O={message:U}}if(O&&this.isMaybeSingle&&(!((P=O==null?void 0:O.details)===null||P===void 0)&&P.includes("0 rows"))&&(O=null,D=200,re="OK"),O&&this.shouldThrowOnError)throw new i.default(O)}return{error:O,data:L,count:F,status:D,statusText:re}});return this.shouldThrowOnError||(m=m.catch(g=>{var y,E,P;return{error:{message:`${(y=g==null?void 0:g.name)!==null&&y!==void 0?y:"FetchError"}: ${g==null?void 0:g.message}`,details:`${(E=g==null?void 0:g.stack)!==null&&E!==void 0?E:""}`,hint:"",code:`${(P=g==null?void 0:g.code)!==null&&P!==void 0?P:""}`},data:null,count:null,status:0,statusText:""}})),m.then(d,f)}returns(){return this}overrideTypes(){return this}}return on.default=l,on}var td;function jd(){if(td)return sn;td=1;var c=sn&&sn.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(sn,"__esModule",{value:!0});const s=c(Ed());class i extends s.default{select(a){let d=!1;const f=(a??"*").split("").map(p=>/\s/.test(p)&&!d?"":(p==='"'&&(d=!d),p)).join("");return this.url.searchParams.set("select",f),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(a,{ascending:d=!0,nullsFirst:f,foreignTable:p,referencedTable:m=p}={}){const g=m?`${m}.order`:"order",y=this.url.searchParams.get(g);return this.url.searchParams.set(g,`${y?`${y},`:""}${a}.${d?"asc":"desc"}${f===void 0?"":f?".nullsfirst":".nullslast"}`),this}limit(a,{foreignTable:d,referencedTable:f=d}={}){const p=typeof f>"u"?"limit":`${f}.limit`;return this.url.searchParams.set(p,`${a}`),this}range(a,d,{foreignTable:f,referencedTable:p=f}={}){const m=typeof p>"u"?"offset":`${p}.offset`,g=typeof p>"u"?"limit":`${p}.limit`;return this.url.searchParams.set(m,`${a}`),this.url.searchParams.set(g,`${d-a+1}`),this}abortSignal(a){return this.signal=a,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:a=!1,verbose:d=!1,settings:f=!1,buffers:p=!1,wal:m=!1,format:g="text"}={}){var y;const E=[a?"analyze":null,d?"verbose":null,f?"settings":null,p?"buffers":null,m?"wal":null].filter(Boolean).join("|"),P=(y=this.headers.Accept)!==null&&y!==void 0?y:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${g}; for="${P}"; options=${E};`,g==="json"?this:this}rollback(){var a;return((a=this.headers.Prefer)!==null&&a!==void 0?a:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return sn.default=i,sn}var rd;function ql(){if(rd)return nn;rd=1;var c=nn&&nn.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(nn,"__esModule",{value:!0});const s=c(jd());class i extends s.default{eq(a,d){return this.url.searchParams.append(a,`eq.${d}`),this}neq(a,d){return this.url.searchParams.append(a,`neq.${d}`),this}gt(a,d){return this.url.searchParams.append(a,`gt.${d}`),this}gte(a,d){return this.url.searchParams.append(a,`gte.${d}`),this}lt(a,d){return this.url.searchParams.append(a,`lt.${d}`),this}lte(a,d){return this.url.searchParams.append(a,`lte.${d}`),this}like(a,d){return this.url.searchParams.append(a,`like.${d}`),this}likeAllOf(a,d){return this.url.searchParams.append(a,`like(all).{${d.join(",")}}`),this}likeAnyOf(a,d){return this.url.searchParams.append(a,`like(any).{${d.join(",")}}`),this}ilike(a,d){return this.url.searchParams.append(a,`ilike.${d}`),this}ilikeAllOf(a,d){return this.url.searchParams.append(a,`ilike(all).{${d.join(",")}}`),this}ilikeAnyOf(a,d){return this.url.searchParams.append(a,`ilike(any).{${d.join(",")}}`),this}is(a,d){return this.url.searchParams.append(a,`is.${d}`),this}in(a,d){const f=Array.from(new Set(d)).map(p=>typeof p=="string"&&new RegExp("[,()]").test(p)?`"${p}"`:`${p}`).join(",");return this.url.searchParams.append(a,`in.(${f})`),this}contains(a,d){return typeof d=="string"?this.url.searchParams.append(a,`cs.${d}`):Array.isArray(d)?this.url.searchParams.append(a,`cs.{${d.join(",")}}`):this.url.searchParams.append(a,`cs.${JSON.stringify(d)}`),this}containedBy(a,d){return typeof d=="string"?this.url.searchParams.append(a,`cd.${d}`):Array.isArray(d)?this.url.searchParams.append(a,`cd.{${d.join(",")}}`):this.url.searchParams.append(a,`cd.${JSON.stringify(d)}`),this}rangeGt(a,d){return this.url.searchParams.append(a,`sr.${d}`),this}rangeGte(a,d){return this.url.searchParams.append(a,`nxl.${d}`),this}rangeLt(a,d){return this.url.searchParams.append(a,`sl.${d}`),this}rangeLte(a,d){return this.url.searchParams.append(a,`nxr.${d}`),this}rangeAdjacent(a,d){return this.url.searchParams.append(a,`adj.${d}`),this}overlaps(a,d){return typeof d=="string"?this.url.searchParams.append(a,`ov.${d}`):this.url.searchParams.append(a,`ov.{${d.join(",")}}`),this}textSearch(a,d,{config:f,type:p}={}){let m="";p==="plain"?m="pl":p==="phrase"?m="ph":p==="websearch"&&(m="w");const g=f===void 0?"":`(${f})`;return this.url.searchParams.append(a,`${m}fts${g}.${d}`),this}match(a){return Object.entries(a).forEach(([d,f])=>{this.url.searchParams.append(d,`eq.${f}`)}),this}not(a,d,f){return this.url.searchParams.append(a,`not.${d}.${f}`),this}or(a,{foreignTable:d,referencedTable:f=d}={}){const p=f?`${f}.or`:"or";return this.url.searchParams.append(p,`(${a})`),this}filter(a,d,f){return this.url.searchParams.append(a,`${d}.${f}`),this}}return nn.default=i,nn}var nd;function Cd(){if(nd)return rn;nd=1;var c=rn&&rn.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(rn,"__esModule",{value:!0});const s=c(ql());class i{constructor(a,{headers:d={},schema:f,fetch:p}){this.url=a,this.headers=d,this.schema=f,this.fetch=p}select(a,{head:d=!1,count:f}={}){const p=d?"HEAD":"GET";let m=!1;const g=(a??"*").split("").map(y=>/\s/.test(y)&&!m?"":(y==='"'&&(m=!m),y)).join("");return this.url.searchParams.set("select",g),f&&(this.headers.Prefer=`count=${f}`),new s.default({method:p,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(a,{count:d,defaultToNull:f=!0}={}){const p="POST",m=[];if(this.headers.Prefer&&m.push(this.headers.Prefer),d&&m.push(`count=${d}`),f||m.push("missing=default"),this.headers.Prefer=m.join(","),Array.isArray(a)){const g=a.reduce((y,E)=>y.concat(Object.keys(E)),[]);if(g.length>0){const y=[...new Set(g)].map(E=>`"${E}"`);this.url.searchParams.set("columns",y.join(","))}}return new s.default({method:p,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}upsert(a,{onConflict:d,ignoreDuplicates:f=!1,count:p,defaultToNull:m=!0}={}){const g="POST",y=[`resolution=${f?"ignore":"merge"}-duplicates`];if(d!==void 0&&this.url.searchParams.set("on_conflict",d),this.headers.Prefer&&y.push(this.headers.Prefer),p&&y.push(`count=${p}`),m||y.push("missing=default"),this.headers.Prefer=y.join(","),Array.isArray(a)){const E=a.reduce((P,O)=>P.concat(Object.keys(O)),[]);if(E.length>0){const P=[...new Set(E)].map(O=>`"${O}"`);this.url.searchParams.set("columns",P.join(","))}}return new s.default({method:g,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(a,{count:d}={}){const f="PATCH",p=[];return this.headers.Prefer&&p.push(this.headers.Prefer),d&&p.push(`count=${d}`),this.headers.Prefer=p.join(","),new s.default({method:f,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}delete({count:a}={}){const d="DELETE",f=[];return a&&f.push(`count=${a}`),this.headers.Prefer&&f.unshift(this.headers.Prefer),this.headers.Prefer=f.join(","),new s.default({method:d,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return rn.default=i,rn}var ts={},rs={},sd;function pp(){return sd||(sd=1,Object.defineProperty(rs,"__esModule",{value:!0}),rs.version=void 0,rs.version="0.0.0-automated"),rs}var id;function mp(){if(id)return ts;id=1,Object.defineProperty(ts,"__esModule",{value:!0}),ts.DEFAULT_HEADERS=void 0;const c=pp();return ts.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${c.version}`},ts}var od;function gp(){if(od)return tn;od=1;var c=tn&&tn.__importDefault||function(d){return d&&d.__esModule?d:{default:d}};Object.defineProperty(tn,"__esModule",{value:!0});const s=c(Cd()),i=c(ql()),l=mp();class a{constructor(f,{headers:p={},schema:m,fetch:g}={}){this.url=f,this.headers=Object.assign(Object.assign({},l.DEFAULT_HEADERS),p),this.schemaName=m,this.fetch=g}from(f){const p=new URL(`${this.url}/${f}`);return new s.default(p,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(f){return new a(this.url,{headers:this.headers,schema:f,fetch:this.fetch})}rpc(f,p={},{head:m=!1,get:g=!1,count:y}={}){let E;const P=new URL(`${this.url}/rpc/${f}`);let O;m||g?(E=m?"HEAD":"GET",Object.entries(p).filter(([F,D])=>D!==void 0).map(([F,D])=>[F,Array.isArray(D)?`{${D.join(",")}}`:`${D}`]).forEach(([F,D])=>{P.searchParams.append(F,D)})):(E="POST",O=p);const L=Object.assign({},this.headers);return y&&(L.Prefer=`count=${y}`),new i.default({method:E,url:P,headers:L,schema:this.schemaName,body:O,fetch:this.fetch,allowEmpty:!1})}}return tn.default=a,tn}var ld;function vp(){if(ld)return Le;ld=1;var c=Le&&Le.__importDefault||function(p){return p&&p.__esModule?p:{default:p}};Object.defineProperty(Le,"__esModule",{value:!0}),Le.PostgrestError=Le.PostgrestBuilder=Le.PostgrestTransformBuilder=Le.PostgrestFilterBuilder=Le.PostgrestQueryBuilder=Le.PostgrestClient=void 0;const s=c(gp());Le.PostgrestClient=s.default;const i=c(Cd());Le.PostgrestQueryBuilder=i.default;const l=c(ql());Le.PostgrestFilterBuilder=l.default;const a=c(jd());Le.PostgrestTransformBuilder=a.default;const d=c(Ed());Le.PostgrestBuilder=d.default;const f=c(Sd());return Le.PostgrestError=f.default,Le.default={PostgrestClient:s.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:l.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:d.default,PostgrestError:f.default},Le}var yp=vp();const wp=Hh(yp),{PostgrestClient:_p,PostgrestQueryBuilder:Ng,PostgrestFilterBuilder:Tg,PostgrestTransformBuilder:bg,PostgrestBuilder:Rg,PostgrestError:Og}=wp,xp="2.11.2",kp={"X-Client-Info":`realtime-js/${xp}`},Sp="1.0.0",Pd=1e4,Ep=1e3;var dn;(function(c){c[c.connecting=0]="connecting",c[c.open=1]="open",c[c.closing=2]="closing",c[c.closed=3]="closed"})(dn||(dn={}));var ot;(function(c){c.closed="closed",c.errored="errored",c.joined="joined",c.joining="joining",c.leaving="leaving"})(ot||(ot={}));var jt;(function(c){c.close="phx_close",c.error="phx_error",c.join="phx_join",c.reply="phx_reply",c.leave="phx_leave",c.access_token="access_token"})(jt||(jt={}));var Al;(function(c){c.websocket="websocket"})(Al||(Al={}));var Pr;(function(c){c.Connecting="connecting",c.Open="open",c.Closing="closing",c.Closed="closed"})(Pr||(Pr={}));class jp{constructor(){this.HEADER_LENGTH=1}decode(s,i){return s.constructor===ArrayBuffer?i(this._binaryDecode(s)):i(typeof s=="string"?JSON.parse(s):{})}_binaryDecode(s){const i=new DataView(s),l=new TextDecoder;return this._decodeBroadcast(s,i,l)}_decodeBroadcast(s,i,l){const a=i.getUint8(1),d=i.getUint8(2);let f=this.HEADER_LENGTH+2;const p=l.decode(s.slice(f,f+a));f=f+a;const m=l.decode(s.slice(f,f+d));f=f+d;const g=JSON.parse(l.decode(s.slice(f,s.byteLength)));return{ref:null,topic:p,event:m,payload:g}}}class Nd{constructor(s,i){this.callback=s,this.timerCalc=i,this.timer=void 0,this.tries=0,this.callback=s,this.timerCalc=i}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var ve;(function(c){c.abstime="abstime",c.bool="bool",c.date="date",c.daterange="daterange",c.float4="float4",c.float8="float8",c.int2="int2",c.int4="int4",c.int4range="int4range",c.int8="int8",c.int8range="int8range",c.json="json",c.jsonb="jsonb",c.money="money",c.numeric="numeric",c.oid="oid",c.reltime="reltime",c.text="text",c.time="time",c.timestamp="timestamp",c.timestamptz="timestamptz",c.timetz="timetz",c.tsrange="tsrange",c.tstzrange="tstzrange"})(ve||(ve={}));const ad=(c,s,i={})=>{var l;const a=(l=i.skipTypes)!==null&&l!==void 0?l:[];return Object.keys(s).reduce((d,f)=>(d[f]=Cp(f,c,s,a),d),{})},Cp=(c,s,i,l)=>{const a=s.find(p=>p.name===c),d=a==null?void 0:a.type,f=i[c];return d&&!l.includes(d)?Td(d,f):Dl(f)},Td=(c,s)=>{if(c.charAt(0)==="_"){const i=c.slice(1,c.length);return bp(s,i)}switch(c){case ve.bool:return Pp(s);case ve.float4:case ve.float8:case ve.int2:case ve.int4:case ve.int8:case ve.numeric:case ve.oid:return Np(s);case ve.json:case ve.jsonb:return Tp(s);case ve.timestamp:return Rp(s);case ve.abstime:case ve.date:case ve.daterange:case ve.int4range:case ve.int8range:case ve.money:case ve.reltime:case ve.text:case ve.time:case ve.timestamptz:case ve.timetz:case ve.tsrange:case ve.tstzrange:return Dl(s);default:return Dl(s)}},Dl=c=>c,Pp=c=>{switch(c){case"t":return!0;case"f":return!1;default:return c}},Np=c=>{if(typeof c=="string"){const s=parseFloat(c);if(!Number.isNaN(s))return s}return c},Tp=c=>{if(typeof c=="string")try{return JSON.parse(c)}catch(s){return console.log(`JSON parse error: ${s}`),c}return c},bp=(c,s)=>{if(typeof c!="string")return c;const i=c.length-1,l=c[i];if(c[0]==="{"&&l==="}"){let d;const f=c.slice(1,i);try{d=JSON.parse("["+f+"]")}catch{d=f?f.split(","):[]}return d.map(p=>Td(s,p))}return c},Rp=c=>typeof c=="string"?c.replace(" ","T"):c,bd=c=>{let s=c;return s=s.replace(/^ws/i,"http"),s=s.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),s.replace(/\/+$/,"")};class bl{constructor(s,i,l={},a=Pd){this.channel=s,this.event=i,this.payload=l,this.timeout=a,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(s){this.timeout=s,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(s){this.payload=Object.assign(Object.assign({},this.payload),s)}receive(s,i){var l;return this._hasReceived(s)&&i((l=this.receivedResp)===null||l===void 0?void 0:l.response),this.recHooks.push({status:s,callback:i}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const s=i=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=i,this._matchReceive(i)};this.channel._on(this.refEvent,{},s),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(s,i){this.refEvent&&this.channel._trigger(this.refEvent,{status:s,response:i})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:s,response:i}){this.recHooks.filter(l=>l.status===s).forEach(l=>l.callback(i))}_hasReceived(s){return this.receivedResp&&this.receivedResp.status===s}}var ud;(function(c){c.SYNC="sync",c.JOIN="join",c.LEAVE="leave"})(ud||(ud={}));class ss{constructor(s,i){this.channel=s,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const l=(i==null?void 0:i.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(l.state,{},a=>{const{onJoin:d,onLeave:f,onSync:p}=this.caller;this.joinRef=this.channel._joinRef(),this.state=ss.syncState(this.state,a,d,f),this.pendingDiffs.forEach(m=>{this.state=ss.syncDiff(this.state,m,d,f)}),this.pendingDiffs=[],p()}),this.channel._on(l.diff,{},a=>{const{onJoin:d,onLeave:f,onSync:p}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(a):(this.state=ss.syncDiff(this.state,a,d,f),p())}),this.onJoin((a,d,f)=>{this.channel._trigger("presence",{event:"join",key:a,currentPresences:d,newPresences:f})}),this.onLeave((a,d,f)=>{this.channel._trigger("presence",{event:"leave",key:a,currentPresences:d,leftPresences:f})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(s,i,l,a){const d=this.cloneDeep(s),f=this.transformState(i),p={},m={};return this.map(d,(g,y)=>{f[g]||(m[g]=y)}),this.map(f,(g,y)=>{const E=d[g];if(E){const P=y.map(D=>D.presence_ref),O=E.map(D=>D.presence_ref),L=y.filter(D=>O.indexOf(D.presence_ref)<0),F=E.filter(D=>P.indexOf(D.presence_ref)<0);L.length>0&&(p[g]=L),F.length>0&&(m[g]=F)}else p[g]=y}),this.syncDiff(d,{joins:p,leaves:m},l,a)}static syncDiff(s,i,l,a){const{joins:d,leaves:f}={joins:this.transformState(i.joins),leaves:this.transformState(i.leaves)};return l||(l=()=>{}),a||(a=()=>{}),this.map(d,(p,m)=>{var g;const y=(g=s[p])!==null&&g!==void 0?g:[];if(s[p]=this.cloneDeep(m),y.length>0){const E=s[p].map(O=>O.presence_ref),P=y.filter(O=>E.indexOf(O.presence_ref)<0);s[p].unshift(...P)}l(p,y,m)}),this.map(f,(p,m)=>{let g=s[p];if(!g)return;const y=m.map(E=>E.presence_ref);g=g.filter(E=>y.indexOf(E.presence_ref)<0),s[p]=g,a(p,g,m),g.length===0&&delete s[p]}),s}static map(s,i){return Object.getOwnPropertyNames(s).map(l=>i(l,s[l]))}static transformState(s){return s=this.cloneDeep(s),Object.getOwnPropertyNames(s).reduce((i,l)=>{const a=s[l];return"metas"in a?i[l]=a.metas.map(d=>(d.presence_ref=d.phx_ref,delete d.phx_ref,delete d.phx_ref_prev,d)):i[l]=a,i},{})}static cloneDeep(s){return JSON.parse(JSON.stringify(s))}onJoin(s){this.caller.onJoin=s}onLeave(s){this.caller.onLeave=s}onSync(s){this.caller.onSync=s}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var cd;(function(c){c.ALL="*",c.INSERT="INSERT",c.UPDATE="UPDATE",c.DELETE="DELETE"})(cd||(cd={}));var dd;(function(c){c.BROADCAST="broadcast",c.PRESENCE="presence",c.POSTGRES_CHANGES="postgres_changes",c.SYSTEM="system"})(dd||(dd={}));var Vt;(function(c){c.SUBSCRIBED="SUBSCRIBED",c.TIMED_OUT="TIMED_OUT",c.CLOSED="CLOSED",c.CHANNEL_ERROR="CHANNEL_ERROR"})(Vt||(Vt={}));class Wl{constructor(s,i={config:{}},l){this.topic=s,this.params=i,this.socket=l,this.bindings={},this.state=ot.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=s.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},i.config),this.timeout=this.socket.timeout,this.joinPush=new bl(this,jt.join,this.params,this.timeout),this.rejoinTimer=new Nd(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ot.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(a=>a.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ot.closed,this.socket._remove(this)}),this._onError(a=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,a),this.state=ot.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ot.errored,this.rejoinTimer.scheduleTimeout())}),this._on(jt.reply,{},(a,d)=>{this._trigger(this._replyEventName(d),a)}),this.presence=new ss(this),this.broadcastEndpointURL=bd(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(s,i=this.timeout){var l,a;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:d,presence:f,private:p}}=this.params;this._onError(y=>s==null?void 0:s(Vt.CHANNEL_ERROR,y)),this._onClose(()=>s==null?void 0:s(Vt.CLOSED));const m={},g={broadcast:d,presence:f,postgres_changes:(a=(l=this.bindings.postgres_changes)===null||l===void 0?void 0:l.map(y=>y.filter))!==null&&a!==void 0?a:[],private:p};this.socket.accessTokenValue&&(m.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:g},m)),this.joinedOnce=!0,this._rejoin(i),this.joinPush.receive("ok",async({postgres_changes:y})=>{var E;if(this.socket.setAuth(),y===void 0){s==null||s(Vt.SUBSCRIBED);return}else{const P=this.bindings.postgres_changes,O=(E=P==null?void 0:P.length)!==null&&E!==void 0?E:0,L=[];for(let F=0;F<O;F++){const D=P[F],{filter:{event:re,schema:ae,table:U,filter:q}}=D,$=y&&y[F];if($&&$.event===re&&$.schema===ae&&$.table===U&&$.filter===q)L.push(Object.assign(Object.assign({},D),{id:$.id}));else{this.unsubscribe(),s==null||s(Vt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=L,s&&s(Vt.SUBSCRIBED);return}}).receive("error",y=>{s==null||s(Vt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(y).join(", ")||"error")))}).receive("timeout",()=>{s==null||s(Vt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(s,i={}){return await this.send({type:"presence",event:"track",payload:s},i.timeout||this.timeout)}async untrack(s={}){return await this.send({type:"presence",event:"untrack"},s)}on(s,i,l){return this._on(s,i,l)}async send(s,i={}){var l,a;if(!this._canPush()&&s.type==="broadcast"){const{event:d,payload:f}=s,m={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:d,payload:f,private:this.private}]})};try{const g=await this._fetchWithTimeout(this.broadcastEndpointURL,m,(l=i.timeout)!==null&&l!==void 0?l:this.timeout);return await((a=g.body)===null||a===void 0?void 0:a.cancel()),g.ok?"ok":"error"}catch(g){return g.name==="AbortError"?"timed out":"error"}}else return new Promise(d=>{var f,p,m;const g=this._push(s.type,s,i.timeout||this.timeout);s.type==="broadcast"&&!(!((m=(p=(f=this.params)===null||f===void 0?void 0:f.config)===null||p===void 0?void 0:p.broadcast)===null||m===void 0)&&m.ack)&&d("ok"),g.receive("ok",()=>d("ok")),g.receive("error",()=>d("error")),g.receive("timeout",()=>d("timed out"))})}updateJoinPayload(s){this.joinPush.updatePayload(s)}unsubscribe(s=this.timeout){this.state=ot.leaving;const i=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(jt.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(l=>{const a=new bl(this,jt.leave,{},s);a.receive("ok",()=>{i(),l("ok")}).receive("timeout",()=>{i(),l("timed out")}).receive("error",()=>{l("error")}),a.send(),this._canPush()||a.trigger("ok",{})})}async _fetchWithTimeout(s,i,l){const a=new AbortController,d=setTimeout(()=>a.abort(),l),f=await this.socket.fetch(s,Object.assign(Object.assign({},i),{signal:a.signal}));return clearTimeout(d),f}_push(s,i,l=this.timeout){if(!this.joinedOnce)throw`tried to push '${s}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let a=new bl(this,s,i,l);return this._canPush()?a.send():(a.startTimeout(),this.pushBuffer.push(a)),a}_onMessage(s,i,l){return i}_isMember(s){return this.topic===s}_joinRef(){return this.joinPush.ref}_trigger(s,i,l){var a,d;const f=s.toLocaleLowerCase(),{close:p,error:m,leave:g,join:y}=jt;if(l&&[p,m,g,y].indexOf(f)>=0&&l!==this._joinRef())return;let P=this._onMessage(f,i,l);if(i&&!P)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?(a=this.bindings.postgres_changes)===null||a===void 0||a.filter(O=>{var L,F,D;return((L=O.filter)===null||L===void 0?void 0:L.event)==="*"||((D=(F=O.filter)===null||F===void 0?void 0:F.event)===null||D===void 0?void 0:D.toLocaleLowerCase())===f}).map(O=>O.callback(P,l)):(d=this.bindings[f])===null||d===void 0||d.filter(O=>{var L,F,D,re,ae,U;if(["broadcast","presence","postgres_changes"].includes(f))if("id"in O){const q=O.id,$=(L=O.filter)===null||L===void 0?void 0:L.event;return q&&((F=i.ids)===null||F===void 0?void 0:F.includes(q))&&($==="*"||($==null?void 0:$.toLocaleLowerCase())===((D=i.data)===null||D===void 0?void 0:D.type.toLocaleLowerCase()))}else{const q=(ae=(re=O==null?void 0:O.filter)===null||re===void 0?void 0:re.event)===null||ae===void 0?void 0:ae.toLocaleLowerCase();return q==="*"||q===((U=i==null?void 0:i.event)===null||U===void 0?void 0:U.toLocaleLowerCase())}else return O.type.toLocaleLowerCase()===f}).map(O=>{if(typeof P=="object"&&"ids"in P){const L=P.data,{schema:F,table:D,commit_timestamp:re,type:ae,errors:U}=L;P=Object.assign(Object.assign({},{schema:F,table:D,commit_timestamp:re,eventType:ae,new:{},old:{},errors:U}),this._getPayloadRecords(L))}O.callback(P,l)})}_isClosed(){return this.state===ot.closed}_isJoined(){return this.state===ot.joined}_isJoining(){return this.state===ot.joining}_isLeaving(){return this.state===ot.leaving}_replyEventName(s){return`chan_reply_${s}`}_on(s,i,l){const a=s.toLocaleLowerCase(),d={type:a,filter:i,callback:l};return this.bindings[a]?this.bindings[a].push(d):this.bindings[a]=[d],this}_off(s,i){const l=s.toLocaleLowerCase();return this.bindings[l]=this.bindings[l].filter(a=>{var d;return!(((d=a.type)===null||d===void 0?void 0:d.toLocaleLowerCase())===l&&Wl.isEqual(a.filter,i))}),this}static isEqual(s,i){if(Object.keys(s).length!==Object.keys(i).length)return!1;for(const l in s)if(s[l]!==i[l])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(s){this._on(jt.close,{},s)}_onError(s){this._on(jt.error,{},i=>s(i))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(s=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ot.joining,this.joinPush.resend(s))}_getPayloadRecords(s){const i={new:{},old:{}};return(s.type==="INSERT"||s.type==="UPDATE")&&(i.new=ad(s.columns,s.record)),(s.type==="UPDATE"||s.type==="DELETE")&&(i.old=ad(s.columns,s.old_record)),i}}const Op=()=>{},$p=typeof WebSocket<"u",Ip=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Lp{constructor(s,i){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=kp,this.params={},this.timeout=Pd,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Op,this.conn=null,this.sendBuffer=[],this.serializer=new jp,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=d=>{let f;return d?f=d:typeof fetch>"u"?f=(...p)=>fn(async()=>{const{default:m}=await Promise.resolve().then(()=>pn);return{default:m}},void 0).then(({default:m})=>m(...p)):f=fetch,(...p)=>f(...p)},this.endPoint=`${s}/${Al.websocket}`,this.httpEndpoint=bd(s),i!=null&&i.transport?this.transport=i.transport:this.transport=null,i!=null&&i.params&&(this.params=i.params),i!=null&&i.headers&&(this.headers=Object.assign(Object.assign({},this.headers),i.headers)),i!=null&&i.timeout&&(this.timeout=i.timeout),i!=null&&i.logger&&(this.logger=i.logger),i!=null&&i.heartbeatIntervalMs&&(this.heartbeatIntervalMs=i.heartbeatIntervalMs);const a=(l=i==null?void 0:i.params)===null||l===void 0?void 0:l.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=i!=null&&i.reconnectAfterMs?i.reconnectAfterMs:d=>[1e3,2e3,5e3,1e4][d-1]||1e4,this.encode=i!=null&&i.encode?i.encode:(d,f)=>f(JSON.stringify(d)),this.decode=i!=null&&i.decode?i.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Nd(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(i==null?void 0:i.fetch),i!=null&&i.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(i==null?void 0:i.worker)||!1,this.workerUrl=i==null?void 0:i.workerUrl}this.accessToken=(i==null?void 0:i.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if($p){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new Ap(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),fn(async()=>{const{default:s}=await import("./browser-BcsEyRPb.js").then(i=>i.b);return{default:s}},[]).then(({default:s})=>{this.conn=new s(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:Sp}))}disconnect(s,i){this.conn&&(this.conn.onclose=function(){},s?this.conn.close(s,i??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(s){const i=await s.unsubscribe();return this.channels.length===0&&this.disconnect(),i}async removeAllChannels(){const s=await Promise.all(this.channels.map(i=>i.unsubscribe()));return this.disconnect(),s}log(s,i,l){this.logger(s,i,l)}connectionState(){switch(this.conn&&this.conn.readyState){case dn.connecting:return Pr.Connecting;case dn.open:return Pr.Open;case dn.closing:return Pr.Closing;default:return Pr.Closed}}isConnected(){return this.connectionState()===Pr.Open}channel(s,i={config:{}}){const l=new Wl(`realtime:${s}`,i,this);return this.channels.push(l),l}push(s){const{topic:i,event:l,payload:a,ref:d}=s,f=()=>{this.encode(s,p=>{var m;(m=this.conn)===null||m===void 0||m.send(p)})};this.log("push",`${i} ${l} (${d})`,a),this.isConnected()?f():this.sendBuffer.push(f)}async setAuth(s=null){let i=s||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(i){let l=null;try{l=JSON.parse(atob(i.split(".")[1]))}catch{}if(l&&l.exp&&!(Math.floor(Date.now()/1e3)-l.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${l.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${l.exp}`);this.accessTokenValue=i,this.channels.forEach(a=>{i&&a.updateJoinPayload({access_token:i}),a.joinedOnce&&a._isJoined()&&a._push(jt.access_token,{access_token:i})})}}async sendHeartbeat(){var s;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(s=this.conn)===null||s===void 0||s.close(Ep,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(s=>s()),this.sendBuffer=[])}_makeRef(){let s=this.ref+1;return s===this.ref?this.ref=0:this.ref=s,this.ref.toString()}_leaveOpenTopic(s){let i=this.channels.find(l=>l.topic===s&&(l._isJoined()||l._isJoining()));i&&(this.log("transport",`leaving duplicate topic "${s}"`),i.unsubscribe())}_remove(s){this.channels=this.channels.filter(i=>i._joinRef()!==s._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=s=>this._onConnError(s),this.conn.onmessage=s=>this._onConnMessage(s),this.conn.onclose=s=>this._onConnClose(s))}_onConnMessage(s){this.decode(s.data,i=>{let{topic:l,event:a,payload:d,ref:f}=i;f&&f===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${d.status||""} ${l} ${a} ${f&&"("+f+")"||""}`,d),this.channels.filter(p=>p._isMember(l)).forEach(p=>p._trigger(a,d,f)),this.stateChangeCallbacks.message.forEach(p=>p(i))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const s=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(s),this.workerRef.onerror=i=>{this.log("worker","worker error",i.message),this.workerRef.terminate()},this.workerRef.onmessage=i=>{i.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(s=>s())}_onConnClose(s){this.log("transport","close",s),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(i=>i(s))}_onConnError(s){this.log("transport",s.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(i=>i(s))}_triggerChanError(){this.channels.forEach(s=>s._trigger(jt.error))}_appendParams(s,i){if(Object.keys(i).length===0)return s;const l=s.match(/\?/)?"&":"?",a=new URLSearchParams(i);return`${s}${l}${a}`}_workerObjectUrl(s){let i;if(s)i=s;else{const l=new Blob([Ip],{type:"application/javascript"});i=URL.createObjectURL(l)}return i}}class Ap{constructor(s,i,l){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=dn.connecting,this.send=()=>{},this.url=null,this.url=s,this.close=l.close}}class Gl extends Error{constructor(s){super(s),this.__isStorageError=!0,this.name="StorageError"}}function Ae(c){return typeof c=="object"&&c!==null&&"__isStorageError"in c}class Dp extends Gl{constructor(s,i){super(s),this.name="StorageApiError",this.status=i}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class zl extends Gl{constructor(s,i){super(s),this.name="StorageUnknownError",this.originalError=i}}var zp=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};const Rd=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>fn(async()=>{const{default:l}=await Promise.resolve().then(()=>pn);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)},Up=()=>zp(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield fn(()=>Promise.resolve().then(()=>pn),void 0)).Response:Response}),Ul=c=>{if(Array.isArray(c))return c.map(i=>Ul(i));if(typeof c=="function"||c!==Object(c))return c;const s={};return Object.entries(c).forEach(([i,l])=>{const a=i.replace(/([-_][a-z])/gi,d=>d.toUpperCase().replace(/[-_]/g,""));s[a]=Ul(l)}),s};var Nr=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};const Rl=c=>c.msg||c.message||c.error_description||c.error||JSON.stringify(c),Mp=(c,s,i)=>Nr(void 0,void 0,void 0,function*(){const l=yield Up();c instanceof l&&!(i!=null&&i.noResolveJson)?c.json().then(a=>{s(new Dp(Rl(a),c.status||500))}).catch(a=>{s(new zl(Rl(a),a))}):s(new zl(Rl(c),c))}),Fp=(c,s,i,l)=>{const a={method:c,headers:(s==null?void 0:s.headers)||{}};return c==="GET"?a:(a.headers=Object.assign({"Content-Type":"application/json"},s==null?void 0:s.headers),l&&(a.body=JSON.stringify(l)),Object.assign(Object.assign({},a),i))};function ls(c,s,i,l,a,d){return Nr(this,void 0,void 0,function*(){return new Promise((f,p)=>{c(i,Fp(s,l,a,d)).then(m=>{if(!m.ok)throw m;return l!=null&&l.noResolveJson?m:m.json()}).then(m=>f(m)).catch(m=>Mp(m,p,l))})})}function Ci(c,s,i,l){return Nr(this,void 0,void 0,function*(){return ls(c,"GET",s,i,l)})}function cr(c,s,i,l,a){return Nr(this,void 0,void 0,function*(){return ls(c,"POST",s,l,a,i)})}function Vp(c,s,i,l,a){return Nr(this,void 0,void 0,function*(){return ls(c,"PUT",s,l,a,i)})}function Bp(c,s,i,l){return Nr(this,void 0,void 0,function*(){return ls(c,"HEAD",s,Object.assign(Object.assign({},i),{noResolveJson:!0}),l)})}function Od(c,s,i,l,a){return Nr(this,void 0,void 0,function*(){return ls(c,"DELETE",s,l,a,i)})}var Ze=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};const Hp={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},fd={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class qp{constructor(s,i={},l,a){this.url=s,this.headers=i,this.bucketId=l,this.fetch=Rd(a)}uploadOrUpdate(s,i,l,a){return Ze(this,void 0,void 0,function*(){try{let d;const f=Object.assign(Object.assign({},fd),a);let p=Object.assign(Object.assign({},this.headers),s==="POST"&&{"x-upsert":String(f.upsert)});const m=f.metadata;typeof Blob<"u"&&l instanceof Blob?(d=new FormData,d.append("cacheControl",f.cacheControl),m&&d.append("metadata",this.encodeMetadata(m)),d.append("",l)):typeof FormData<"u"&&l instanceof FormData?(d=l,d.append("cacheControl",f.cacheControl),m&&d.append("metadata",this.encodeMetadata(m))):(d=l,p["cache-control"]=`max-age=${f.cacheControl}`,p["content-type"]=f.contentType,m&&(p["x-metadata"]=this.toBase64(this.encodeMetadata(m)))),a!=null&&a.headers&&(p=Object.assign(Object.assign({},p),a.headers));const g=this._removeEmptyFolders(i),y=this._getFinalPath(g),E=yield this.fetch(`${this.url}/object/${y}`,Object.assign({method:s,body:d,headers:p},f!=null&&f.duplex?{duplex:f.duplex}:{})),P=yield E.json();return E.ok?{data:{path:g,id:P.Id,fullPath:P.Key},error:null}:{data:null,error:P}}catch(d){if(Ae(d))return{data:null,error:d};throw d}})}upload(s,i,l){return Ze(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",s,i,l)})}uploadToSignedUrl(s,i,l,a){return Ze(this,void 0,void 0,function*(){const d=this._removeEmptyFolders(s),f=this._getFinalPath(d),p=new URL(this.url+`/object/upload/sign/${f}`);p.searchParams.set("token",i);try{let m;const g=Object.assign({upsert:fd.upsert},a),y=Object.assign(Object.assign({},this.headers),{"x-upsert":String(g.upsert)});typeof Blob<"u"&&l instanceof Blob?(m=new FormData,m.append("cacheControl",g.cacheControl),m.append("",l)):typeof FormData<"u"&&l instanceof FormData?(m=l,m.append("cacheControl",g.cacheControl)):(m=l,y["cache-control"]=`max-age=${g.cacheControl}`,y["content-type"]=g.contentType);const E=yield this.fetch(p.toString(),{method:"PUT",body:m,headers:y}),P=yield E.json();return E.ok?{data:{path:d,fullPath:P.Key},error:null}:{data:null,error:P}}catch(m){if(Ae(m))return{data:null,error:m};throw m}})}createSignedUploadUrl(s,i){return Ze(this,void 0,void 0,function*(){try{let l=this._getFinalPath(s);const a=Object.assign({},this.headers);i!=null&&i.upsert&&(a["x-upsert"]="true");const d=yield cr(this.fetch,`${this.url}/object/upload/sign/${l}`,{},{headers:a}),f=new URL(this.url+d.url),p=f.searchParams.get("token");if(!p)throw new Gl("No token returned by API");return{data:{signedUrl:f.toString(),path:s,token:p},error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}update(s,i,l){return Ze(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",s,i,l)})}move(s,i,l){return Ze(this,void 0,void 0,function*(){try{return{data:yield cr(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:s,destinationKey:i,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers}),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}copy(s,i,l){return Ze(this,void 0,void 0,function*(){try{return{data:{path:(yield cr(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:s,destinationKey:i,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers})).Key},error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrl(s,i,l){return Ze(this,void 0,void 0,function*(){try{let a=this._getFinalPath(s),d=yield cr(this.fetch,`${this.url}/object/sign/${a}`,Object.assign({expiresIn:i},l!=null&&l.transform?{transform:l.transform}:{}),{headers:this.headers});const f=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return d={signedUrl:encodeURI(`${this.url}${d.signedURL}${f}`)},{data:d,error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrls(s,i,l){return Ze(this,void 0,void 0,function*(){try{const a=yield cr(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:i,paths:s},{headers:this.headers}),d=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return{data:a.map(f=>Object.assign(Object.assign({},f),{signedUrl:f.signedURL?encodeURI(`${this.url}${f.signedURL}${d}`):null})),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}download(s,i){return Ze(this,void 0,void 0,function*(){const a=typeof(i==null?void 0:i.transform)<"u"?"render/image/authenticated":"object",d=this.transformOptsToQueryString((i==null?void 0:i.transform)||{}),f=d?`?${d}`:"";try{const p=this._getFinalPath(s);return{data:yield(yield Ci(this.fetch,`${this.url}/${a}/${p}${f}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(p){if(Ae(p))return{data:null,error:p};throw p}})}info(s){return Ze(this,void 0,void 0,function*(){const i=this._getFinalPath(s);try{const l=yield Ci(this.fetch,`${this.url}/object/info/${i}`,{headers:this.headers});return{data:Ul(l),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}exists(s){return Ze(this,void 0,void 0,function*(){const i=this._getFinalPath(s);try{return yield Bp(this.fetch,`${this.url}/object/${i}`,{headers:this.headers}),{data:!0,error:null}}catch(l){if(Ae(l)&&l instanceof zl){const a=l.originalError;if([400,404].includes(a==null?void 0:a.status))return{data:!1,error:l}}throw l}})}getPublicUrl(s,i){const l=this._getFinalPath(s),a=[],d=i!=null&&i.download?`download=${i.download===!0?"":i.download}`:"";d!==""&&a.push(d);const p=typeof(i==null?void 0:i.transform)<"u"?"render/image":"object",m=this.transformOptsToQueryString((i==null?void 0:i.transform)||{});m!==""&&a.push(m);let g=a.join("&");return g!==""&&(g=`?${g}`),{data:{publicUrl:encodeURI(`${this.url}/${p}/public/${l}${g}`)}}}remove(s){return Ze(this,void 0,void 0,function*(){try{return{data:yield Od(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:s},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}list(s,i,l){return Ze(this,void 0,void 0,function*(){try{const a=Object.assign(Object.assign(Object.assign({},Hp),i),{prefix:s||""});return{data:yield cr(this.fetch,`${this.url}/object/list/${this.bucketId}`,a,{headers:this.headers},l),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}encodeMetadata(s){return JSON.stringify(s)}toBase64(s){return typeof Buffer<"u"?Buffer.from(s).toString("base64"):btoa(s)}_getFinalPath(s){return`${this.bucketId}/${s}`}_removeEmptyFolders(s){return s.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(s){const i=[];return s.width&&i.push(`width=${s.width}`),s.height&&i.push(`height=${s.height}`),s.resize&&i.push(`resize=${s.resize}`),s.format&&i.push(`format=${s.format}`),s.quality&&i.push(`quality=${s.quality}`),i.join("&")}}const Wp="2.7.1",Gp={"X-Client-Info":`storage-js/${Wp}`};var ln=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};class Qp{constructor(s,i={},l){this.url=s,this.headers=Object.assign(Object.assign({},Gp),i),this.fetch=Rd(l)}listBuckets(){return ln(this,void 0,void 0,function*(){try{return{data:yield Ci(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(s){if(Ae(s))return{data:null,error:s};throw s}})}getBucket(s){return ln(this,void 0,void 0,function*(){try{return{data:yield Ci(this.fetch,`${this.url}/bucket/${s}`,{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}createBucket(s,i={public:!1}){return ln(this,void 0,void 0,function*(){try{return{data:yield cr(this.fetch,`${this.url}/bucket`,{id:s,name:s,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}updateBucket(s,i){return ln(this,void 0,void 0,function*(){try{return{data:yield Vp(this.fetch,`${this.url}/bucket/${s}`,{id:s,name:s,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}emptyBucket(s){return ln(this,void 0,void 0,function*(){try{return{data:yield cr(this.fetch,`${this.url}/bucket/${s}/empty`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}deleteBucket(s){return ln(this,void 0,void 0,function*(){try{return{data:yield Od(this.fetch,`${this.url}/bucket/${s}`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}}class Kp extends Qp{constructor(s,i={},l){super(s,i,l)}from(s){return new qp(this.url,this.headers,s,this.fetch)}}const Jp="2.49.1";let ns="";typeof Deno<"u"?ns="deno":typeof document<"u"?ns="web":typeof navigator<"u"&&navigator.product==="ReactNative"?ns="react-native":ns="node";const Yp={"X-Client-Info":`supabase-js-${ns}/${Jp}`},Xp={headers:Yp},Zp={schema:"public"},em={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},tm={};var rm=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};const nm=c=>{let s;return c?s=c:typeof fetch>"u"?s=xd:s=fetch,(...i)=>s(...i)},sm=()=>typeof Headers>"u"?kd:Headers,im=(c,s,i)=>{const l=nm(i),a=sm();return(d,f)=>rm(void 0,void 0,void 0,function*(){var p;const m=(p=yield s())!==null&&p!==void 0?p:c;let g=new a(f==null?void 0:f.headers);return g.has("apikey")||g.set("apikey",c),g.has("Authorization")||g.set("Authorization",`Bearer ${m}`),l(d,Object.assign(Object.assign({},f),{headers:g}))})};var om=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};function lm(c){return c.replace(/\/$/,"")}function am(c,s){const{db:i,auth:l,realtime:a,global:d}=c,{db:f,auth:p,realtime:m,global:g}=s,y={db:Object.assign(Object.assign({},f),i),auth:Object.assign(Object.assign({},p),l),realtime:Object.assign(Object.assign({},m),a),global:Object.assign(Object.assign({},g),d),accessToken:()=>om(this,void 0,void 0,function*(){return""})};return c.accessToken?y.accessToken=c.accessToken:delete y.accessToken,y}const $d="2.68.0",cn=30*1e3,Ml=3,Ol=Ml*cn,um="http://localhost:9999",cm="supabase.auth.token",dm={"X-Client-Info":`gotrue-js/${$d}`},Fl="X-Supabase-Api-Version",Id={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}};function fm(c){return Math.round(Date.now()/1e3)+c}function hm(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){const s=Math.random()*16|0;return(c=="x"?s:s&3|8).toString(16)})}const $t=()=>typeof window<"u"&&typeof document<"u",jr={tested:!1,writable:!1},is=()=>{if(!$t())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(jr.tested)return jr.writable;const c=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(c,c),globalThis.localStorage.removeItem(c),jr.tested=!0,jr.writable=!0}catch{jr.tested=!0,jr.writable=!1}return jr.writable};function pm(c){const s={},i=new URL(c);if(i.hash&&i.hash[0]==="#")try{new URLSearchParams(i.hash.substring(1)).forEach((a,d)=>{s[d]=a})}catch{}return i.searchParams.forEach((l,a)=>{s[a]=l}),s}const Ld=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>fn(async()=>{const{default:l}=await Promise.resolve().then(()=>pn);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)},mm=c=>typeof c=="object"&&c!==null&&"status"in c&&"ok"in c&&"json"in c&&typeof c.json=="function",Ad=async(c,s,i)=>{await c.setItem(s,JSON.stringify(i))},xi=async(c,s)=>{const i=await c.getItem(s);if(!i)return null;try{return JSON.parse(i)}catch{return i}},ki=async(c,s)=>{await c.removeItem(s)};function gm(c){const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let i="",l,a,d,f,p,m,g,y=0;for(c=c.replace("-","+").replace("_","/");y<c.length;)f=s.indexOf(c.charAt(y++)),p=s.indexOf(c.charAt(y++)),m=s.indexOf(c.charAt(y++)),g=s.indexOf(c.charAt(y++)),l=f<<2|p>>4,a=(p&15)<<4|m>>2,d=(m&3)<<6|g,i=i+String.fromCharCode(l),m!=64&&a!=0&&(i=i+String.fromCharCode(a)),g!=64&&d!=0&&(i=i+String.fromCharCode(d));return i}class bi{constructor(){this.promise=new bi.promiseConstructor((s,i)=>{this.resolve=s,this.reject=i})}}bi.promiseConstructor=Promise;function hd(c){const s=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,i=c.split(".");if(i.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!s.test(i[1]))throw new Error("JWT is not valid: payload is not in base64url format");const l=i[1];return JSON.parse(gm(l))}async function vm(c){return await new Promise(s=>{setTimeout(()=>s(null),c)})}function ym(c,s){return new Promise((l,a)=>{(async()=>{for(let d=0;d<1/0;d++)try{const f=await c(d);if(!s(d,null,f)){l(f);return}}catch(f){if(!s(d,f)){a(f);return}}})()})}function wm(c){return("0"+c.toString(16)).substr(-2)}function _m(){const s=new Uint32Array(56);if(typeof crypto>"u"){const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",l=i.length;let a="";for(let d=0;d<56;d++)a+=i.charAt(Math.floor(Math.random()*l));return a}return crypto.getRandomValues(s),Array.from(s,wm).join("")}async function xm(c){const i=new TextEncoder().encode(c),l=await crypto.subtle.digest("SHA-256",i),a=new Uint8Array(l);return Array.from(a).map(d=>String.fromCharCode(d)).join("")}function km(c){return btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Sm(c){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),c;const i=await xm(c);return km(i)}async function an(c,s,i=!1){const l=_m();let a=l;i&&(a+="/PASSWORD_RECOVERY"),await Ad(c,`${s}-code-verifier`,a);const d=await Sm(l);return[d,l===d?"plain":"s256"]}const Em=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function jm(c){const s=c.headers.get(Fl);if(!s||!s.match(Em))return null;try{return new Date(`${s}T00:00:00.0Z`)}catch{return null}}class Ql extends Error{constructor(s,i,l){super(s),this.__isAuthError=!0,this.name="AuthError",this.status=i,this.code=l}}function Z(c){return typeof c=="object"&&c!==null&&"__isAuthError"in c}class Cm extends Ql{constructor(s,i,l){super(s,i,l),this.name="AuthApiError",this.status=i,this.code=l}}function Pm(c){return Z(c)&&c.name==="AuthApiError"}class Dd extends Ql{constructor(s,i){super(s),this.name="AuthUnknownError",this.originalError=i}}class Tr extends Ql{constructor(s,i,l,a){super(s,l,a),this.name=i,this.status=l}}class ar extends Tr{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Nm(c){return Z(c)&&c.name==="AuthSessionMissingError"}class $l extends Tr{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Si extends Tr{constructor(s){super(s,"AuthInvalidCredentialsError",400,void 0)}}class Ei extends Tr{constructor(s,i=null){super(s,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Tm(c){return Z(c)&&c.name==="AuthImplicitGrantRedirectError"}class pd extends Tr{constructor(s,i=null){super(s,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Vl extends Tr{constructor(s,i){super(s,"AuthRetryableFetchError",i,void 0)}}function Il(c){return Z(c)&&c.name==="AuthRetryableFetchError"}class md extends Tr{constructor(s,i,l){super(s,"AuthWeakPasswordError",i,"weak_password"),this.reasons=l}}var bm=function(c,s){var i={};for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&s.indexOf(l)<0&&(i[l]=c[l]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(c);a<l.length;a++)s.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(c,l[a])&&(i[l[a]]=c[l[a]]);return i};const Cr=c=>c.msg||c.message||c.error_description||c.error||JSON.stringify(c),Rm=[502,503,504];async function gd(c){var s;if(!mm(c))throw new Vl(Cr(c),0);if(Rm.includes(c.status))throw new Vl(Cr(c),c.status);let i;try{i=await c.json()}catch(d){throw new Dd(Cr(d),d)}let l;const a=jm(c);if(a&&a.getTime()>=Id["2024-01-01"].timestamp&&typeof i=="object"&&i&&typeof i.code=="string"?l=i.code:typeof i=="object"&&i&&typeof i.error_code=="string"&&(l=i.error_code),l){if(l==="weak_password")throw new md(Cr(i),c.status,((s=i.weak_password)===null||s===void 0?void 0:s.reasons)||[]);if(l==="session_not_found")throw new ar}else if(typeof i=="object"&&i&&typeof i.weak_password=="object"&&i.weak_password&&Array.isArray(i.weak_password.reasons)&&i.weak_password.reasons.length&&i.weak_password.reasons.reduce((d,f)=>d&&typeof f=="string",!0))throw new md(Cr(i),c.status,i.weak_password.reasons);throw new Cm(Cr(i),c.status||500,l)}const Om=(c,s,i,l)=>{const a={method:c,headers:(s==null?void 0:s.headers)||{}};return c==="GET"?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},s==null?void 0:s.headers),a.body=JSON.stringify(l),Object.assign(Object.assign({},a),i))};async function ie(c,s,i,l){var a;const d=Object.assign({},l==null?void 0:l.headers);d[Fl]||(d[Fl]=Id["2024-01-01"].name),l!=null&&l.jwt&&(d.Authorization=`Bearer ${l.jwt}`);const f=(a=l==null?void 0:l.query)!==null&&a!==void 0?a:{};l!=null&&l.redirectTo&&(f.redirect_to=l.redirectTo);const p=Object.keys(f).length?"?"+new URLSearchParams(f).toString():"",m=await $m(c,s,i+p,{headers:d,noResolveJson:l==null?void 0:l.noResolveJson},{},l==null?void 0:l.body);return l!=null&&l.xform?l==null?void 0:l.xform(m):{data:Object.assign({},m),error:null}}async function $m(c,s,i,l,a,d){const f=Om(s,l,a,d);let p;try{p=await c(i,Object.assign({},f))}catch(m){throw console.error(m),new Vl(Cr(m),0)}if(p.ok||await gd(p),l!=null&&l.noResolveJson)return p;try{return await p.json()}catch(m){await gd(m)}}function ur(c){var s;let i=null;Dm(c)&&(i=Object.assign({},c),c.expires_at||(i.expires_at=fm(c.expires_in)));const l=(s=c.user)!==null&&s!==void 0?s:c;return{data:{session:i,user:l},error:null}}function vd(c){const s=ur(c);return!s.error&&c.weak_password&&typeof c.weak_password=="object"&&Array.isArray(c.weak_password.reasons)&&c.weak_password.reasons.length&&c.weak_password.message&&typeof c.weak_password.message=="string"&&c.weak_password.reasons.reduce((i,l)=>i&&typeof l=="string",!0)&&(s.data.weak_password=c.weak_password),s}function dr(c){var s;return{data:{user:(s=c.user)!==null&&s!==void 0?s:c},error:null}}function Im(c){return{data:c,error:null}}function Lm(c){const{action_link:s,email_otp:i,hashed_token:l,redirect_to:a,verification_type:d}=c,f=bm(c,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),p={action_link:s,email_otp:i,hashed_token:l,redirect_to:a,verification_type:d},m=Object.assign({},f);return{data:{properties:p,user:m},error:null}}function Am(c){return c}function Dm(c){return c.access_token&&c.refresh_token&&c.expires_in}var zm=function(c,s){var i={};for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&s.indexOf(l)<0&&(i[l]=c[l]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(c);a<l.length;a++)s.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(c,l[a])&&(i[l[a]]=c[l[a]]);return i};class Um{constructor({url:s="",headers:i={},fetch:l}){this.url=s,this.headers=i,this.fetch=Ld(l),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(s,i="global"){try{return await ie(this.fetch,"POST",`${this.url}/logout?scope=${i}`,{headers:this.headers,jwt:s,noResolveJson:!0}),{data:null,error:null}}catch(l){if(Z(l))return{data:null,error:l};throw l}}async inviteUserByEmail(s,i={}){try{return await ie(this.fetch,"POST",`${this.url}/invite`,{body:{email:s,data:i.data},headers:this.headers,redirectTo:i.redirectTo,xform:dr})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async generateLink(s){try{const{options:i}=s,l=zm(s,["options"]),a=Object.assign(Object.assign({},l),i);return"newEmail"in l&&(a.new_email=l==null?void 0:l.newEmail,delete a.newEmail),await ie(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:a,headers:this.headers,xform:Lm,redirectTo:i==null?void 0:i.redirectTo})}catch(i){if(Z(i))return{data:{properties:null,user:null},error:i};throw i}}async createUser(s){try{return await ie(this.fetch,"POST",`${this.url}/admin/users`,{body:s,headers:this.headers,xform:dr})}catch(i){if(Z(i))return{data:{user:null},error:i};throw i}}async listUsers(s){var i,l,a,d,f,p,m;try{const g={nextPage:null,lastPage:0,total:0},y=await ie(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(l=(i=s==null?void 0:s.page)===null||i===void 0?void 0:i.toString())!==null&&l!==void 0?l:"",per_page:(d=(a=s==null?void 0:s.perPage)===null||a===void 0?void 0:a.toString())!==null&&d!==void 0?d:""},xform:Am});if(y.error)throw y.error;const E=await y.json(),P=(f=y.headers.get("x-total-count"))!==null&&f!==void 0?f:0,O=(m=(p=y.headers.get("link"))===null||p===void 0?void 0:p.split(","))!==null&&m!==void 0?m:[];return O.length>0&&(O.forEach(L=>{const F=parseInt(L.split(";")[0].split("=")[1].substring(0,1)),D=JSON.parse(L.split(";")[1].split("=")[1]);g[`${D}Page`]=F}),g.total=parseInt(P)),{data:Object.assign(Object.assign({},E),g),error:null}}catch(g){if(Z(g))return{data:{users:[]},error:g};throw g}}async getUserById(s){try{return await ie(this.fetch,"GET",`${this.url}/admin/users/${s}`,{headers:this.headers,xform:dr})}catch(i){if(Z(i))return{data:{user:null},error:i};throw i}}async updateUserById(s,i){try{return await ie(this.fetch,"PUT",`${this.url}/admin/users/${s}`,{body:i,headers:this.headers,xform:dr})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async deleteUser(s,i=!1){try{return await ie(this.fetch,"DELETE",`${this.url}/admin/users/${s}`,{headers:this.headers,body:{should_soft_delete:i},xform:dr})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async _listFactors(s){try{const{data:i,error:l}=await ie(this.fetch,"GET",`${this.url}/admin/users/${s.userId}/factors`,{headers:this.headers,xform:a=>({data:{factors:a},error:null})});return{data:i,error:l}}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _deleteFactor(s){try{return{data:await ie(this.fetch,"DELETE",`${this.url}/admin/users/${s.userId}/factors/${s.id}`,{headers:this.headers}),error:null}}catch(i){if(Z(i))return{data:null,error:i};throw i}}}const Mm={getItem:c=>is()?globalThis.localStorage.getItem(c):null,setItem:(c,s)=>{is()&&globalThis.localStorage.setItem(c,s)},removeItem:c=>{is()&&globalThis.localStorage.removeItem(c)}};function yd(c={}){return{getItem:s=>c[s]||null,setItem:(s,i)=>{c[s]=i},removeItem:s=>{delete c[s]}}}function Fm(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const un={debug:!!(globalThis&&is()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class zd extends Error{constructor(s){super(s),this.isAcquireTimeout=!0}}class Vm extends zd{}async function Bm(c,s,i){un.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",c,s);const l=new globalThis.AbortController;return s>0&&setTimeout(()=>{l.abort(),un.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",c)},s),await Promise.resolve().then(()=>globalThis.navigator.locks.request(c,s===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:l.signal},async a=>{if(a){un.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",c,a.name);try{return await i()}finally{un.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",c,a.name)}}else{if(s===0)throw un.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",c),new Vm(`Acquiring an exclusive Navigator LockManager lock "${c}" immediately failed`);if(un.debug)try{const d=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(d,null,"  "))}catch(d){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",d)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await i()}}))}Fm();const Hm={url:um,storageKey:cm,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:dm,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function wd(c,s,i){return await i()}class os{constructor(s){var i,l;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=os.nextInstanceID,os.nextInstanceID+=1,this.instanceID>0&&$t()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const a=Object.assign(Object.assign({},Hm),s);if(this.logDebugMessages=!!a.debug,typeof a.debug=="function"&&(this.logger=a.debug),this.persistSession=a.persistSession,this.storageKey=a.storageKey,this.autoRefreshToken=a.autoRefreshToken,this.admin=new Um({url:a.url,headers:a.headers,fetch:a.fetch}),this.url=a.url,this.headers=a.headers,this.fetch=Ld(a.fetch),this.lock=a.lock||wd,this.detectSessionInUrl=a.detectSessionInUrl,this.flowType=a.flowType,this.hasCustomAuthorizationHeader=a.hasCustomAuthorizationHeader,a.lock?this.lock=a.lock:$t()&&(!((i=globalThis==null?void 0:globalThis.navigator)===null||i===void 0)&&i.locks)?this.lock=Bm:this.lock=wd,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?a.storage?this.storage=a.storage:is()?this.storage=Mm:(this.memoryStorage={},this.storage=yd(this.memoryStorage)):(this.memoryStorage={},this.storage=yd(this.memoryStorage)),$t()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(d){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",d)}(l=this.broadcastChannel)===null||l===void 0||l.addEventListener("message",async d=>{this._debug("received broadcast notification from other tab or client",d),await this._notifyAllSubscribers(d.data.event,d.data.session,!1)})}this.initialize()}_debug(...s){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${$d}) ${new Date().toISOString()}`,...s),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var s;try{const i=pm(window.location.href);let l="none";if(this._isImplicitGrantCallback(i)?l="implicit":await this._isPKCECallback(i)&&(l="pkce"),$t()&&this.detectSessionInUrl&&l!=="none"){const{data:a,error:d}=await this._getSessionFromURL(i,l);if(d){if(this._debug("#_initialize()","error detecting session from URL",d),Tm(d)){const m=(s=d.details)===null||s===void 0?void 0:s.code;if(m==="identity_already_exists"||m==="identity_not_found"||m==="single_identity_not_deletable")return{error:d}}return await this._removeSession(),{error:d}}const{session:f,redirectType:p}=a;return this._debug("#_initialize()","detected session in URL",f,"redirect type",p),await this._saveSession(f),setTimeout(async()=>{p==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",f):await this._notifyAllSubscribers("SIGNED_IN",f)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(i){return Z(i)?{error:i}:{error:new Dd("Unexpected error during initialization",i)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(s){var i,l,a;try{const d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(l=(i=s==null?void 0:s.options)===null||i===void 0?void 0:i.data)!==null&&l!==void 0?l:{},gotrue_meta_security:{captcha_token:(a=s==null?void 0:s.options)===null||a===void 0?void 0:a.captchaToken}},xform:ur}),{data:f,error:p}=d;if(p||!f)return{data:{user:null,session:null},error:p};const m=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",m)),{data:{user:g,session:m},error:null}}catch(d){if(Z(d))return{data:{user:null,session:null},error:d};throw d}}async signUp(s){var i,l,a;try{let d;if("email"in s){const{email:y,password:E,options:P}=s;let O=null,L=null;this.flowType==="pkce"&&([O,L]=await an(this.storage,this.storageKey)),d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:P==null?void 0:P.emailRedirectTo,body:{email:y,password:E,data:(i=P==null?void 0:P.data)!==null&&i!==void 0?i:{},gotrue_meta_security:{captcha_token:P==null?void 0:P.captchaToken},code_challenge:O,code_challenge_method:L},xform:ur})}else if("phone"in s){const{phone:y,password:E,options:P}=s;d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:y,password:E,data:(l=P==null?void 0:P.data)!==null&&l!==void 0?l:{},channel:(a=P==null?void 0:P.channel)!==null&&a!==void 0?a:"sms",gotrue_meta_security:{captcha_token:P==null?void 0:P.captchaToken}},xform:ur})}else throw new Si("You must provide either an email or phone number and a password");const{data:f,error:p}=d;if(p||!f)return{data:{user:null,session:null},error:p};const m=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",m)),{data:{user:g,session:m},error:null}}catch(d){if(Z(d))return{data:{user:null,session:null},error:d};throw d}}async signInWithPassword(s){try{let i;if("email"in s){const{email:d,password:f,options:p}=s;i=await ie(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:d,password:f,gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken}},xform:vd})}else if("phone"in s){const{phone:d,password:f,options:p}=s;i=await ie(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:d,password:f,gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken}},xform:vd})}else throw new Si("You must provide either an email or phone number and a password");const{data:l,error:a}=i;return a?{data:{user:null,session:null},error:a}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new $l}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:Object.assign({user:l.user,session:l.session},l.weak_password?{weakPassword:l.weak_password}:null),error:a})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOAuth(s){var i,l,a,d;return await this._handleProviderSignIn(s.provider,{redirectTo:(i=s.options)===null||i===void 0?void 0:i.redirectTo,scopes:(l=s.options)===null||l===void 0?void 0:l.scopes,queryParams:(a=s.options)===null||a===void 0?void 0:a.queryParams,skipBrowserRedirect:(d=s.options)===null||d===void 0?void 0:d.skipBrowserRedirect})}async exchangeCodeForSession(s){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(s))}async _exchangeCodeForSession(s){const i=await xi(this.storage,`${this.storageKey}-code-verifier`),[l,a]=(i??"").split("/");try{const{data:d,error:f}=await ie(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:s,code_verifier:l},xform:ur});if(await ki(this.storage,`${this.storageKey}-code-verifier`),f)throw f;return!d||!d.session||!d.user?{data:{user:null,session:null,redirectType:null},error:new $l}:(d.session&&(await this._saveSession(d.session),await this._notifyAllSubscribers("SIGNED_IN",d.session)),{data:Object.assign(Object.assign({},d),{redirectType:a??null}),error:f})}catch(d){if(Z(d))return{data:{user:null,session:null,redirectType:null},error:d};throw d}}async signInWithIdToken(s){try{const{options:i,provider:l,token:a,access_token:d,nonce:f}=s,p=await ie(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:l,id_token:a,access_token:d,nonce:f,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},xform:ur}),{data:m,error:g}=p;return g?{data:{user:null,session:null},error:g}:!m||!m.session||!m.user?{data:{user:null,session:null},error:new $l}:(m.session&&(await this._saveSession(m.session),await this._notifyAllSubscribers("SIGNED_IN",m.session)),{data:m,error:g})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOtp(s){var i,l,a,d,f;try{if("email"in s){const{email:p,options:m}=s;let g=null,y=null;this.flowType==="pkce"&&([g,y]=await an(this.storage,this.storageKey));const{error:E}=await ie(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:p,data:(i=m==null?void 0:m.data)!==null&&i!==void 0?i:{},create_user:(l=m==null?void 0:m.shouldCreateUser)!==null&&l!==void 0?l:!0,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken},code_challenge:g,code_challenge_method:y},redirectTo:m==null?void 0:m.emailRedirectTo});return{data:{user:null,session:null},error:E}}if("phone"in s){const{phone:p,options:m}=s,{data:g,error:y}=await ie(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:p,data:(a=m==null?void 0:m.data)!==null&&a!==void 0?a:{},create_user:(d=m==null?void 0:m.shouldCreateUser)!==null&&d!==void 0?d:!0,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken},channel:(f=m==null?void 0:m.channel)!==null&&f!==void 0?f:"sms"}});return{data:{user:null,session:null,messageId:g==null?void 0:g.message_id},error:y}}throw new Si("You must provide either an email or phone number.")}catch(p){if(Z(p))return{data:{user:null,session:null},error:p};throw p}}async verifyOtp(s){var i,l;try{let a,d;"options"in s&&(a=(i=s.options)===null||i===void 0?void 0:i.redirectTo,d=(l=s.options)===null||l===void 0?void 0:l.captchaToken);const{data:f,error:p}=await ie(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},s),{gotrue_meta_security:{captcha_token:d}}),redirectTo:a,xform:ur});if(p)throw p;if(!f)throw new Error("An error occurred on token verification.");const m=f.session,g=f.user;return m!=null&&m.access_token&&(await this._saveSession(m),await this._notifyAllSubscribers(s.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",m)),{data:{user:g,session:m},error:null}}catch(a){if(Z(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithSSO(s){var i,l,a;try{let d=null,f=null;return this.flowType==="pkce"&&([d,f]=await an(this.storage,this.storageKey)),await ie(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in s?{provider_id:s.providerId}:null),"domain"in s?{domain:s.domain}:null),{redirect_to:(l=(i=s.options)===null||i===void 0?void 0:i.redirectTo)!==null&&l!==void 0?l:void 0}),!((a=s==null?void 0:s.options)===null||a===void 0)&&a.captchaToken?{gotrue_meta_security:{captcha_token:s.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:d,code_challenge_method:f}),headers:this.headers,xform:Im})}catch(d){if(Z(d))return{data:null,error:d};throw d}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async s=>{const{data:{session:i},error:l}=s;if(l)throw l;if(!i)throw new ar;const{error:a}=await ie(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:i.access_token});return{data:{user:null,session:null},error:a}})}catch(s){if(Z(s))return{data:{user:null,session:null},error:s};throw s}}async resend(s){try{const i=`${this.url}/resend`;if("email"in s){const{email:l,type:a,options:d}=s,{error:f}=await ie(this.fetch,"POST",i,{headers:this.headers,body:{email:l,type:a,gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}},redirectTo:d==null?void 0:d.emailRedirectTo});return{data:{user:null,session:null},error:f}}else if("phone"in s){const{phone:l,type:a,options:d}=s,{data:f,error:p}=await ie(this.fetch,"POST",i,{headers:this.headers,body:{phone:l,type:a,gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}}});return{data:{user:null,session:null,messageId:f==null?void 0:f.message_id},error:p}}throw new Si("You must provide either an email or phone number and a type")}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async i=>i))}async _acquireLock(s,i){this._debug("#_acquireLock","begin",s);try{if(this.lockAcquired){const l=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),a=(async()=>(await l,await i()))();return this.pendingInLock.push((async()=>{try{await a}catch{}})()),a}return await this.lock(`lock:${this.storageKey}`,s,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const l=i();for(this.pendingInLock.push((async()=>{try{await l}catch{}})()),await l;this.pendingInLock.length;){const a=[...this.pendingInLock];await Promise.all(a),this.pendingInLock.splice(0,a.length)}return await l}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(s){this._debug("#_useSession","begin");try{const i=await this.__loadSession();return await s(i)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let s=null;const i=await xi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",i),i!==null&&(this._isValidSession(i)?s=i:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!s)return{data:{session:null},error:null};const l=s.expires_at?s.expires_at*1e3-Date.now()<Ol:!1;if(this._debug("#__loadSession()",`session has${l?"":" not"} expired`,"expires_at",s.expires_at),!l){if(this.storage.isServer){let f=this.suppressGetSessionWarning;s=new Proxy(s,{get:(m,g,y)=>(!f&&g==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),f=!0,this.suppressGetSessionWarning=!0),Reflect.get(m,g,y))})}return{data:{session:s},error:null}}const{session:a,error:d}=await this._callRefreshToken(s.refresh_token);return d?{data:{session:null},error:d}:{data:{session:a},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(s){return s?await this._getUser(s):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(s){try{return s?await ie(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:s,xform:dr}):await this._useSession(async i=>{var l,a,d;const{data:f,error:p}=i;if(p)throw p;return!(!((l=f.session)===null||l===void 0)&&l.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new ar}:await ie(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(d=(a=f.session)===null||a===void 0?void 0:a.access_token)!==null&&d!==void 0?d:void 0,xform:dr})})}catch(i){if(Z(i))return Nm(i)&&(await this._removeSession(),await ki(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:i};throw i}}async updateUser(s,i={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(s,i))}async _updateUser(s,i={}){try{return await this._useSession(async l=>{const{data:a,error:d}=l;if(d)throw d;if(!a.session)throw new ar;const f=a.session;let p=null,m=null;this.flowType==="pkce"&&s.email!=null&&([p,m]=await an(this.storage,this.storageKey));const{data:g,error:y}=await ie(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:i==null?void 0:i.emailRedirectTo,body:Object.assign(Object.assign({},s),{code_challenge:p,code_challenge_method:m}),jwt:f.access_token,xform:dr});if(y)throw y;return f.user=g.user,await this._saveSession(f),await this._notifyAllSubscribers("USER_UPDATED",f),{data:{user:f.user},error:null}})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}_decodeJWT(s){return hd(s)}async setSession(s){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(s))}async _setSession(s){try{if(!s.access_token||!s.refresh_token)throw new ar;const i=Date.now()/1e3;let l=i,a=!0,d=null;const f=hd(s.access_token);if(f.exp&&(l=f.exp,a=l<=i),a){const{session:p,error:m}=await this._callRefreshToken(s.refresh_token);if(m)return{data:{user:null,session:null},error:m};if(!p)return{data:{user:null,session:null},error:null};d=p}else{const{data:p,error:m}=await this._getUser(s.access_token);if(m)throw m;d={access_token:s.access_token,refresh_token:s.refresh_token,user:p.user,token_type:"bearer",expires_in:l-i,expires_at:l},await this._saveSession(d),await this._notifyAllSubscribers("SIGNED_IN",d)}return{data:{user:d.user,session:d},error:null}}catch(i){if(Z(i))return{data:{session:null,user:null},error:i};throw i}}async refreshSession(s){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(s))}async _refreshSession(s){try{return await this._useSession(async i=>{var l;if(!s){const{data:f,error:p}=i;if(p)throw p;s=(l=f.session)!==null&&l!==void 0?l:void 0}if(!(s!=null&&s.refresh_token))throw new ar;const{session:a,error:d}=await this._callRefreshToken(s.refresh_token);return d?{data:{user:null,session:null},error:d}:a?{data:{user:a.user,session:a},error:null}:{data:{user:null,session:null},error:null}})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async _getSessionFromURL(s,i){try{if(!$t())throw new Ei("No browser detected.");if(s.error||s.error_description||s.error_code)throw new Ei(s.error_description||"Error in URL with unspecified error_description",{error:s.error||"unspecified_error",code:s.error_code||"unspecified_code"});switch(i){case"implicit":if(this.flowType==="pkce")throw new pd("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new Ei("Not a valid implicit grant flow url.");break;default:}if(i==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!s.code)throw new pd("No code detected.");const{data:ae,error:U}=await this._exchangeCodeForSession(s.code);if(U)throw U;const q=new URL(window.location.href);return q.searchParams.delete("code"),window.history.replaceState(window.history.state,"",q.toString()),{data:{session:ae.session,redirectType:null},error:null}}const{provider_token:l,provider_refresh_token:a,access_token:d,refresh_token:f,expires_in:p,expires_at:m,token_type:g}=s;if(!d||!p||!f||!g)throw new Ei("No session defined in URL");const y=Math.round(Date.now()/1e3),E=parseInt(p);let P=y+E;m&&(P=parseInt(m));const O=P-y;O*1e3<=cn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${O}s, should have been closer to ${E}s`);const L=P-E;y-L>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",L,P,y):y-L<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",L,P,y);const{data:F,error:D}=await this._getUser(d);if(D)throw D;const re={provider_token:l,provider_refresh_token:a,access_token:d,expires_in:E,expires_at:P,refresh_token:f,token_type:g,user:F.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:re,redirectType:s.type},error:null}}catch(l){if(Z(l))return{data:{session:null,redirectType:null},error:l};throw l}}_isImplicitGrantCallback(s){return!!(s.access_token||s.error_description)}async _isPKCECallback(s){const i=await xi(this.storage,`${this.storageKey}-code-verifier`);return!!(s.code&&i)}async signOut(s={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(s))}async _signOut({scope:s}={scope:"global"}){return await this._useSession(async i=>{var l;const{data:a,error:d}=i;if(d)return{error:d};const f=(l=a.session)===null||l===void 0?void 0:l.access_token;if(f){const{error:p}=await this.admin.signOut(f,s);if(p&&!(Pm(p)&&(p.status===404||p.status===401||p.status===403)))return{error:p}}return s!=="others"&&(await this._removeSession(),await ki(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(s){const i=hm(),l={id:i,callback:s,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",i),this.stateChangeEmitters.delete(i)}};return this._debug("#onAuthStateChange()","registered callback with id",i),this.stateChangeEmitters.set(i,l),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(i)})))(),{data:{subscription:l}}}async _emitInitialSession(s){return await this._useSession(async i=>{var l,a;try{const{data:{session:d},error:f}=i;if(f)throw f;await((l=this.stateChangeEmitters.get(s))===null||l===void 0?void 0:l.callback("INITIAL_SESSION",d)),this._debug("INITIAL_SESSION","callback id",s,"session",d)}catch(d){await((a=this.stateChangeEmitters.get(s))===null||a===void 0?void 0:a.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",s,"error",d),console.error(d)}})}async resetPasswordForEmail(s,i={}){let l=null,a=null;this.flowType==="pkce"&&([l,a]=await an(this.storage,this.storageKey,!0));try{return await ie(this.fetch,"POST",`${this.url}/recover`,{body:{email:s,code_challenge:l,code_challenge_method:a,gotrue_meta_security:{captcha_token:i.captchaToken}},headers:this.headers,redirectTo:i.redirectTo})}catch(d){if(Z(d))return{data:null,error:d};throw d}}async getUserIdentities(){var s;try{const{data:i,error:l}=await this.getUser();if(l)throw l;return{data:{identities:(s=i.user.identities)!==null&&s!==void 0?s:[]},error:null}}catch(i){if(Z(i))return{data:null,error:i};throw i}}async linkIdentity(s){var i;try{const{data:l,error:a}=await this._useSession(async d=>{var f,p,m,g,y;const{data:E,error:P}=d;if(P)throw P;const O=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,s.provider,{redirectTo:(f=s.options)===null||f===void 0?void 0:f.redirectTo,scopes:(p=s.options)===null||p===void 0?void 0:p.scopes,queryParams:(m=s.options)===null||m===void 0?void 0:m.queryParams,skipBrowserRedirect:!0});return await ie(this.fetch,"GET",O,{headers:this.headers,jwt:(y=(g=E.session)===null||g===void 0?void 0:g.access_token)!==null&&y!==void 0?y:void 0})});if(a)throw a;return $t()&&!(!((i=s.options)===null||i===void 0)&&i.skipBrowserRedirect)&&window.location.assign(l==null?void 0:l.url),{data:{provider:s.provider,url:l==null?void 0:l.url},error:null}}catch(l){if(Z(l))return{data:{provider:s.provider,url:null},error:l};throw l}}async unlinkIdentity(s){try{return await this._useSession(async i=>{var l,a;const{data:d,error:f}=i;if(f)throw f;return await ie(this.fetch,"DELETE",`${this.url}/user/identities/${s.identity_id}`,{headers:this.headers,jwt:(a=(l=d.session)===null||l===void 0?void 0:l.access_token)!==null&&a!==void 0?a:void 0})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _refreshAccessToken(s){const i=`#_refreshAccessToken(${s.substring(0,5)}...)`;this._debug(i,"begin");try{const l=Date.now();return await ym(async a=>(a>0&&await vm(200*Math.pow(2,a-1)),this._debug(i,"refreshing attempt",a),await ie(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:s},headers:this.headers,xform:ur})),(a,d)=>{const f=200*Math.pow(2,a);return d&&Il(d)&&Date.now()+f-l<cn})}catch(l){if(this._debug(i,"error",l),Z(l))return{data:{session:null,user:null},error:l};throw l}finally{this._debug(i,"end")}}_isValidSession(s){return typeof s=="object"&&s!==null&&"access_token"in s&&"refresh_token"in s&&"expires_at"in s}async _handleProviderSignIn(s,i){const l=await this._getUrlForProvider(`${this.url}/authorize`,s,{redirectTo:i.redirectTo,scopes:i.scopes,queryParams:i.queryParams});return this._debug("#_handleProviderSignIn()","provider",s,"options",i,"url",l),$t()&&!i.skipBrowserRedirect&&window.location.assign(l),{data:{provider:s,url:l},error:null}}async _recoverAndRefresh(){var s;const i="#_recoverAndRefresh()";this._debug(i,"begin");try{const l=await xi(this.storage,this.storageKey);if(this._debug(i,"session from storage",l),!this._isValidSession(l)){this._debug(i,"session is not valid"),l!==null&&await this._removeSession();return}const a=((s=l.expires_at)!==null&&s!==void 0?s:1/0)*1e3-Date.now()<Ol;if(this._debug(i,`session has${a?"":" not"} expired with margin of ${Ol}s`),a){if(this.autoRefreshToken&&l.refresh_token){const{error:d}=await this._callRefreshToken(l.refresh_token);d&&(console.error(d),Il(d)||(this._debug(i,"refresh failed with a non-retryable error, removing the session",d),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",l)}catch(l){this._debug(i,"error",l),console.error(l);return}finally{this._debug(i,"end")}}async _callRefreshToken(s){var i,l;if(!s)throw new ar;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const a=`#_callRefreshToken(${s.substring(0,5)}...)`;this._debug(a,"begin");try{this.refreshingDeferred=new bi;const{data:d,error:f}=await this._refreshAccessToken(s);if(f)throw f;if(!d.session)throw new ar;await this._saveSession(d.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",d.session);const p={session:d.session,error:null};return this.refreshingDeferred.resolve(p),p}catch(d){if(this._debug(a,"error",d),Z(d)){const f={session:null,error:d};return Il(d)||await this._removeSession(),(i=this.refreshingDeferred)===null||i===void 0||i.resolve(f),f}throw(l=this.refreshingDeferred)===null||l===void 0||l.reject(d),d}finally{this.refreshingDeferred=null,this._debug(a,"end")}}async _notifyAllSubscribers(s,i,l=!0){const a=`#_notifyAllSubscribers(${s})`;this._debug(a,"begin",i,`broadcast = ${l}`);try{this.broadcastChannel&&l&&this.broadcastChannel.postMessage({event:s,session:i});const d=[],f=Array.from(this.stateChangeEmitters.values()).map(async p=>{try{await p.callback(s,i)}catch(m){d.push(m)}});if(await Promise.all(f),d.length>0){for(let p=0;p<d.length;p+=1)console.error(d[p]);throw d[0]}}finally{this._debug(a,"end")}}async _saveSession(s){this._debug("#_saveSession()",s),this.suppressGetSessionWarning=!0,await Ad(this.storage,this.storageKey,s)}async _removeSession(){this._debug("#_removeSession()"),await ki(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const s=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{s&&$t()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",s)}catch(i){console.error("removing visibilitychange callback failed",i)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const s=setInterval(()=>this._autoRefreshTokenTick(),cn);this.autoRefreshTicker=s,s&&typeof s=="object"&&typeof s.unref=="function"?s.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(s),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const s=this.autoRefreshTicker;this.autoRefreshTicker=null,s&&clearInterval(s)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const s=Date.now();try{return await this._useSession(async i=>{const{data:{session:l}}=i;if(!l||!l.refresh_token||!l.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const a=Math.floor((l.expires_at*1e3-s)/cn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${a} ticks, a tick lasts ${cn}ms, refresh threshold is ${Ml} ticks`),a<=Ml&&await this._callRefreshToken(l.refresh_token)})}catch(i){console.error("Auto refresh tick failed with error. This is likely a transient error.",i)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(s){if(s.isAcquireTimeout||s instanceof zd)this._debug("auto refresh token tick lock not available");else throw s}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!$t()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(s){console.error("_handleVisibilityChange",s)}}async _onVisibilityChanged(s){const i=`#_onVisibilityChanged(${s})`;this._debug(i,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),s||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(i,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(s,i,l){const a=[`provider=${encodeURIComponent(i)}`];if(l!=null&&l.redirectTo&&a.push(`redirect_to=${encodeURIComponent(l.redirectTo)}`),l!=null&&l.scopes&&a.push(`scopes=${encodeURIComponent(l.scopes)}`),this.flowType==="pkce"){const[d,f]=await an(this.storage,this.storageKey),p=new URLSearchParams({code_challenge:`${encodeURIComponent(d)}`,code_challenge_method:`${encodeURIComponent(f)}`});a.push(p.toString())}if(l!=null&&l.queryParams){const d=new URLSearchParams(l.queryParams);a.push(d.toString())}return l!=null&&l.skipBrowserRedirect&&a.push(`skip_http_redirect=${l.skipBrowserRedirect}`),`${s}?${a.join("&")}`}async _unenroll(s){try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;return d?{data:null,error:d}:await ie(this.fetch,"DELETE",`${this.url}/factors/${s.factorId}`,{headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _enroll(s){try{return await this._useSession(async i=>{var l,a;const{data:d,error:f}=i;if(f)return{data:null,error:f};const p=Object.assign({friendly_name:s.friendlyName,factor_type:s.factorType},s.factorType==="phone"?{phone:s.phone}:{issuer:s.issuer}),{data:m,error:g}=await ie(this.fetch,"POST",`${this.url}/factors`,{body:p,headers:this.headers,jwt:(l=d==null?void 0:d.session)===null||l===void 0?void 0:l.access_token});return g?{data:null,error:g}:(s.factorType==="totp"&&(!((a=m==null?void 0:m.totp)===null||a===void 0)&&a.qr_code)&&(m.totp.qr_code=`data:image/svg+xml;utf-8,${m.totp.qr_code}`),{data:m,error:null})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _verify(s){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;if(d)return{data:null,error:d};const{data:f,error:p}=await ie(this.fetch,"POST",`${this.url}/factors/${s.factorId}/verify`,{body:{code:s.code,challenge_id:s.challengeId},headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token});return p?{data:null,error:p}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:p})})}catch(i){if(Z(i))return{data:null,error:i};throw i}})}async _challenge(s){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;return d?{data:null,error:d}:await ie(this.fetch,"POST",`${this.url}/factors/${s.factorId}/challenge`,{body:{channel:s.channel},headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(Z(i))return{data:null,error:i};throw i}})}async _challengeAndVerify(s){const{data:i,error:l}=await this._challenge({factorId:s.factorId});return l?{data:null,error:l}:await this._verify({factorId:s.factorId,challengeId:i.id,code:s.code})}async _listFactors(){const{data:{user:s},error:i}=await this.getUser();if(i)return{data:null,error:i};const l=(s==null?void 0:s.factors)||[],a=l.filter(f=>f.factor_type==="totp"&&f.status==="verified"),d=l.filter(f=>f.factor_type==="phone"&&f.status==="verified");return{data:{all:l,totp:a,phone:d},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async s=>{var i,l;const{data:{session:a},error:d}=s;if(d)return{data:null,error:d};if(!a)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const f=this._decodeJWT(a.access_token);let p=null;f.aal&&(p=f.aal);let m=p;((l=(i=a.user.factors)===null||i===void 0?void 0:i.filter(E=>E.status==="verified"))!==null&&l!==void 0?l:[]).length>0&&(m="aal2");const y=f.amr||[];return{data:{currentLevel:p,nextLevel:m,currentAuthenticationMethods:y},error:null}}))}}os.nextInstanceID=0;const qm=os;class Wm extends qm{constructor(s){super(s)}}var Gm=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function p(y){try{g(l.next(y))}catch(E){f(E)}}function m(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(p,m)}g((l=l.apply(c,s||[])).next())})};class Qm{constructor(s,i,l){var a,d,f;if(this.supabaseUrl=s,this.supabaseKey=i,!s)throw new Error("supabaseUrl is required.");if(!i)throw new Error("supabaseKey is required.");const p=lm(s);this.realtimeUrl=`${p}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${p}/auth/v1`,this.storageUrl=`${p}/storage/v1`,this.functionsUrl=`${p}/functions/v1`;const m=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,g={db:Zp,realtime:tm,auth:Object.assign(Object.assign({},em),{storageKey:m}),global:Xp},y=am(l??{},g);this.storageKey=(a=y.auth.storageKey)!==null&&a!==void 0?a:"",this.headers=(d=y.global.headers)!==null&&d!==void 0?d:{},y.accessToken?(this.accessToken=y.accessToken,this.auth=new Proxy({},{get:(E,P)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(P)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((f=y.auth)!==null&&f!==void 0?f:{},this.headers,y.global.fetch),this.fetch=im(i,this._getAccessToken.bind(this),y.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},y.realtime)),this.rest=new _p(`${p}/rest/v1`,{headers:this.headers,schema:y.db.schema,fetch:this.fetch}),y.accessToken||this._listenForAuthEvents()}get functions(){return new ap(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Kp(this.storageUrl,this.headers,this.fetch)}from(s){return this.rest.from(s)}schema(s){return this.rest.schema(s)}rpc(s,i={},l={}){return this.rest.rpc(s,i,l)}channel(s,i={config:{}}){return this.realtime.channel(s,i)}getChannels(){return this.realtime.getChannels()}removeChannel(s){return this.realtime.removeChannel(s)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var s,i;return Gm(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:l}=yield this.auth.getSession();return(i=(s=l.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:null})}_initSupabaseAuthClient({autoRefreshToken:s,persistSession:i,detectSessionInUrl:l,storage:a,storageKey:d,flowType:f,lock:p,debug:m},g,y){const E={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Wm({url:this.authUrl,headers:Object.assign(Object.assign({},E),g),storageKey:d,autoRefreshToken:s,persistSession:i,detectSessionInUrl:l,storage:a,flowType:f,lock:p,debug:m,fetch:y,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(s){return new Lp(this.realtimeUrl,Object.assign(Object.assign({},s),{params:Object.assign({apikey:this.supabaseKey},s==null?void 0:s.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((i,l)=>{this._handleTokenChanged(i,"CLIENT",l==null?void 0:l.access_token)})}_handleTokenChanged(s,i,l){(s==="TOKEN_REFRESHED"||s==="SIGNED_IN")&&this.changedAccessToken!==l?this.changedAccessToken=l:s==="SIGNED_OUT"&&(this.realtime.setAuth(),i=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Km=(c,s,i)=>new Qm(c,s,i),Jm="https://nzluxtdhyiwmyuhmiili.supabase.co",Ym="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.d8dIrFP5nsY2ZHHAvnMnacPDkvix9-xHEvgPfSrmbyI",pt=Km(Jm,Ym);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Xm={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zm=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),mt=(c,s)=>{const i=se.forwardRef(({color:l="currentColor",size:a=24,strokeWidth:d=2,absoluteStrokeWidth:f,className:p="",children:m,...g},y)=>se.createElement("svg",{ref:y,...Xm,width:a,height:a,stroke:l,strokeWidth:f?Number(d)*24/Number(a):d,className:["lucide",`lucide-${Zm(c)}`,p].join(" "),...g},[...s.map(([E,P])=>se.createElement(E,P)),...Array.isArray(m)?m:[m]]));return i.displayName=`${c}`,i};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eg=mt("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tg=mt("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rg=mt("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ng=mt("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=mt("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sg=mt("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pi=mt("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ig=mt("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ni=mt("Printer",[["polyline",{points:"6 9 6 2 18 2 18 9",key:"1306q4"}],["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["rect",{width:"12",height:"8",x:"6",y:"14",key:"5ipwut"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Md=mt("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fd=mt("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const og=mt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function lg({activeTab:c,onTabChange:s,isAuthenticated:i}){const l=i?["sell","bulk","redeem","list"]:["sell","redeem","list"];return w.jsx("div",{style:{display:"flex",gap:"0.5rem"},children:l.map(a=>w.jsxs("button",{onClick:()=>s(a),style:{backgroundColor:"black",color:c===a?"#dc2626":"white",border:c===a?"2px solid #dc2626":"none",padding:"0.5rem 1rem",borderRadius:"0.375rem",fontWeight:"600",display:"flex",alignItems:"center",transition:"all 300ms"},children:[a==="sell"&&w.jsx(ng,{className:"h-5 w-5 mr-2"}),a==="bulk"&&w.jsx(ig,{className:"h-5 w-5 mr-2"}),a==="redeem"&&w.jsx(Md,{className:"h-5 w-5 mr-2"}),a==="list"&&w.jsx(sg,{className:"h-5 w-5 mr-2"}),w.jsxs("span",{children:[a==="sell"&&"Vender Vale-Presente",a==="bulk"&&"Gerar em Lote",a==="redeem"&&"Resgatar Vale-Presente",a==="list"&&"Listar Vale-Presentes"]})]},a))},"tabs-container-updated")}let ji;const ag=new Uint8Array(16);function ug(){if(!ji&&(ji=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!ji))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return ji(ag)}const Ue=[];for(let c=0;c<256;++c)Ue.push((c+256).toString(16).slice(1));function cg(c,s=0){return Ue[c[s+0]]+Ue[c[s+1]]+Ue[c[s+2]]+Ue[c[s+3]]+"-"+Ue[c[s+4]]+Ue[c[s+5]]+"-"+Ue[c[s+6]]+Ue[c[s+7]]+"-"+Ue[c[s+8]]+Ue[c[s+9]]+"-"+Ue[c[s+10]]+Ue[c[s+11]]+Ue[c[s+12]]+Ue[c[s+13]]+Ue[c[s+14]]+Ue[c[s+15]]}const dg=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),_d={randomUUID:dg};function Ti(c,s,i){if(_d.randomUUID&&!c)return _d.randomUUID();c=c||{};const l=c.random||(c.rng||ug)();return l[6]=l[6]&15|64,l[8]=l[8]&63|128,cg(l)}function fg({onSell:c}){const[s,i]=se.useState({value:"",recipientName:"",buyerName:""}),[l,a]=se.useState(null),[d,f]=se.useState(!1),[p,m]=se.useState(null),g=async E=>{if(E.preventDefault(),a(null),!s.value||!s.buyerName){a("Preencha todos os campos obrigatórios");return}try{f(!0);const P={id:Ti(),code:y(),value:Number(s.value),recipientName:s.recipientName,buyerName:s.buyerName,createdAt:new Date,used:!1};await c(P),m(P),i({value:"",recipientName:"",buyerName:""})}catch(P){console.error("Error generating gift card:",P),a("Erro ao gerar vale-presente")}finally{f(!1)}},y=()=>`N${Ti().replace(/-/g,"").substring(0,15).toUpperCase()}`;return w.jsx("div",{className:"space-y-8",children:w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Vender Vale-Presente"}),w.jsxs("form",{onSubmit:g,className:"space-y-6",children:[l&&w.jsx("div",{className:"p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200",children:l}),d&&w.jsx("div",{className:"p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200",children:"Gerando vale-presente..."}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Valor do Vale-Presente (R$)"}),w.jsx("input",{type:"number",step:"0.01",required:!0,value:s.value,onChange:E=>i({...s,value:E.target.value}),className:"form-input",placeholder:"0.00"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Destinatário"}),w.jsx("input",{type:"text",value:s.recipientName,onChange:E=>i({...s,recipientName:E.target.value}),className:"form-input",placeholder:"Nome de quem vai receber o vale (opcional)"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Comprador"}),w.jsx("input",{type:"text",required:!0,value:s.buyerName,onChange:E=>i({...s,buyerName:E.target.value}),className:"form-input",placeholder:"Nome de quem está comprando"})]}),w.jsxs("button",{type:"submit",disabled:d,className:"primary-button mt-4",children:[w.jsx(Ud,{className:"h-5 w-5"}),w.jsx("span",{children:d?"Gerando...":"Gerar Vale-Presente"})]})]})]})})}const Ct="/assets/logo-BXwS7maE.png";function hg({giftCards:c,isAuthenticated:s}){const[i,l]=se.useState(1),[a,d]=se.useState([]),[f,p]=se.useState(new Set),[m,g]=se.useState([]),y=10;se.useEffect(()=>{const $=c.filter(pe=>!pe.used),A={};$.forEach(pe=>{const _e=pe.code.substring(0,3);A[_e]||(A[_e]=[]),A[_e].push(pe)});const X=[],oe=[];Object.entries(A).forEach(([pe,_e])=>{_e.length===1?X.push(_e[0]):oe.push({groupId:pe,cards:_e,sampleCard:_e[0],isExpanded:f.has(pe)})}),g(X),d(oe)},[c,f]);const P=(()=>{const $=[...m.map(oe=>({type:"individual",card:oe})),...a.flatMap(oe=>oe.isExpanded&&s?[{type:"group",group:oe},...oe.cards.map(pe=>({type:"card",card:pe}))]:[{type:"group",group:oe}])],A=(i-1)*y,X=A+y;return{currentItems:$.slice(A,X),totalPages:Math.ceil($.length/y)}})(),O=P.currentItems,L=P.totalPages,F=$=>{l($)},D=()=>{const $=new Date;let A=$.getMonth()+2,X=$.getFullYear();return A>11&&(A=A-12,X+=1),`${new Date(X,A+1,0).getDate().toString().padStart(2,"0")}/${(A+1).toString().padStart(2,"0")}/${X}`},re=($,A)=>{const X=_e=>{const lt=document.createElement("canvas");return lt.width=_e.width,lt.height=_e.height,lt.getContext("2d").drawImage(_e,0,0),lt.toDataURL("image/png")},oe=new Image;oe.src=Ct;let pe=Ct;if(oe.complete)try{pe=X(oe)}catch(_e){console.error("Error converting logo to data URL",_e)}return`
      <div style="
        width: ${A?"226px":"302px"}; 
        padding: ${A?"0.75rem":"1rem"}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${pe}"
              alt="Logo" 
              style="height: ${A?"20px":"28px"}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${A?"0.875rem":"1.125rem"}; 
              margin: 0; 
              color: ${A?"#dc2626":"black"};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${$.recipientName?`
            <div>
              <p style="font-size: ${A?"0.75rem":"0.875rem"}; margin: 0; color: ${A?"#4b5563":"black"};">Para:</p>
              <p style="font-weight: 500; font-size: ${A?"0.875rem":"1rem"}; margin: 0; color: ${A?"#1f2937":"black"}">
                ${$.recipientName}
              </p>
            </div>
          `:""}
          <div style="text-align: ${$.recipientName?"right":"center"}; ${$.recipientName?"":"width: 100%;"}">
            <p style="font-size: ${A?"0.75rem":"0.875rem"}; margin: 0; color: ${A?"#4b5563":"black"};">Valor:</p>
            <p style="font-weight: 700; font-size: ${A?"1.25rem":"1.5rem"}; margin: 0; color: ${A?"#dc2626":"black"}">
              R$ ${$.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${A?100:130}&data=${encodeURIComponent($.code)}" 
               style="width: ${A?"100px":"130px"}; height: ${A?"100px":"130px"};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${A?"1rem":"1.125rem"}; 
            margin: 0; 
            color: ${A?"#1f2937":"black"};
          ">
            ${$.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${A?"#6b7280":"black"};">
          <p style="font-weight: 500; font-size: ${A?"0.75rem":"0.875rem"}; margin: 0;">Um presente de: ${$.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${A?"0.625rem":"0.75rem"}; margin: 0;">
            Válido até ${D()}
          </p>
        </div>
      </div>
    `},ae=($,A=!1)=>{const X=window.open("","_blank");if(X){const oe=`
        <html>
          <head>
            <title>${A?"Lote de Vale-Presentes":"Vale-Presente"}</title>
            <style>
              @media print {
                @page {
                  ${A?"size: A4; margin: 10mm;":"size: 80mm auto; margin: 0;"}
                }
                body {
                  margin: 0;
                  padding: ${A?"10mm":"4mm"};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
              
              /* Grid layout for bulk cards */
              .bulk-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
              }
              
              .card-container {
                margin-bottom: 16px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${Array.isArray($)?`
                <div class="bulk-grid">
                  ${$.map(pe=>`
                    <div class="card-container">
                      ${re(pe,!0)}
                    </div>
                  `).join("")}
                </div>
              `:`
                <div style="display: flex; justify-content: center;">
                  ${re($,!1)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;X.document.write(oe),X.document.close()}},U=$=>{s&&p(A=>{const X=new Set(A);return X.has($)?X.delete($):X.add($),X})},q=$=>$.substring(0,3)+"•••••••••••"+$.substring($.length-2);return w.jsxs("div",{className:"w-full mx-auto space-y-6 bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Vale-Presentes Ativos"}),w.jsx("div",{className:"space-y-3",children:O.length===0&&m.length===0&&a.length===0?w.jsxs("div",{className:"empty-state",children:[w.jsx("p",{className:"empty-state-title",children:"Nenhum Vale-Presente ativo"}),w.jsx("p",{className:"empty-state-subtitle",children:'Gere novos vale-presentes na aba "Vender Vale-Presente" ou "Gerar em Lote"'})]}):O.map(($,A)=>{if($.type==="individual")return w.jsxs("div",{className:"list-item",children:[w.jsxs("div",{children:[w.jsx("p",{className:"list-item-title",children:$.card.recipientName||`Comprador: ${$.card.buyerName}`}),w.jsxs("p",{className:"list-item-text",children:["Código do Vale-Presente: ",s?$.card.code:q($.card.code)]}),w.jsxs("p",{className:"list-item-text",children:["Valor: R$ ",$.card.value.toFixed(2)]})]}),s?w.jsxs("button",{onClick:()=>ae([$.card],!1),className:"print-button",children:[w.jsx(Ni,{className:"h-4 w-4"}),w.jsx("span",{children:"Imprimir"})]}):w.jsxs("div",{className:"text-gray-400 flex items-center",children:[w.jsx(Pi,{className:"h-4 w-4 mr-1"}),w.jsx("span",{className:"text-sm",children:"Bloqueado"})]})]},$.card.id);if($.type==="group"){const X=$.group;return w.jsx("div",{className:"list-item",children:w.jsxs("div",{className:"flex items-center justify-between w-full",children:[w.jsxs("div",{children:[w.jsxs("p",{className:"list-item-title",children:["Grupo ",X.groupId," - ",X.sampleCard.buyerName]}),w.jsxs("p",{className:"list-item-text",children:[X.cards.length," vale-presentes"]}),w.jsxs("p",{className:"list-item-text",children:["Valor: R$ ",X.sampleCard.value.toFixed(2)]})]}),w.jsx("div",{className:"flex items-center space-x-2",children:s?w.jsxs(w.Fragment,{children:[w.jsxs("button",{onClick:()=>ae(X.cards,!0),className:"print-button",children:[w.jsx(Ni,{className:"h-4 w-4"}),w.jsx("span",{children:"Imprimir Todos"})]}),w.jsx("button",{onClick:()=>U(X.groupId),className:"p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200",children:X.isExpanded?w.jsx(rg,{className:"h-5 w-5"}):w.jsx(tg,{className:"h-5 w-5"})})]}):w.jsxs("div",{className:"text-gray-400 flex items-center",children:[w.jsx(Pi,{className:"h-4 w-4 mr-1"}),w.jsx("span",{className:"text-sm",children:"Bloqueado"})]})})]})},X.groupId)}else return w.jsx("div",{className:"ml-6 p-3 border-l-2 border-red-200 bg-white rounded-r-lg",children:w.jsxs("div",{className:"flex items-center justify-between",children:[w.jsxs("p",{className:"list-item-text",children:["Código do Vale-Presente: ",s?$.card.code:q($.card.code)]}),w.jsx("button",{onClick:()=>ae([$.card],!1),className:"text-sm text-red-600 hover:text-red-700 px-2 py-1 rounded-md hover:bg-red-50 transition-colors duration-200",children:"Imprimir Individual"})]})},$.card.id)})}),L>1&&w.jsx("div",{className:"flex justify-center space-x-2 mt-4",children:Array.from({length:L},($,A)=>A+1).map($=>w.jsx("button",{onClick:()=>F($),className:`px-3 py-1 rounded ${i===$?"bg-red-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:$},$))})]})}function pg({giftCard:c}){const s=()=>{if(document.getElementById("redemption-receipt")){const a=window.open("","_blank");if(!a){alert("Por favor, permita pop-ups para imprimir o comprovante.");return}a.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Comprovante de Resgate</title>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
                padding: 0;
              }
              body {
                margin: 0;
                padding: 4mm;
                font-family: Arial, sans-serif;
                width: 72mm; /* 80mm - 8mm padding */
                box-sizing: border-box;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              .receipt {
                width: 100%;
                background: white;
              }
              .logo {
                height: 8mm;
                width: auto;
                object-fit: contain;
                display: block;
                margin: 0 auto 4mm auto;
                visibility: visible !important;
              }
              .header {
                text-align: center;
                margin-bottom: 4mm;
              }
              .title {
                font-size: 5mm;
                font-weight: bold;
                margin: 0;
              }
              .date {
                font-size: 3mm;
                color: #666;
                margin: 1mm 0 0 0;
              }
              .info {
                margin-bottom: 3mm;
              }
              .label {
                font-size: 3mm;
                color: #666;
                margin: 0;
              }
              .value {
                font-size: 3.5mm;
                margin: 1mm 0 0 0;
              }
              .amount {
                font-size: 6mm;
                font-weight: bold;
                margin: 1mm 0 0 0;
              }
              .signature {
                margin-top: 8mm;
                padding-top: 8mm;
                border-top: 1px solid #ddd;
              }
              .signature-line {
                border-bottom: 1px solid #666;
                margin-bottom: 2mm;
              }
              .signature-label {
                text-align: center;
                font-size: 3mm;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <img src="${Ct}" alt="Logo" class="logo" />
              
              <div class="header">
                <h2 class="title">Comprovante de Resgate</h2>
                <p class="date">${new Date().toLocaleDateString("pt-BR")}</p>
              </div>
              
              <div class="info">
                <p class="label">Vale-Presente:</p>
                <p class="value">${(c==null?void 0:c.code)||"N/A"}</p>
              </div>
              
              <div class="info">
                <p class="label">Valor Resgatado:</p>
                <p class="amount">R$ ${i}</p>
              </div>
              
              <div class="signature">
                <div class="signature-line"></div>
                <p class="signature-label">Assinatura</p>
              </div>
            </div>
          </body>
        </html>
      `),a.document.close(),a.focus(),setTimeout(()=>{a.print(),a.close()},500)}},i=typeof(c==null?void 0:c.value)=="number"?c.value.toFixed(2):"0.00";return console.log("RedemptionReceipt received giftCard:",c),w.jsxs("div",{children:[w.jsxs("button",{onClick:s,className:"mb-4 flex items-center space-x-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:[w.jsx(Ni,{className:"h-5 w-5"}),w.jsx("span",{children:"Imprimir Comprovante"})]}),w.jsx("div",{className:"flex justify-center",children:w.jsxs("div",{id:"redemption-receipt",className:"w-[302px] p-4 bg-white print:w-[80mm] print:p-2 print:border-none print:shadow-none print-content print:text-black",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:Ct,alt:"Logo",style:{height:"28px",width:"auto",objectFit:"contain",visibility:"visible"}})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-black text-lg print:text-black",style:{color:"black !important"},children:"Comprovante de Resgate"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[w.jsxs("div",{children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Data:"}),w.jsx("p",{className:"font-medium text-base print:text-black",style:{color:"black !important"},children:new Date().toLocaleDateString("pt-BR")})]}),w.jsxs("div",{className:"text-right",children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Valor:"}),w.jsxs("p",{className:"font-bold text-2xl",style:{color:"black !important"},children:["R$ ",i]})]})]}),w.jsxs("div",{className:"text-center mb-3",children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Vale-Presente:"}),w.jsx("p",{className:"font-mono tracking-wider text-lg print:text-black",style:{color:"black !important"},children:(c==null?void 0:c.code)||"N/A"})]}),w.jsx("div",{className:"mt-8 pt-4 border-t border-gray-200",children:w.jsx("p",{className:"text-center text-sm text-gray-600",style:{color:"black !important"},children:"Assinatura"})})]})})]})}function mg({onRedeem:c}){const[s,i]=se.useState(""),[l,a]=se.useState(null),[d,f]=se.useState(null),[p,m]=se.useState(!1),g=async y=>{y.preventDefault(),a(null),f(null),m(!0);try{const E=await c(s);E.success&&E.giftCard?(f(E.giftCard),i("")):a(E.message||"Vale-presente inválido ou já utilizado")}catch(E){console.error("Error redeeming gift card:",E),a("Erro ao resgatar vale-presente. Tente novamente.")}finally{m(!1)}};return w.jsxs("div",{className:"space-y-8",children:[d&&w.jsx("div",{className:"w-full bg-white p-6 rounded-lg shadow-md",children:w.jsx(pg,{giftCard:d})}),w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Resgatar Vale-Presente"}),w.jsxs("form",{onSubmit:g,className:"space-y-6",children:[w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Código do Vale-Presente"}),w.jsx("input",{type:"text",name:"code",required:!0,className:"form-input",value:s,onChange:y=>i(y.target.value.toUpperCase()),placeholder:"Digite o código manualmente",disabled:p})]}),l&&w.jsx("div",{className:"rounded-md bg-red-50 p-4 border border-red-200",children:w.jsxs("div",{className:"flex",children:[w.jsx(eg,{className:"h-5 w-5 text-red-500"}),w.jsx("div",{className:"ml-3",children:w.jsx("p",{className:"text-sm text-red-700",children:l})})]})}),w.jsxs("button",{type:"submit",className:"primary-button mt-4",disabled:p,children:[w.jsx(Md,{className:"h-5 w-5"}),w.jsx("span",{children:p?"Resgatando...":"Resgatar Vale-Presente"})]})]})]})]})}function Vd({giftCards:c=[],isBulk:s=!1,isFromVenderTab:i=!1}){if(!c||c.length===0)return null;const l=()=>{const m=new Date;let g=m.getMonth()+2,y=m.getFullYear();return g>11&&(g=g-12,y+=1),`${new Date(y,g+1,0).getDate().toString().padStart(2,"0")}/${(g+1).toString().padStart(2,"0")}/${y}`},a=(m,g)=>{const y=O=>{const L=document.createElement("canvas");return L.width=O.width,L.height=O.height,L.getContext("2d").drawImage(O,0,0),L.toDataURL("image/png")},E=new Image;E.src=Ct;let P=Ct;if(E.complete)try{P=y(E)}catch(O){console.error("Error converting logo to data URL",O)}return`
      <div style="
        width: ${g?"226px":"302px"}; 
        padding: ${g?"0.75rem":"1rem"}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${P}"
              alt="Logo" 
              style="height: ${g?"20px":"28px"}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${g?"0.875rem":"1.125rem"}; 
              margin: 0; 
              color: ${g?"#dc2626":"black"};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${m.recipientName?`
            <div>
              <p style="font-size: ${g?"0.75rem":"0.875rem"}; margin: 0; color: ${g?"#4b5563":"black"};">Para:</p>
              <p style="font-weight: 500; font-size: ${g?"0.875rem":"1rem"}; margin: 0; color: ${g?"#1f2937":"black"}">
                ${m.recipientName}
              </p>
            </div>
          `:""}
          <div style="text-align: ${m.recipientName?"right":"center"}; ${m.recipientName?"":"width: 100%;"}">
            <p style="font-size: ${g?"0.75rem":"0.875rem"}; margin: 0; color: ${g?"#4b5563":"black"};">Valor:</p>
            <p style="font-weight: 700; font-size: ${g?"1.25rem":"1.5rem"}; margin: 0; color: ${g?"#dc2626":"black"}">
              R$ ${m.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${g?100:130}&data=${encodeURIComponent(m.code)}" 
               style="width: ${g?"100px":"130px"}; height: ${g?"100px":"130px"};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${g?"1rem":"1.125rem"}; 
            margin: 0; 
            color: ${g?"#1f2937":"black"};
          ">
            ${m.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${g?"#6b7280":"black"};">
          <p style="font-weight: 500; font-size: ${g?"0.75rem":"0.875rem"}; margin: 0;">Um presente de: ${m.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${g?"0.625rem":"0.75rem"}; margin: 0;">
            Válido até ${l()}
          </p>
        </div>
      </div>
    `},d=()=>{f(),i&&!s&&c.length===1&&setTimeout(()=>{p(c[0])},1e3)},f=()=>{const m=window.open("","_blank");if(m){const g=`
        <html>
          <head>
            <title>${s?"Lote de Vale-Presentes":"Vale-Presente"}</title>
            <style>
              @media print {
                @page {
                  ${s?"size: A4; margin: 10mm;":"size: 80mm auto; margin: 0;"}
                }
                body {
                  margin: 0;
                  padding: ${s?"10mm":"4mm"};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${s?`
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                  ${c.map(y=>`
                    <div style="margin-bottom: 16px;">
                      ${a(y,!0)}
                    </div>
                  `).join("")}
                </div>
              `:`
                <div style="${i?"":"display: flex; justify-content: center;"}">
                  ${a(c[0],!1)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;m.document.write(g),m.document.close()}},p=m=>{const g=window.open("","_blank");if(g){const y=new Date,E=y.toLocaleDateString("pt-BR"),P=y.toLocaleTimeString("pt-BR"),L=(U=>U.length<=6?U:U.substring(0,3)+"*".repeat(U.length-6)+U.substring(U.length-3))(m.code),F=U=>{const q=document.createElement("canvas");return q.width=U.width,q.height=U.height,q.getContext("2d").drawImage(U,0,0),q.toDataURL("image/png")},D=new Image;D.src=Ct;let re=Ct;if(D.complete)try{re=F(D)}catch(U){console.error("Error converting logo to data URL",U)}const ae=`
        <html>
          <head>
            <title>Controle Interno - Vale-Presente</title>
            <style>
              @media print {
                @page {
                  size: 80mm auto;
                  margin: 0;
                }
                body {
                  margin: 0;
                  padding: 4mm;
                }
              }
              
              body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
              }
              
              .receipt {
                width: 72mm;
              }
              
              .header {
                text-align: center;
                margin-bottom: 10px;
              }
              
              .divider {
                border-top: 1px dashed #000;
                margin: 10px 0;
              }
              
              .info-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
              }
              
              .footer {
                text-align: center;
                margin-top: 10px;
                font-size: 10px;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <div class="header">
                <img src="${re}" alt="Logo" style="height: 30px; margin-bottom: 5px;" />
                <h3 style="margin: 5px 0;">CONTROLE INTERNO - VALE-PRESENTE</h3>
              </div>
              
              <div class="divider"></div>
              
              <div class="info-row">
                <span>Data:</span>
                <span>${E}</span>
              </div>
              
              <div class="info-row">
                <span>Hora:</span>
                <span>${P}</span>
              </div>
              
              <div class="info-row">
                <span>Código:</span>
                <span>${L}</span>
              </div>
              
              <div class="info-row">
                <span>Valor:</span>
                <span>R$ ${m.value.toFixed(2)}</span>
              </div>
              
              ${m.recipientName?`
              <div class="info-row">
                <span>Destinatário:</span>
                <span>${m.recipientName}</span>
              </div>
              `:""}
              
              <div class="info-row">
                <span>Comprador:</span>
                <span>${m.buyerName}</span>
              </div>
              
              <div class="divider"></div>
              
              <p style="text-align: center; margin: 10px 0;">Válido até ${l()}</p>
              
              <div class="divider"></div>
              
              <div class="footer">
                <p>CONTROLE INTERNO - NÃO ENTREGAR AO CLIENTE</p>
              </div>
            </div>
            
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;g.document.write(ae),g.document.close()}};return w.jsxs("div",{children:[w.jsxs("button",{onClick:d,className:"mb-4 flex items-center gap-2 px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors",children:[w.jsx(Ni,{size:20}),"Imprimir Vale-Presente",c.length>1?"s":""]}),w.jsx("div",{className:"print:m-0",children:s?w.jsx("div",{className:"grid grid-cols-3 gap-4",children:c.map(m=>w.jsx("div",{className:"mb-4 print:mb-0",children:w.jsxs("div",{className:"w-[226px] p-3 bg-white",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:Ct,alt:"Logo",className:"h-5 w-auto object-contain"})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-red-600 whitespace-nowrap text-sm",children:"Vale-Presente"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[m.recipientName&&w.jsxs("div",{children:[w.jsx("p",{className:"text-gray-600 text-xs",children:"Para:"}),w.jsx("p",{className:"font-medium text-gray-800 text-sm",children:m.recipientName})]}),w.jsxs("div",{className:`text-${m.recipientName?"right":"center"} ${m.recipientName?"":"w-full"}`,children:[w.jsx("p",{className:"text-gray-600 text-xs",children:"Valor:"}),w.jsxs("p",{className:"font-bold text-red-600 text-xl",children:["R$ ",m.value.toFixed(2)]})]})]}),w.jsx("div",{className:"flex justify-center mb-2",children:w.jsx("img",{src:`https://api.qrserver.com/v1/create-qr-code/?size=100&data=${encodeURIComponent(m.code)}`,alt:"QR Code",className:"w-[100px] h-[100px]"})}),w.jsx("div",{className:"text-center",children:w.jsx("p",{className:"font-mono tracking-wider text-gray-800 text-base",children:m.code})}),w.jsxs("div",{className:"mt-3 text-center text-gray-500",children:[w.jsxs("p",{className:"font-medium text-xs",children:["Um presente de: ",m.buyerName]}),w.jsxs("p",{className:"mt-1 text-[10px]",children:["Válido até ",l()]})]})]})},m.id||m.code))}):w.jsx("div",{className:"flex justify-center",children:w.jsxs("div",{className:"w-[302px] p-4 bg-white",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:Ct,alt:"Logo",className:"h-7 w-auto object-contain"})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-black whitespace-nowrap text-lg",children:"Vale-Presente"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[c[0].recipientName&&w.jsxs("div",{children:[w.jsx("p",{className:"text-sm",children:"Para:"}),w.jsx("p",{className:"font-medium text-base",children:c[0].recipientName})]}),w.jsxs("div",{className:`text-${c[0].recipientName?"right":"center"} ${c[0].recipientName?"":"w-full"}`,children:[w.jsx("p",{className:"text-sm",children:"Valor:"}),w.jsxs("p",{className:"font-bold text-2xl",children:["R$ ",c[0].value.toFixed(2)]})]})]}),w.jsx("div",{className:"flex justify-center mb-2",children:w.jsx("img",{src:`https://api.qrserver.com/v1/create-qr-code/?size=130&data=${encodeURIComponent(c[0].code)}`,alt:"QR Code",className:"w-[130px] h-[130px]"})}),w.jsx("div",{className:"text-center",children:w.jsx("p",{className:"font-mono tracking-wider text-lg",children:c[0].code})}),w.jsxs("div",{className:"mt-3 text-center",children:[w.jsxs("p",{className:"font-medium text-sm",children:["Um presente de: ",c[0].buyerName]}),w.jsxs("p",{className:"mt-1 text-xs",children:["Válido até ",l()]})]})]})})})]})}function gg({onGenerate:c}){const[s,i]=se.useState({quantity:"2",value:"",buyerName:""}),[l,a]=se.useState([]),[d,f]=se.useState("0.00"),[p,m]=se.useState(!1),[g,y]=se.useState(null),E=async L=>{L.preventDefault(),y(null);const F=parseInt(s.quantity,10);if(F<2||F>100){y("A quantidade deve estar entre 2 e 100");return}if(!s.value||!s.buyerName){y("Preencha todos os campos obrigatórios");return}try{m(!0);const D=P(),re=[];for(let ae=0;ae<F;ae++){const U={id:Ti(),code:O(D),value:Number(s.value),recipientName:"",buyerName:s.buyerName,createdAt:new Date,used:!1,isBulkGenerated:!0};re.push(U)}c(re),f(s.value),i({...s,quantity:"2",value:"",buyerName:""}),a(re)}catch(D){console.error("Error generating bulk cards:",D),y("Erro ao gerar vale-presentes em lote")}finally{m(!1)}},P=()=>{let L;do L=Math.floor(Math.random()*900+100).toString();while(L.startsWith("N"));return L},O=L=>{const F=Ti().replace(/-/g,"").substring(0,13).toUpperCase();return`${L}${F}`.substring(0,16)};return w.jsxs("div",{className:"space-y-8",children:[l.length>0&&w.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-4 text-center",children:"Vale-Presentes Gerados"}),w.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-md border border-gray-200",children:w.jsxs("p",{className:"text-sm text-gray-600",children:["Foram gerados ",w.jsx("span",{className:"font-medium",children:l.length})," vale-presentes no valor de ",w.jsxs("span",{className:"font-medium",children:["R$ ",Number(d).toFixed(2)]})," cada."]})}),w.jsx(Vd,{giftCards:l,isBulk:!0})]}),w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Gerar em Lote"}),w.jsxs("form",{onSubmit:E,className:"space-y-6",children:[g&&w.jsx("div",{className:"p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200",children:g}),p&&w.jsx("div",{className:"p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200",children:"Gerando vale-presentes..."}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Quantidade"}),w.jsx("input",{type:"number",min:"2",max:"100",value:s.quantity,onChange:L=>i({...s,quantity:L.target.value}),className:"form-input",placeholder:"Quantidade de vale-presentes a gerar"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Valor (R$)"}),w.jsx("input",{type:"number",step:"0.01",required:!0,value:s.value,onChange:L=>i({...s,value:L.target.value}),className:"form-input",placeholder:"0.00"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Comprador"}),w.jsx("input",{type:"text",required:!0,value:s.buyerName,onChange:L=>i({...s,buyerName:L.target.value}),className:"form-input",placeholder:"Nome de quem está comprando"})]}),w.jsxs("button",{type:"submit",disabled:p,className:"primary-button mt-4",children:[w.jsx(Ud,{className:"h-5 w-5"}),w.jsx("span",{children:p?"Gerando...":"Gerar Vale-Presentes"})]})]})]})]})}const Bd=se.createContext(void 0);function vg({children:c}){const[s,i]=se.useState(()=>localStorage.getItem("isAuthenticated")==="true");se.useEffect(()=>{localStorage.setItem("isAuthenticated",s.toString())},[s]);const l=d=>d==="gerente"?(i(!0),!0):!1,a=()=>{i(!1)};return w.jsx(Bd.Provider,{value:{isAuthenticated:s,login:l,logout:a},children:c})}function Hd(){const c=se.useContext(Bd);if(c===void 0)throw new Error("useAuth must be used within an AuthProvider");return c}function yg({isOpen:c,onClose:s}){const[i,l]=se.useState(""),[a,d]=se.useState(null),{isAuthenticated:f,login:p,logout:m}=Hd(),g=y=>{if(y.preventDefault(),d(null),f){m(),s();return}if(!i.trim()){d("Por favor, insira a senha");return}p(i)?(l(""),s()):d("Senha incorreta")};return c?w.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:w.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full",children:[w.jsxs("div",{className:"flex justify-between items-center mb-4",children:[w.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:f?"Bloquear Acesso":"Acesso Restrito"}),w.jsx("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:w.jsx(og,{className:"h-5 w-5"})})]}),a&&w.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm",children:a}),w.jsxs("form",{onSubmit:g,children:[f?w.jsx("div",{className:"mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-md",children:w.jsx("p",{children:"Você tem acesso às funcionalidades restritas. Deseja bloquear o acesso?"})}):w.jsxs("div",{className:"mb-4",children:[w.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Senha"}),w.jsx("input",{type:"password",id:"password",value:i,onChange:y=>l(y.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500",placeholder:"Digite a senha",autoFocus:!0})]}),w.jsx("div",{className:"flex justify-end",children:w.jsx("button",{type:"submit",className:`flex items-center space-x-2 px-4 py-2 rounded-md ${f?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-red-600 hover:bg-red-700 text-white"}`,children:f?w.jsxs(w.Fragment,{children:[w.jsx(Pi,{className:"h-4 w-4"}),w.jsx("span",{children:"Bloquear Acesso"})]}):w.jsxs(w.Fragment,{children:[w.jsx(Fd,{className:"h-4 w-4"}),w.jsx("span",{children:"Desbloquear"})]})})})]})]})}):null}class wg extends se.Component{constructor(s){super(s),this.state={hasError:!1,error:null}}static getDerivedStateFromError(s){return{hasError:!0,error:s}}componentDidCatch(s,i){console.error("Uncaught error:",s,i)}render(){return this.state.hasError?w.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:w.jsx("div",{className:"max-w-md w-full p-6 bg-white rounded-lg shadow-lg",children:w.jsxs("div",{className:"text-center",children:[w.jsx("h2",{className:"text-xl font-bold text-red-600 mb-4",children:"Ops! Algo deu errado."}),w.jsx("p",{className:"text-gray-600 mb-4",children:"Ocorreu um erro ao processar sua solicitação."}),this.state.error&&w.jsx("pre",{className:"mt-4 p-4 bg-gray-100 rounded text-sm text-gray-700 overflow-auto",children:this.state.error.message}),w.jsx("button",{onClick:()=>window.location.reload(),className:"mt-6 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Recarregar Página"})]})})}):this.props.children}}function _g(){const[c,s]=se.useState([]),[i,l]=se.useState("sell"),[a,d]=se.useState(!1),[f,p]=se.useState(null),[m,g]=se.useState(null),[y,E]=se.useState(!1),{isAuthenticated:P}=Hd();se.useEffect(()=>{O()},[]);const O=async()=>{p(null),d(!0);try{const{data:U,error:q}=await pt.from("gift_cards").select("*").order("created_at",{ascending:!1});if(q)throw q;if(U){const $=U.map(A=>({id:A.id,code:A.code,value:A.value,recipientName:A.recipient_name,buyerName:A.buyer_name,createdAt:new Date(A.created_at),used:A.used,usedAt:A.used_at?new Date(A.used_at):null,isBulkGenerated:!A.code.startsWith("N")&&A.code.length===16}));s($)}}catch(U){console.error("Error fetching gift cards:",U),p("Erro ao carregar os vale-presentes. Por favor, tente novamente.")}finally{d(!1)}},L=async U=>{p(null),d(!0);try{const{error:q}=await pt.from("gift_cards").insert({id:U.id,code:U.code,value:U.value,recipient_name:U.recipientName,buyer_name:U.buyerName,created_at:U.createdAt.toISOString(),used:!1});if(q)throw q;return s($=>[U,...$]),g(U),!0}catch(q){throw console.error("Error selling gift card:",q),p("Erro ao salvar vale-presente"),q}finally{d(!1)}},F=async U=>{p(null),d(!0);try{const q=U.map(A=>({id:A.id,code:A.code,value:A.value,recipient_name:A.recipientName,buyer_name:A.buyerName,created_at:A.createdAt.toISOString(),used:!1})),{error:$}=await pt.from("gift_cards").insert(q);if($)throw $;return s(A=>[...U,...A]),O(),U.length>0&&g(U[0]),!0}catch(q){throw console.error("Error generating bulk gift cards:",q),p("Erro ao gerar vale-presentes em lote"),q}finally{d(!1)}},D=async U=>{p(null),d(!0);try{const q=c.find(oe=>oe.code===U);if(!q)return console.log("Gift card not found with code:",U),{success:!1,message:"Vale-presente não encontrado."};if(q.used)return console.log("Gift card already used:",q),{success:!1,message:"Este vale-presente já foi utilizado."};const $=new Date,{error:A}=await pt.from("gift_cards").update({used:!0,used_at:$.toISOString()}).eq("code",U);if(A)throw console.error("Error updating gift card in database:",A),A;s(oe=>oe.map(pe=>pe.code===U?{...pe,used:!0,usedAt:$}:pe));const X={...q,used:!0,usedAt:$};return console.log("Successfully redeemed gift card:",X),{success:!0,message:"Vale-presente resgatado com sucesso!",giftCard:X}}catch(q){throw console.error("Error redeeming gift card:",q),p("Erro ao resgatar o vale-presente. Por favor, tente novamente."),q}finally{d(!1)}},re=U=>{l(U),g(null)},ae=()=>{E(!y)};return w.jsx(wg,{children:w.jsxs("div",{className:"min-h-screen bg-gray-50",children:[f&&w.jsx("div",{className:"mb-4 p-4 bg-red-50 text-red-600 rounded-md",children:f}),w.jsx("header",{className:"bg-white shadow-sm",children:w.jsx("div",{className:"max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8",children:w.jsxs("div",{className:"flex justify-between items-center mb-8",children:[w.jsx("div",{className:"flex-shrink-0",children:w.jsx("img",{src:Ct,alt:"New Look Logo",className:"h-16"})}),w.jsxs("div",{className:"flex items-center",children:[w.jsx("h1",{className:"text-2xl font-bold text-gray-800 mr-4",children:"Sistema de Vale-Presentes"}),w.jsx("button",{onClick:ae,className:"p-2 rounded-full hover:bg-gray-100 transition-colors duration-200",title:P?"Bloquear acesso":"Desbloquear acesso",children:P?w.jsx(Fd,{className:"h-5 w-5 text-green-600"}):w.jsx(Pi,{className:"h-5 w-5 text-gray-500"})})]})]})})}),w.jsxs("main",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[w.jsx(lg,{activeTab:i,onTabChange:re,isAuthenticated:P}),m&&i==="sell"&&w.jsxs("div",{className:"mb-8 bg-white shadow-sm rounded-lg p-6",children:[w.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-4 text-center",children:"Vale-Presente Gerado"}),w.jsx(Vd,{giftCards:[m],isFromVenderTab:!0})]}),w.jsxs("div",{className:"mt-6 bg-white shadow-sm rounded-lg p-6",children:[a&&w.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:w.jsx("div",{className:"bg-white p-4 rounded-md shadow-lg",children:w.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"})})}),i==="sell"&&w.jsx(fg,{onSell:L}),i==="bulk"&&P&&w.jsx(gg,{onGenerate:F}),i==="list"&&w.jsx(hg,{giftCards:c,isAuthenticated:P}),i==="redeem"&&w.jsx(mg,{onRedeem:D})]})]}),w.jsx(yg,{isOpen:y,onClose:()=>E(!1)})]})})}function xg(){return w.jsx(vg,{children:w.jsx(_g,{})})}async function kg(){try{console.log("Iniciando migração da tabela heartbeat...");const c=`
      CREATE TABLE IF NOT EXISTS heartbeat (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        timestamp timestamptz DEFAULT now(),
        status text DEFAULT 'alive'
      );
    `,s=`
      ALTER TABLE heartbeat ENABLE ROW LEVEL SECURITY;
    `,i=`
      -- Allow all authenticated users to read heartbeat
      CREATE POLICY IF NOT EXISTS "Anyone can read heartbeat"
        ON heartbeat
        FOR SELECT
        TO authenticated
        USING (true);

      -- Allow all authenticated users to insert heartbeat
      CREATE POLICY IF NOT EXISTS "Anyone can insert heartbeat"
        ON heartbeat
        FOR INSERT
        TO authenticated
        WITH CHECK (true);

      -- Allow all authenticated users to delete old heartbeat records
      CREATE POLICY IF NOT EXISTS "Anyone can delete heartbeat"
        ON heartbeat
        FOR DELETE
        TO authenticated
        USING (true);
    `,l=`
      CREATE INDEX IF NOT EXISTS idx_heartbeat_timestamp ON heartbeat(timestamp);
    `,{error:a}=await pt.rpc("exec_sql",{sql:c});if(a)throw console.error("Erro ao criar tabela:",a),a;const{error:d}=await pt.rpc("exec_sql",{sql:s});if(d)throw console.error("Erro ao habilitar RLS:",d),d;const{error:f}=await pt.rpc("exec_sql",{sql:i});if(f)throw console.error("Erro ao criar políticas:",f),f;const{error:p}=await pt.rpc("exec_sql",{sql:l});if(p)throw console.error("Erro ao criar índice:",p),p;console.log("Migração da tabela heartbeat concluída com sucesso!")}catch(c){throw console.error("Erro durante a migração:",c),c}}async function Sg(){try{const{data:c,error:s}=await pt.from("heartbeat").select("id").limit(1);return!s||s.code!=="42P01"}catch{return!1}}async function Eg(){await Sg()?console.log("Tabela heartbeat já existe."):(console.log("Tabela heartbeat não encontrada. Criando..."),await kg())}class jg{constructor(){yi(this,"intervalId",null);yi(this,"HEARTBEAT_INTERVAL",5*60*1e3);yi(this,"MAX_RECORDS",10)}async start(){if(this.intervalId){console.log("Heartbeat service já está rodando");return}console.log("Iniciando heartbeat service...");try{await Eg()}catch(s){console.error("Erro ao inicializar tabela heartbeat:",s)}this.sendHeartbeat(),this.intervalId=setInterval(()=>{this.sendHeartbeat()},this.HEARTBEAT_INTERVAL)}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null,console.log("Heartbeat service parado"))}async sendHeartbeat(){try{const{error:s}=await pt.from("heartbeat").insert({status:"alive",timestamp:new Date().toISOString()});if(s){console.error("Erro ao inserir heartbeat:",s);return}console.log("Heartbeat enviado com sucesso:",new Date().toISOString()),await this.cleanupOldRecords()}catch(s){console.error("Erro no heartbeat service:",s)}}async cleanupOldRecords(){try{const{data:s,error:i}=await pt.from("heartbeat").select("id, timestamp").order("timestamp",{ascending:!1});if(i){console.error("Erro ao buscar registros de heartbeat:",i);return}if(s&&s.length>this.MAX_RECORDS){const l=s.slice(this.MAX_RECORDS),a=l.map(f=>f.id),{error:d}=await pt.from("heartbeat").delete().in("id",a);d?console.error("Erro ao limpar registros antigos:",d):console.log(`Removidos ${l.length} registros antigos de heartbeat`)}}catch(s){console.error("Erro na limpeza de registros antigos:",s)}}isRunning(){return this.intervalId!==null}async forceHeartbeat(){await this.sendHeartbeat()}}const Cg=new jg;Cg.start();ep.createRoot(document.getElementById("root")).render(w.jsx(se.StrictMode,{children:w.jsx(xg,{})}));export{Hh as g};
//# sourceMappingURL=index-Cpa_Mu2B.js.map
