(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const d of a)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&l(f)}).observe(document,{childList:!0,subtree:!0});function i(a){const d={};return a.integrity&&(d.integrity=a.integrity),a.referrerPolicy&&(d.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?d.credentials="include":a.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function l(a){if(a.ep)return;a.ep=!0;const d=i(a);fetch(a.href,d)}})();function Fh(c){return c&&c.__esModule&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c}function Vh(c){if(c.__esModule)return c;var s=c.default;if(typeof s=="function"){var i=function l(){return this instanceof l?Reflect.construct(s,arguments,this.constructor):s.apply(this,arguments)};i.prototype=s.prototype}else i={};return Object.defineProperty(i,"__esModule",{value:!0}),Object.keys(c).forEach(function(l){var a=Object.getOwnPropertyDescriptor(c,l);Object.defineProperty(i,l,a.get?a:{enumerable:!0,get:function(){return c[l]}})}),i}var El={exports:{}},Zr={},jl={exports:{}},te={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vc;function Bh(){if(Vc)return te;Vc=1;var c=Symbol.for("react.element"),s=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),d=Symbol.for("react.provider"),f=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),E=Symbol.iterator;function P(k){return k===null||typeof k!="object"?null:(k=E&&k[E]||k["@@iterator"],typeof k=="function"?k:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},I=Object.assign,F={};function D(k,T,ee){this.props=k,this.context=T,this.refs=F,this.updater=ee||O}D.prototype.isReactComponent={},D.prototype.setState=function(k,T){if(typeof k!="object"&&typeof k!="function"&&k!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,k,T,"setState")},D.prototype.forceUpdate=function(k){this.updater.enqueueForceUpdate(this,k,"forceUpdate")};function ne(){}ne.prototype=D.prototype;function ae(k,T,ee){this.props=k,this.context=T,this.refs=F,this.updater=ee||O}var U=ae.prototype=new ne;U.constructor=ae,I(U,D.prototype),U.isPureReactComponent=!0;var q=Array.isArray,$=Object.prototype.hasOwnProperty,A={current:null},X={key:!0,ref:!0,__self:!0,__source:!0};function oe(k,T,ee){var re,ue={},ce=null,me=null;if(T!=null)for(re in T.ref!==void 0&&(me=T.ref),T.key!==void 0&&(ce=""+T.key),T)$.call(T,re)&&!X.hasOwnProperty(re)&&(ue[re]=T[re]);var fe=arguments.length-2;if(fe===1)ue.children=ee;else if(1<fe){for(var xe=Array(fe),tt=0;tt<fe;tt++)xe[tt]=arguments[tt+2];ue.children=xe}if(k&&k.defaultProps)for(re in fe=k.defaultProps,fe)ue[re]===void 0&&(ue[re]=fe[re]);return{$$typeof:c,type:k,key:ce,ref:me,props:ue,_owner:A.current}}function pe(k,T){return{$$typeof:c,type:k.type,key:T,ref:k.ref,props:k.props,_owner:k._owner}}function _e(k){return typeof k=="object"&&k!==null&&k.$$typeof===c}function lt(k){var T={"=":"=0",":":"=2"};return"$"+k.replace(/[=:]/g,function(ee){return T[ee]})}var mt=/\/+/g;function et(k,T){return typeof k=="object"&&k!==null&&k.key!=null?lt(""+k.key):T.toString(36)}function gt(k,T,ee,re,ue){var ce=typeof k;(ce==="undefined"||ce==="boolean")&&(k=null);var me=!1;if(k===null)me=!0;else switch(ce){case"string":case"number":me=!0;break;case"object":switch(k.$$typeof){case c:case s:me=!0}}if(me)return me=k,ue=ue(me),k=re===""?"."+et(me,0):re,q(ue)?(ee="",k!=null&&(ee=k.replace(mt,"$&/")+"/"),gt(ue,T,ee,"",function(tt){return tt})):ue!=null&&(_e(ue)&&(ue=pe(ue,ee+(!ue.key||me&&me.key===ue.key?"":(""+ue.key).replace(mt,"$&/")+"/")+k)),T.push(ue)),1;if(me=0,re=re===""?".":re+":",q(k))for(var fe=0;fe<k.length;fe++){ce=k[fe];var xe=re+et(ce,fe);me+=gt(ce,T,ee,xe,ue)}else if(xe=P(k),typeof xe=="function")for(k=xe.call(k),fe=0;!(ce=k.next()).done;)ce=ce.value,xe=re+et(ce,fe++),me+=gt(ce,T,ee,xe,ue);else if(ce==="object")throw T=String(k),Error("Objects are not valid as a React child (found: "+(T==="[object Object]"?"object with keys {"+Object.keys(k).join(", ")+"}":T)+"). If you meant to render a collection of children, use an array instead.");return me}function Ct(k,T,ee){if(k==null)return k;var re=[],ue=0;return gt(k,re,"","",function(ce){return T.call(ee,ce,ue++)}),re}function We(k){if(k._status===-1){var T=k._result;T=T(),T.then(function(ee){(k._status===0||k._status===-1)&&(k._status=1,k._result=ee)},function(ee){(k._status===0||k._status===-1)&&(k._status=2,k._result=ee)}),k._status===-1&&(k._status=0,k._result=T)}if(k._status===1)return k._result.default;throw k._result}var je={current:null},z={transition:null},J={ReactCurrentDispatcher:je,ReactCurrentBatchConfig:z,ReactCurrentOwner:A};function B(){throw Error("act(...) is not supported in production builds of React.")}return te.Children={map:Ct,forEach:function(k,T,ee){Ct(k,function(){T.apply(this,arguments)},ee)},count:function(k){var T=0;return Ct(k,function(){T++}),T},toArray:function(k){return Ct(k,function(T){return T})||[]},only:function(k){if(!_e(k))throw Error("React.Children.only expected to receive a single React element child.");return k}},te.Component=D,te.Fragment=i,te.Profiler=a,te.PureComponent=ae,te.StrictMode=l,te.Suspense=p,te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=J,te.act=B,te.cloneElement=function(k,T,ee){if(k==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+k+".");var re=I({},k.props),ue=k.key,ce=k.ref,me=k._owner;if(T!=null){if(T.ref!==void 0&&(ce=T.ref,me=A.current),T.key!==void 0&&(ue=""+T.key),k.type&&k.type.defaultProps)var fe=k.type.defaultProps;for(xe in T)$.call(T,xe)&&!X.hasOwnProperty(xe)&&(re[xe]=T[xe]===void 0&&fe!==void 0?fe[xe]:T[xe])}var xe=arguments.length-2;if(xe===1)re.children=ee;else if(1<xe){fe=Array(xe);for(var tt=0;tt<xe;tt++)fe[tt]=arguments[tt+2];re.children=fe}return{$$typeof:c,type:k.type,key:ue,ref:ce,props:re,_owner:me}},te.createContext=function(k){return k={$$typeof:f,_currentValue:k,_currentValue2:k,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},k.Provider={$$typeof:d,_context:k},k.Consumer=k},te.createElement=oe,te.createFactory=function(k){var T=oe.bind(null,k);return T.type=k,T},te.createRef=function(){return{current:null}},te.forwardRef=function(k){return{$$typeof:m,render:k}},te.isValidElement=_e,te.lazy=function(k){return{$$typeof:y,_payload:{_status:-1,_result:k},_init:We}},te.memo=function(k,T){return{$$typeof:g,type:k,compare:T===void 0?null:T}},te.startTransition=function(k){var T=z.transition;z.transition={};try{k()}finally{z.transition=T}},te.unstable_act=B,te.useCallback=function(k,T){return je.current.useCallback(k,T)},te.useContext=function(k){return je.current.useContext(k)},te.useDebugValue=function(){},te.useDeferredValue=function(k){return je.current.useDeferredValue(k)},te.useEffect=function(k,T){return je.current.useEffect(k,T)},te.useId=function(){return je.current.useId()},te.useImperativeHandle=function(k,T,ee){return je.current.useImperativeHandle(k,T,ee)},te.useInsertionEffect=function(k,T){return je.current.useInsertionEffect(k,T)},te.useLayoutEffect=function(k,T){return je.current.useLayoutEffect(k,T)},te.useMemo=function(k,T){return je.current.useMemo(k,T)},te.useReducer=function(k,T,ee){return je.current.useReducer(k,T,ee)},te.useRef=function(k){return je.current.useRef(k)},te.useState=function(k){return je.current.useState(k)},te.useSyncExternalStore=function(k,T,ee){return je.current.useSyncExternalStore(k,T,ee)},te.useTransition=function(){return je.current.useTransition()},te.version="18.3.1",te}var Bc;function Vl(){return Bc||(Bc=1,jl.exports=Bh()),jl.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hc;function Hh(){if(Hc)return Zr;Hc=1;var c=Vl(),s=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,a=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,d={key:!0,ref:!0,__self:!0,__source:!0};function f(m,p,g){var y,E={},P=null,O=null;g!==void 0&&(P=""+g),p.key!==void 0&&(P=""+p.key),p.ref!==void 0&&(O=p.ref);for(y in p)l.call(p,y)&&!d.hasOwnProperty(y)&&(E[y]=p[y]);if(m&&m.defaultProps)for(y in p=m.defaultProps,p)E[y]===void 0&&(E[y]=p[y]);return{$$typeof:s,type:m,key:P,ref:O,props:E,_owner:a.current}}return Zr.Fragment=i,Zr.jsx=f,Zr.jsxs=f,Zr}var qc;function qh(){return qc||(qc=1,El.exports=Hh()),El.exports}var w=qh(),se=Vl(),vi={},Cl={exports:{}},Xe={},Pl={exports:{}},Nl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wc;function Wh(){return Wc||(Wc=1,function(c){function s(z,J){var B=z.length;z.push(J);e:for(;0<B;){var k=B-1>>>1,T=z[k];if(0<a(T,J))z[k]=J,z[B]=T,B=k;else break e}}function i(z){return z.length===0?null:z[0]}function l(z){if(z.length===0)return null;var J=z[0],B=z.pop();if(B!==J){z[0]=B;e:for(var k=0,T=z.length,ee=T>>>1;k<ee;){var re=2*(k+1)-1,ue=z[re],ce=re+1,me=z[ce];if(0>a(ue,B))ce<T&&0>a(me,ue)?(z[k]=me,z[ce]=B,k=ce):(z[k]=ue,z[re]=B,k=re);else if(ce<T&&0>a(me,B))z[k]=me,z[ce]=B,k=ce;else break e}}return J}function a(z,J){var B=z.sortIndex-J.sortIndex;return B!==0?B:z.id-J.id}if(typeof performance=="object"&&typeof performance.now=="function"){var d=performance;c.unstable_now=function(){return d.now()}}else{var f=Date,m=f.now();c.unstable_now=function(){return f.now()-m}}var p=[],g=[],y=1,E=null,P=3,O=!1,I=!1,F=!1,D=typeof setTimeout=="function"?setTimeout:null,ne=typeof clearTimeout=="function"?clearTimeout:null,ae=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function U(z){for(var J=i(g);J!==null;){if(J.callback===null)l(g);else if(J.startTime<=z)l(g),J.sortIndex=J.expirationTime,s(p,J);else break;J=i(g)}}function q(z){if(F=!1,U(z),!I)if(i(p)!==null)I=!0,We($);else{var J=i(g);J!==null&&je(q,J.startTime-z)}}function $(z,J){I=!1,F&&(F=!1,ne(oe),oe=-1),O=!0;var B=P;try{for(U(J),E=i(p);E!==null&&(!(E.expirationTime>J)||z&&!lt());){var k=E.callback;if(typeof k=="function"){E.callback=null,P=E.priorityLevel;var T=k(E.expirationTime<=J);J=c.unstable_now(),typeof T=="function"?E.callback=T:E===i(p)&&l(p),U(J)}else l(p);E=i(p)}if(E!==null)var ee=!0;else{var re=i(g);re!==null&&je(q,re.startTime-J),ee=!1}return ee}finally{E=null,P=B,O=!1}}var A=!1,X=null,oe=-1,pe=5,_e=-1;function lt(){return!(c.unstable_now()-_e<pe)}function mt(){if(X!==null){var z=c.unstable_now();_e=z;var J=!0;try{J=X(!0,z)}finally{J?et():(A=!1,X=null)}}else A=!1}var et;if(typeof ae=="function")et=function(){ae(mt)};else if(typeof MessageChannel<"u"){var gt=new MessageChannel,Ct=gt.port2;gt.port1.onmessage=mt,et=function(){Ct.postMessage(null)}}else et=function(){D(mt,0)};function We(z){X=z,A||(A=!0,et())}function je(z,J){oe=D(function(){z(c.unstable_now())},J)}c.unstable_IdlePriority=5,c.unstable_ImmediatePriority=1,c.unstable_LowPriority=4,c.unstable_NormalPriority=3,c.unstable_Profiling=null,c.unstable_UserBlockingPriority=2,c.unstable_cancelCallback=function(z){z.callback=null},c.unstable_continueExecution=function(){I||O||(I=!0,We($))},c.unstable_forceFrameRate=function(z){0>z||125<z?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):pe=0<z?Math.floor(1e3/z):5},c.unstable_getCurrentPriorityLevel=function(){return P},c.unstable_getFirstCallbackNode=function(){return i(p)},c.unstable_next=function(z){switch(P){case 1:case 2:case 3:var J=3;break;default:J=P}var B=P;P=J;try{return z()}finally{P=B}},c.unstable_pauseExecution=function(){},c.unstable_requestPaint=function(){},c.unstable_runWithPriority=function(z,J){switch(z){case 1:case 2:case 3:case 4:case 5:break;default:z=3}var B=P;P=z;try{return J()}finally{P=B}},c.unstable_scheduleCallback=function(z,J,B){var k=c.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?k+B:k):B=k,z){case 1:var T=-1;break;case 2:T=250;break;case 5:T=**********;break;case 4:T=1e4;break;default:T=5e3}return T=B+T,z={id:y++,callback:J,priorityLevel:z,startTime:B,expirationTime:T,sortIndex:-1},B>k?(z.sortIndex=B,s(g,z),i(p)===null&&z===i(g)&&(F?(ne(oe),oe=-1):F=!0,je(q,B-k))):(z.sortIndex=T,s(p,z),I||O||(I=!0,We($))),z},c.unstable_shouldYield=lt,c.unstable_wrapCallback=function(z){var J=P;return function(){var B=P;P=J;try{return z.apply(this,arguments)}finally{P=B}}}}(Nl)),Nl}var Gc;function Gh(){return Gc||(Gc=1,Pl.exports=Wh()),Pl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qc;function Qh(){if(Qc)return Xe;Qc=1;var c=Vl(),s=Gh();function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,a={};function d(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(a[e]=t,e=0;e<t.length;e++)l.add(t[e])}var m=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,g=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,y={},E={};function P(e){return p.call(E,e)?!0:p.call(y,e)?!1:g.test(e)?E[e]=!0:(y[e]=!0,!1)}function O(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function I(e,t,n,r){if(t===null||typeof t>"u"||O(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function F(e,t,n,r,o,u,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=u,this.removeEmptyString=h}var D={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){D[e]=new F(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];D[t]=new F(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){D[e]=new F(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){D[e]=new F(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){D[e]=new F(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){D[e]=new F(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){D[e]=new F(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){D[e]=new F(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){D[e]=new F(e,5,!1,e.toLowerCase(),null,!1,!1)});var ne=/[\-:]([a-z])/g;function ae(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(ne,ae);D[t]=new F(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(ne,ae);D[t]=new F(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(ne,ae);D[t]=new F(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){D[e]=new F(e,1,!1,e.toLowerCase(),null,!1,!1)}),D.xlinkHref=new F("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){D[e]=new F(e,1,!1,e.toLowerCase(),null,!0,!0)});function U(e,t,n,r){var o=D.hasOwnProperty(t)?D[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(I(t,n,o,r)&&(n=null),r||o===null?P(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var q=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$=Symbol.for("react.element"),A=Symbol.for("react.portal"),X=Symbol.for("react.fragment"),oe=Symbol.for("react.strict_mode"),pe=Symbol.for("react.profiler"),_e=Symbol.for("react.provider"),lt=Symbol.for("react.context"),mt=Symbol.for("react.forward_ref"),et=Symbol.for("react.suspense"),gt=Symbol.for("react.suspense_list"),Ct=Symbol.for("react.memo"),We=Symbol.for("react.lazy"),je=Symbol.for("react.offscreen"),z=Symbol.iterator;function J(e){return e===null||typeof e!="object"?null:(e=z&&e[z]||e["@@iterator"],typeof e=="function"?e:null)}var B=Object.assign,k;function T(e){if(k===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);k=t&&t[1]||""}return`
`+k+e}var ee=!1;function re(e,t){if(!e||ee)return"";ee=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(C){var r=C}Reflect.construct(e,[],t)}else{try{t.call()}catch(C){r=C}e.call(t.prototype)}else{try{throw Error()}catch(C){r=C}e()}}catch(C){if(C&&r&&typeof C.stack=="string"){for(var o=C.stack.split(`
`),u=r.stack.split(`
`),h=o.length-1,v=u.length-1;1<=h&&0<=v&&o[h]!==u[v];)v--;for(;1<=h&&0<=v;h--,v--)if(o[h]!==u[v]){if(h!==1||v!==1)do if(h--,v--,0>v||o[h]!==u[v]){var _=`
`+o[h].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=h&&0<=v);break}}}finally{ee=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?T(e):""}function ue(e){switch(e.tag){case 5:return T(e.type);case 16:return T("Lazy");case 13:return T("Suspense");case 19:return T("SuspenseList");case 0:case 2:case 15:return e=re(e.type,!1),e;case 11:return e=re(e.type.render,!1),e;case 1:return e=re(e.type,!0),e;default:return""}}function ce(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case X:return"Fragment";case A:return"Portal";case pe:return"Profiler";case oe:return"StrictMode";case et:return"Suspense";case gt:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case lt:return(e.displayName||"Context")+".Consumer";case _e:return(e._context.displayName||"Context")+".Provider";case mt:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ct:return t=e.displayName||null,t!==null?t:ce(e.type)||"Memo";case We:t=e._payload,e=e._init;try{return ce(e(t))}catch{}}return null}function me(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ce(t);case 8:return t===oe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function fe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xe(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tt(e){var t=xe(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,u=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(h){r=""+h,u.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(h){r=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ls(e){e._valueTracker||(e._valueTracker=tt(e))}function Ql(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=xe(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function as(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ri(e,t){var n=t.checked;return B({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Kl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=fe(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Jl(e,t){t=t.checked,t!=null&&U(e,"checked",t,!1)}function bi(e,t){Jl(e,t);var n=fe(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Oi(e,t.type,n):t.hasOwnProperty("defaultValue")&&Oi(e,t.type,fe(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Oi(e,t,n){(t!=="number"||as(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var pr=Array.isArray;function Rn(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+fe(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function $i(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(i(91));return B({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xl(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(i(92));if(pr(n)){if(1<n.length)throw Error(i(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:fe(n)}}function Zl(e,t){var n=fe(t.value),r=fe(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ea(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ta(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Li(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ta(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var us,na=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(us=us||document.createElement("div"),us.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=us.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function mr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var gr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Hd=["Webkit","ms","Moz","O"];Object.keys(gr).forEach(function(e){Hd.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),gr[t]=gr[e]})});function ra(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||gr.hasOwnProperty(e)&&gr[e]?(""+t).trim():t+"px"}function sa(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=ra(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var qd=B({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ii(e,t){if(t){if(qd[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(i(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(i(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(t.style!=null&&typeof t.style!="object")throw Error(i(62))}}function Ai(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Di=null;function zi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ui=null,bn=null,On=null;function ia(e){if(e=zr(e)){if(typeof Ui!="function")throw Error(i(280));var t=e.stateNode;t&&(t=Os(t),Ui(e.stateNode,e.type,t))}}function oa(e){bn?On?On.push(e):On=[e]:bn=e}function la(){if(bn){var e=bn,t=On;if(On=bn=null,ia(e),t)for(e=0;e<t.length;e++)ia(t[e])}}function aa(e,t){return e(t)}function ua(){}var Mi=!1;function ca(e,t,n){if(Mi)return e(t,n);Mi=!0;try{return aa(e,t,n)}finally{Mi=!1,(bn!==null||On!==null)&&(ua(),la())}}function vr(e,t){var n=e.stateNode;if(n===null)return null;var r=Os(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(i(231,t,typeof n));return n}var Fi=!1;if(m)try{var yr={};Object.defineProperty(yr,"passive",{get:function(){Fi=!0}}),window.addEventListener("test",yr,yr),window.removeEventListener("test",yr,yr)}catch{Fi=!1}function Wd(e,t,n,r,o,u,h,v,_){var C=Array.prototype.slice.call(arguments,3);try{t.apply(n,C)}catch(R){this.onError(R)}}var wr=!1,cs=null,ds=!1,Vi=null,Gd={onError:function(e){wr=!0,cs=e}};function Qd(e,t,n,r,o,u,h,v,_){wr=!1,cs=null,Wd.apply(Gd,arguments)}function Kd(e,t,n,r,o,u,h,v,_){if(Qd.apply(this,arguments),wr){if(wr){var C=cs;wr=!1,cs=null}else throw Error(i(198));ds||(ds=!0,Vi=C)}}function fn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function da(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function fa(e){if(fn(e)!==e)throw Error(i(188))}function Jd(e){var t=e.alternate;if(!t){if(t=fn(e),t===null)throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var u=o.alternate;if(u===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===n)return fa(o),e;if(u===r)return fa(o),t;u=u.sibling}throw Error(i(188))}if(n.return!==r.return)n=o,r=u;else{for(var h=!1,v=o.child;v;){if(v===n){h=!0,n=o,r=u;break}if(v===r){h=!0,r=o,n=u;break}v=v.sibling}if(!h){for(v=u.child;v;){if(v===n){h=!0,n=u,r=o;break}if(v===r){h=!0,r=u,n=o;break}v=v.sibling}if(!h)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(n.tag!==3)throw Error(i(188));return n.stateNode.current===n?e:t}function ha(e){return e=Jd(e),e!==null?pa(e):null}function pa(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=pa(e);if(t!==null)return t;e=e.sibling}return null}var ma=s.unstable_scheduleCallback,ga=s.unstable_cancelCallback,Yd=s.unstable_shouldYield,Xd=s.unstable_requestPaint,Pe=s.unstable_now,Zd=s.unstable_getCurrentPriorityLevel,Bi=s.unstable_ImmediatePriority,va=s.unstable_UserBlockingPriority,fs=s.unstable_NormalPriority,ef=s.unstable_LowPriority,ya=s.unstable_IdlePriority,hs=null,Pt=null;function tf(e){if(Pt&&typeof Pt.onCommitFiberRoot=="function")try{Pt.onCommitFiberRoot(hs,e,void 0,(e.current.flags&128)===128)}catch{}}var vt=Math.clz32?Math.clz32:sf,nf=Math.log,rf=Math.LN2;function sf(e){return e>>>=0,e===0?32:31-(nf(e)/rf|0)|0}var ps=64,ms=4194304;function _r(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function gs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,u=e.pingedLanes,h=n&268435455;if(h!==0){var v=h&~o;v!==0?r=_r(v):(u&=h,u!==0&&(r=_r(u)))}else h=n&~o,h!==0?r=_r(h):u!==0&&(r=_r(u));if(r===0)return 0;if(t!==0&&t!==r&&(t&o)===0&&(o=r&-r,u=t&-t,o>=u||o===16&&(u&4194240)!==0))return t;if((r&4)!==0&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-vt(t),o=1<<n,r|=e[n],t&=~o;return r}function of(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function lf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,u=e.pendingLanes;0<u;){var h=31-vt(u),v=1<<h,_=o[h];_===-1?((v&n)===0||(v&r)!==0)&&(o[h]=of(v,t)):_<=t&&(e.expiredLanes|=v),u&=~v}}function Hi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function wa(){var e=ps;return ps<<=1,(ps&4194240)===0&&(ps=64),e}function qi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function xr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-vt(t),e[t]=n}function af(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-vt(n),u=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~u}}function Wi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-vt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var he=0;function _a(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var xa,Gi,ka,Sa,Ea,Qi=!1,vs=[],Vt=null,Bt=null,Ht=null,kr=new Map,Sr=new Map,qt=[],uf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ja(e,t){switch(e){case"focusin":case"focusout":Vt=null;break;case"dragenter":case"dragleave":Bt=null;break;case"mouseover":case"mouseout":Ht=null;break;case"pointerover":case"pointerout":kr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Sr.delete(t.pointerId)}}function Er(e,t,n,r,o,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:u,targetContainers:[o]},t!==null&&(t=zr(t),t!==null&&Gi(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function cf(e,t,n,r,o){switch(t){case"focusin":return Vt=Er(Vt,e,t,n,r,o),!0;case"dragenter":return Bt=Er(Bt,e,t,n,r,o),!0;case"mouseover":return Ht=Er(Ht,e,t,n,r,o),!0;case"pointerover":var u=o.pointerId;return kr.set(u,Er(kr.get(u)||null,e,t,n,r,o)),!0;case"gotpointercapture":return u=o.pointerId,Sr.set(u,Er(Sr.get(u)||null,e,t,n,r,o)),!0}return!1}function Ca(e){var t=hn(e.target);if(t!==null){var n=fn(t);if(n!==null){if(t=n.tag,t===13){if(t=da(n),t!==null){e.blockedOn=t,Ea(e.priority,function(){ka(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ys(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ji(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Di=r,n.target.dispatchEvent(r),Di=null}else return t=zr(n),t!==null&&Gi(t),e.blockedOn=n,!1;t.shift()}return!0}function Pa(e,t,n){ys(e)&&n.delete(t)}function df(){Qi=!1,Vt!==null&&ys(Vt)&&(Vt=null),Bt!==null&&ys(Bt)&&(Bt=null),Ht!==null&&ys(Ht)&&(Ht=null),kr.forEach(Pa),Sr.forEach(Pa)}function jr(e,t){e.blockedOn===t&&(e.blockedOn=null,Qi||(Qi=!0,s.unstable_scheduleCallback(s.unstable_NormalPriority,df)))}function Cr(e){function t(o){return jr(o,e)}if(0<vs.length){jr(vs[0],e);for(var n=1;n<vs.length;n++){var r=vs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Vt!==null&&jr(Vt,e),Bt!==null&&jr(Bt,e),Ht!==null&&jr(Ht,e),kr.forEach(t),Sr.forEach(t),n=0;n<qt.length;n++)r=qt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<qt.length&&(n=qt[0],n.blockedOn===null);)Ca(n),n.blockedOn===null&&qt.shift()}var $n=q.ReactCurrentBatchConfig,ws=!0;function ff(e,t,n,r){var o=he,u=$n.transition;$n.transition=null;try{he=1,Ki(e,t,n,r)}finally{he=o,$n.transition=u}}function hf(e,t,n,r){var o=he,u=$n.transition;$n.transition=null;try{he=4,Ki(e,t,n,r)}finally{he=o,$n.transition=u}}function Ki(e,t,n,r){if(ws){var o=Ji(e,t,n,r);if(o===null)po(e,t,r,_s,n),ja(e,r);else if(cf(o,e,t,n,r))r.stopPropagation();else if(ja(e,r),t&4&&-1<uf.indexOf(e)){for(;o!==null;){var u=zr(o);if(u!==null&&xa(u),u=Ji(e,t,n,r),u===null&&po(e,t,r,_s,n),u===o)break;o=u}o!==null&&r.stopPropagation()}else po(e,t,r,null,n)}}var _s=null;function Ji(e,t,n,r){if(_s=null,e=zi(r),e=hn(e),e!==null)if(t=fn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=da(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return _s=e,null}function Na(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zd()){case Bi:return 1;case va:return 4;case fs:case ef:return 16;case ya:return 536870912;default:return 16}default:return 16}}var Wt=null,Yi=null,xs=null;function Ta(){if(xs)return xs;var e,t=Yi,n=t.length,r,o="value"in Wt?Wt.value:Wt.textContent,u=o.length;for(e=0;e<n&&t[e]===o[e];e++);var h=n-e;for(r=1;r<=h&&t[n-r]===o[u-r];r++);return xs=o.slice(e,1<r?1-r:void 0)}function ks(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ss(){return!0}function Ra(){return!1}function nt(e){function t(n,r,o,u,h){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=u,this.target=h,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(n=e[v],this[v]=n?n(u):u[v]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Ss:Ra,this.isPropagationStopped=Ra,this}return B(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ss)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ss)},persist:function(){},isPersistent:Ss}),t}var Ln={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xi=nt(Ln),Pr=B({},Ln,{view:0,detail:0}),pf=nt(Pr),Zi,eo,Nr,Es=B({},Pr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:no,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Nr&&(Nr&&e.type==="mousemove"?(Zi=e.screenX-Nr.screenX,eo=e.screenY-Nr.screenY):eo=Zi=0,Nr=e),Zi)},movementY:function(e){return"movementY"in e?e.movementY:eo}}),ba=nt(Es),mf=B({},Es,{dataTransfer:0}),gf=nt(mf),vf=B({},Pr,{relatedTarget:0}),to=nt(vf),yf=B({},Ln,{animationName:0,elapsedTime:0,pseudoElement:0}),wf=nt(yf),_f=B({},Ln,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),xf=nt(_f),kf=B({},Ln,{data:0}),Oa=nt(kf),Sf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Ef={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},jf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=jf[e])?!!t[e]:!1}function no(){return Cf}var Pf=B({},Pr,{key:function(e){if(e.key){var t=Sf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ks(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Ef[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:no,charCode:function(e){return e.type==="keypress"?ks(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ks(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nf=nt(Pf),Tf=B({},Es,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$a=nt(Tf),Rf=B({},Pr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:no}),bf=nt(Rf),Of=B({},Ln,{propertyName:0,elapsedTime:0,pseudoElement:0}),$f=nt(Of),Lf=B({},Es,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),If=nt(Lf),Af=[9,13,27,32],ro=m&&"CompositionEvent"in window,Tr=null;m&&"documentMode"in document&&(Tr=document.documentMode);var Df=m&&"TextEvent"in window&&!Tr,La=m&&(!ro||Tr&&8<Tr&&11>=Tr),Ia=" ",Aa=!1;function Da(e,t){switch(e){case"keyup":return Af.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function za(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var In=!1;function zf(e,t){switch(e){case"compositionend":return za(t);case"keypress":return t.which!==32?null:(Aa=!0,Ia);case"textInput":return e=t.data,e===Ia&&Aa?null:e;default:return null}}function Uf(e,t){if(In)return e==="compositionend"||!ro&&Da(e,t)?(e=Ta(),xs=Yi=Wt=null,In=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return La&&t.locale!=="ko"?null:t.data;default:return null}}var Mf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ua(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Mf[e.type]:t==="textarea"}function Ma(e,t,n,r){oa(r),t=Ts(t,"onChange"),0<t.length&&(n=new Xi("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Rr=null,br=null;function Ff(e){su(e,0)}function js(e){var t=Mn(e);if(Ql(t))return e}function Vf(e,t){if(e==="change")return t}var Fa=!1;if(m){var so;if(m){var io="oninput"in document;if(!io){var Va=document.createElement("div");Va.setAttribute("oninput","return;"),io=typeof Va.oninput=="function"}so=io}else so=!1;Fa=so&&(!document.documentMode||9<document.documentMode)}function Ba(){Rr&&(Rr.detachEvent("onpropertychange",Ha),br=Rr=null)}function Ha(e){if(e.propertyName==="value"&&js(br)){var t=[];Ma(t,br,e,zi(e)),ca(Ff,t)}}function Bf(e,t,n){e==="focusin"?(Ba(),Rr=t,br=n,Rr.attachEvent("onpropertychange",Ha)):e==="focusout"&&Ba()}function Hf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return js(br)}function qf(e,t){if(e==="click")return js(t)}function Wf(e,t){if(e==="input"||e==="change")return js(t)}function Gf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:Gf;function Or(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!p.call(t,o)||!yt(e[o],t[o]))return!1}return!0}function qa(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wa(e,t){var n=qa(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=qa(n)}}function Ga(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ga(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Qa(){for(var e=window,t=as();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=as(e.document)}return t}function oo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Qf(e){var t=Qa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ga(n.ownerDocument.documentElement,n)){if(r!==null&&oo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,u=Math.min(r.start,o);r=r.end===void 0?u:Math.min(r.end,o),!e.extend&&u>r&&(o=r,r=u,u=o),o=Wa(n,u);var h=Wa(n,r);o&&h&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),u>r?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Kf=m&&"documentMode"in document&&11>=document.documentMode,An=null,lo=null,$r=null,ao=!1;function Ka(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ao||An==null||An!==as(r)||(r=An,"selectionStart"in r&&oo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),$r&&Or($r,r)||($r=r,r=Ts(lo,"onSelect"),0<r.length&&(t=new Xi("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=An)))}function Cs(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Dn={animationend:Cs("Animation","AnimationEnd"),animationiteration:Cs("Animation","AnimationIteration"),animationstart:Cs("Animation","AnimationStart"),transitionend:Cs("Transition","TransitionEnd")},uo={},Ja={};m&&(Ja=document.createElement("div").style,"AnimationEvent"in window||(delete Dn.animationend.animation,delete Dn.animationiteration.animation,delete Dn.animationstart.animation),"TransitionEvent"in window||delete Dn.transitionend.transition);function Ps(e){if(uo[e])return uo[e];if(!Dn[e])return e;var t=Dn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ja)return uo[e]=t[n];return e}var Ya=Ps("animationend"),Xa=Ps("animationiteration"),Za=Ps("animationstart"),eu=Ps("transitionend"),tu=new Map,nu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Gt(e,t){tu.set(e,t),d(t,[e])}for(var co=0;co<nu.length;co++){var fo=nu[co],Jf=fo.toLowerCase(),Yf=fo[0].toUpperCase()+fo.slice(1);Gt(Jf,"on"+Yf)}Gt(Ya,"onAnimationEnd"),Gt(Xa,"onAnimationIteration"),Gt(Za,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(eu,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),d("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),d("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),d("onBeforeInput",["compositionend","keypress","textInput","paste"]),d("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),d("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Lr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Lr));function ru(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Kd(r,t,void 0,e),e.currentTarget=null}function su(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var u=void 0;if(t)for(var h=r.length-1;0<=h;h--){var v=r[h],_=v.instance,C=v.currentTarget;if(v=v.listener,_!==u&&o.isPropagationStopped())break e;ru(o,v,C),u=_}else for(h=0;h<r.length;h++){if(v=r[h],_=v.instance,C=v.currentTarget,v=v.listener,_!==u&&o.isPropagationStopped())break e;ru(o,v,C),u=_}}}if(ds)throw e=Vi,ds=!1,Vi=null,e}function ye(e,t){var n=t[_o];n===void 0&&(n=t[_o]=new Set);var r=e+"__bubble";n.has(r)||(iu(t,e,2,!1),n.add(r))}function ho(e,t,n){var r=0;t&&(r|=4),iu(n,e,r,t)}var Ns="_reactListening"+Math.random().toString(36).slice(2);function Ir(e){if(!e[Ns]){e[Ns]=!0,l.forEach(function(n){n!=="selectionchange"&&(Xf.has(n)||ho(n,!1,e),ho(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ns]||(t[Ns]=!0,ho("selectionchange",!1,t))}}function iu(e,t,n,r){switch(Na(t)){case 1:var o=ff;break;case 4:o=hf;break;default:o=Ki}n=o.bind(null,t,n,e),o=void 0,!Fi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function po(e,t,n,r,o){var u=r;if((t&1)===0&&(t&2)===0&&r!==null)e:for(;;){if(r===null)return;var h=r.tag;if(h===3||h===4){var v=r.stateNode.containerInfo;if(v===o||v.nodeType===8&&v.parentNode===o)break;if(h===4)for(h=r.return;h!==null;){var _=h.tag;if((_===3||_===4)&&(_=h.stateNode.containerInfo,_===o||_.nodeType===8&&_.parentNode===o))return;h=h.return}for(;v!==null;){if(h=hn(v),h===null)return;if(_=h.tag,_===5||_===6){r=u=h;continue e}v=v.parentNode}}r=r.return}ca(function(){var C=u,R=zi(n),b=[];e:{var N=tu.get(e);if(N!==void 0){var M=Xi,H=e;switch(e){case"keypress":if(ks(n)===0)break e;case"keydown":case"keyup":M=Nf;break;case"focusin":H="focus",M=to;break;case"focusout":H="blur",M=to;break;case"beforeblur":case"afterblur":M=to;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=ba;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=gf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=bf;break;case Ya:case Xa:case Za:M=wf;break;case eu:M=$f;break;case"scroll":M=pf;break;case"wheel":M=If;break;case"copy":case"cut":case"paste":M=xf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=$a}var W=(t&4)!==0,Ne=!W&&e==="scroll",S=W?N!==null?N+"Capture":null:N;W=[];for(var x=C,j;x!==null;){j=x;var L=j.stateNode;if(j.tag===5&&L!==null&&(j=L,S!==null&&(L=vr(x,S),L!=null&&W.push(Ar(x,L,j)))),Ne)break;x=x.return}0<W.length&&(N=new M(N,H,null,n,R),b.push({event:N,listeners:W}))}}if((t&7)===0){e:{if(N=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",N&&n!==Di&&(H=n.relatedTarget||n.fromElement)&&(hn(H)||H[$t]))break e;if((M||N)&&(N=R.window===R?R:(N=R.ownerDocument)?N.defaultView||N.parentWindow:window,M?(H=n.relatedTarget||n.toElement,M=C,H=H?hn(H):null,H!==null&&(Ne=fn(H),H!==Ne||H.tag!==5&&H.tag!==6)&&(H=null)):(M=null,H=C),M!==H)){if(W=ba,L="onMouseLeave",S="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(W=$a,L="onPointerLeave",S="onPointerEnter",x="pointer"),Ne=M==null?N:Mn(M),j=H==null?N:Mn(H),N=new W(L,x+"leave",M,n,R),N.target=Ne,N.relatedTarget=j,L=null,hn(R)===C&&(W=new W(S,x+"enter",H,n,R),W.target=j,W.relatedTarget=Ne,L=W),Ne=L,M&&H)t:{for(W=M,S=H,x=0,j=W;j;j=zn(j))x++;for(j=0,L=S;L;L=zn(L))j++;for(;0<x-j;)W=zn(W),x--;for(;0<j-x;)S=zn(S),j--;for(;x--;){if(W===S||S!==null&&W===S.alternate)break t;W=zn(W),S=zn(S)}W=null}else W=null;M!==null&&ou(b,N,M,W,!1),H!==null&&Ne!==null&&ou(b,Ne,H,W,!0)}}e:{if(N=C?Mn(C):window,M=N.nodeName&&N.nodeName.toLowerCase(),M==="select"||M==="input"&&N.type==="file")var G=Vf;else if(Ua(N))if(Fa)G=Wf;else{G=Hf;var Q=Bf}else(M=N.nodeName)&&M.toLowerCase()==="input"&&(N.type==="checkbox"||N.type==="radio")&&(G=qf);if(G&&(G=G(e,C))){Ma(b,G,n,R);break e}Q&&Q(e,N,C),e==="focusout"&&(Q=N._wrapperState)&&Q.controlled&&N.type==="number"&&Oi(N,"number",N.value)}switch(Q=C?Mn(C):window,e){case"focusin":(Ua(Q)||Q.contentEditable==="true")&&(An=Q,lo=C,$r=null);break;case"focusout":$r=lo=An=null;break;case"mousedown":ao=!0;break;case"contextmenu":case"mouseup":case"dragend":ao=!1,Ka(b,n,R);break;case"selectionchange":if(Kf)break;case"keydown":case"keyup":Ka(b,n,R)}var K;if(ro)e:{switch(e){case"compositionstart":var Y="onCompositionStart";break e;case"compositionend":Y="onCompositionEnd";break e;case"compositionupdate":Y="onCompositionUpdate";break e}Y=void 0}else In?Da(e,n)&&(Y="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(Y="onCompositionStart");Y&&(La&&n.locale!=="ko"&&(In||Y!=="onCompositionStart"?Y==="onCompositionEnd"&&In&&(K=Ta()):(Wt=R,Yi="value"in Wt?Wt.value:Wt.textContent,In=!0)),Q=Ts(C,Y),0<Q.length&&(Y=new Oa(Y,e,null,n,R),b.push({event:Y,listeners:Q}),K?Y.data=K:(K=za(n),K!==null&&(Y.data=K)))),(K=Df?zf(e,n):Uf(e,n))&&(C=Ts(C,"onBeforeInput"),0<C.length&&(R=new Oa("onBeforeInput","beforeinput",null,n,R),b.push({event:R,listeners:C}),R.data=K))}su(b,t)})}function Ar(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ts(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,u=o.stateNode;o.tag===5&&u!==null&&(o=u,u=vr(e,n),u!=null&&r.unshift(Ar(e,u,o)),u=vr(e,t),u!=null&&r.push(Ar(e,u,o))),e=e.return}return r}function zn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ou(e,t,n,r,o){for(var u=t._reactName,h=[];n!==null&&n!==r;){var v=n,_=v.alternate,C=v.stateNode;if(_!==null&&_===r)break;v.tag===5&&C!==null&&(v=C,o?(_=vr(n,u),_!=null&&h.unshift(Ar(n,_,v))):o||(_=vr(n,u),_!=null&&h.push(Ar(n,_,v)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var Zf=/\r\n?/g,eh=/\u0000|\uFFFD/g;function lu(e){return(typeof e=="string"?e:""+e).replace(Zf,`
`).replace(eh,"")}function Rs(e,t,n){if(t=lu(t),lu(e)!==t&&n)throw Error(i(425))}function bs(){}var mo=null,go=null;function vo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var yo=typeof setTimeout=="function"?setTimeout:void 0,th=typeof clearTimeout=="function"?clearTimeout:void 0,au=typeof Promise=="function"?Promise:void 0,nh=typeof queueMicrotask=="function"?queueMicrotask:typeof au<"u"?function(e){return au.resolve(null).then(e).catch(rh)}:yo;function rh(e){setTimeout(function(){throw e})}function wo(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Cr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Cr(t)}function Qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function uu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Un=Math.random().toString(36).slice(2),Nt="__reactFiber$"+Un,Dr="__reactProps$"+Un,$t="__reactContainer$"+Un,_o="__reactEvents$"+Un,sh="__reactListeners$"+Un,ih="__reactHandles$"+Un;function hn(e){var t=e[Nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[$t]||n[Nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=uu(e);e!==null;){if(n=e[Nt])return n;e=uu(e)}return t}e=n,n=e.parentNode}return null}function zr(e){return e=e[Nt]||e[$t],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Mn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(i(33))}function Os(e){return e[Dr]||null}var xo=[],Fn=-1;function Kt(e){return{current:e}}function we(e){0>Fn||(e.current=xo[Fn],xo[Fn]=null,Fn--)}function ge(e,t){Fn++,xo[Fn]=e.current,e.current=t}var Jt={},Me=Kt(Jt),Ge=Kt(!1),pn=Jt;function Vn(e,t){var n=e.type.contextTypes;if(!n)return Jt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},u;for(u in n)o[u]=t[u];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Qe(e){return e=e.childContextTypes,e!=null}function $s(){we(Ge),we(Me)}function cu(e,t,n){if(Me.current!==Jt)throw Error(i(168));ge(Me,t),ge(Ge,n)}function du(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(i(108,me(e)||"Unknown",o));return B({},n,r)}function Ls(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Jt,pn=Me.current,ge(Me,e),ge(Ge,Ge.current),!0}function fu(e,t,n){var r=e.stateNode;if(!r)throw Error(i(169));n?(e=du(e,t,pn),r.__reactInternalMemoizedMergedChildContext=e,we(Ge),we(Me),ge(Me,e)):we(Ge),ge(Ge,n)}var Lt=null,Is=!1,ko=!1;function hu(e){Lt===null?Lt=[e]:Lt.push(e)}function oh(e){Is=!0,hu(e)}function Yt(){if(!ko&&Lt!==null){ko=!0;var e=0,t=he;try{var n=Lt;for(he=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Lt=null,Is=!1}catch(o){throw Lt!==null&&(Lt=Lt.slice(e+1)),ma(Bi,Yt),o}finally{he=t,ko=!1}}return null}var Bn=[],Hn=0,As=null,Ds=0,at=[],ut=0,mn=null,It=1,At="";function gn(e,t){Bn[Hn++]=Ds,Bn[Hn++]=As,As=e,Ds=t}function pu(e,t,n){at[ut++]=It,at[ut++]=At,at[ut++]=mn,mn=e;var r=It;e=At;var o=32-vt(r)-1;r&=~(1<<o),n+=1;var u=32-vt(t)+o;if(30<u){var h=o-o%5;u=(r&(1<<h)-1).toString(32),r>>=h,o-=h,It=1<<32-vt(t)+o|n<<o|r,At=u+e}else It=1<<u|n<<o|r,At=e}function So(e){e.return!==null&&(gn(e,1),pu(e,1,0))}function Eo(e){for(;e===As;)As=Bn[--Hn],Bn[Hn]=null,Ds=Bn[--Hn],Bn[Hn]=null;for(;e===mn;)mn=at[--ut],at[ut]=null,At=at[--ut],at[ut]=null,It=at[--ut],at[ut]=null}var rt=null,st=null,ke=!1,wt=null;function mu(e,t){var n=ht(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function gu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,rt=e,st=Qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,rt=e,st=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mn!==null?{id:It,overflow:At}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ht(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,rt=e,st=null,!0):!1;default:return!1}}function jo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Co(e){if(ke){var t=st;if(t){var n=t;if(!gu(e,t)){if(jo(e))throw Error(i(418));t=Qt(n.nextSibling);var r=rt;t&&gu(e,t)?mu(r,n):(e.flags=e.flags&-4097|2,ke=!1,rt=e)}}else{if(jo(e))throw Error(i(418));e.flags=e.flags&-4097|2,ke=!1,rt=e}}}function vu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;rt=e}function zs(e){if(e!==rt)return!1;if(!ke)return vu(e),ke=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!vo(e.type,e.memoizedProps)),t&&(t=st)){if(jo(e))throw yu(),Error(i(418));for(;t;)mu(e,t),t=Qt(t.nextSibling)}if(vu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){st=Qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}st=null}}else st=rt?Qt(e.stateNode.nextSibling):null;return!0}function yu(){for(var e=st;e;)e=Qt(e.nextSibling)}function qn(){st=rt=null,ke=!1}function Po(e){wt===null?wt=[e]:wt.push(e)}var lh=q.ReactCurrentBatchConfig;function Ur(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(i(309));var r=n.stateNode}if(!r)throw Error(i(147,e));var o=r,u=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===u?t.ref:(t=function(h){var v=o.refs;h===null?delete v[u]:v[u]=h},t._stringRef=u,t)}if(typeof e!="string")throw Error(i(284));if(!n._owner)throw Error(i(290,e))}return e}function Us(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function wu(e){var t=e._init;return t(e._payload)}function _u(e){function t(S,x){if(e){var j=S.deletions;j===null?(S.deletions=[x],S.flags|=16):j.push(x)}}function n(S,x){if(!e)return null;for(;x!==null;)t(S,x),x=x.sibling;return null}function r(S,x){for(S=new Map;x!==null;)x.key!==null?S.set(x.key,x):S.set(x.index,x),x=x.sibling;return S}function o(S,x){return S=on(S,x),S.index=0,S.sibling=null,S}function u(S,x,j){return S.index=j,e?(j=S.alternate,j!==null?(j=j.index,j<x?(S.flags|=2,x):j):(S.flags|=2,x)):(S.flags|=1048576,x)}function h(S){return e&&S.alternate===null&&(S.flags|=2),S}function v(S,x,j,L){return x===null||x.tag!==6?(x=yl(j,S.mode,L),x.return=S,x):(x=o(x,j),x.return=S,x)}function _(S,x,j,L){var G=j.type;return G===X?R(S,x,j.props.children,L,j.key):x!==null&&(x.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===We&&wu(G)===x.type)?(L=o(x,j.props),L.ref=Ur(S,x,j),L.return=S,L):(L=ui(j.type,j.key,j.props,null,S.mode,L),L.ref=Ur(S,x,j),L.return=S,L)}function C(S,x,j,L){return x===null||x.tag!==4||x.stateNode.containerInfo!==j.containerInfo||x.stateNode.implementation!==j.implementation?(x=wl(j,S.mode,L),x.return=S,x):(x=o(x,j.children||[]),x.return=S,x)}function R(S,x,j,L,G){return x===null||x.tag!==7?(x=En(j,S.mode,L,G),x.return=S,x):(x=o(x,j),x.return=S,x)}function b(S,x,j){if(typeof x=="string"&&x!==""||typeof x=="number")return x=yl(""+x,S.mode,j),x.return=S,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case $:return j=ui(x.type,x.key,x.props,null,S.mode,j),j.ref=Ur(S,null,x),j.return=S,j;case A:return x=wl(x,S.mode,j),x.return=S,x;case We:var L=x._init;return b(S,L(x._payload),j)}if(pr(x)||J(x))return x=En(x,S.mode,j,null),x.return=S,x;Us(S,x)}return null}function N(S,x,j,L){var G=x!==null?x.key:null;if(typeof j=="string"&&j!==""||typeof j=="number")return G!==null?null:v(S,x,""+j,L);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case $:return j.key===G?_(S,x,j,L):null;case A:return j.key===G?C(S,x,j,L):null;case We:return G=j._init,N(S,x,G(j._payload),L)}if(pr(j)||J(j))return G!==null?null:R(S,x,j,L,null);Us(S,j)}return null}function M(S,x,j,L,G){if(typeof L=="string"&&L!==""||typeof L=="number")return S=S.get(j)||null,v(x,S,""+L,G);if(typeof L=="object"&&L!==null){switch(L.$$typeof){case $:return S=S.get(L.key===null?j:L.key)||null,_(x,S,L,G);case A:return S=S.get(L.key===null?j:L.key)||null,C(x,S,L,G);case We:var Q=L._init;return M(S,x,j,Q(L._payload),G)}if(pr(L)||J(L))return S=S.get(j)||null,R(x,S,L,G,null);Us(x,L)}return null}function H(S,x,j,L){for(var G=null,Q=null,K=x,Y=x=0,Le=null;K!==null&&Y<j.length;Y++){K.index>Y?(Le=K,K=null):Le=K.sibling;var de=N(S,K,j[Y],L);if(de===null){K===null&&(K=Le);break}e&&K&&de.alternate===null&&t(S,K),x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de,K=Le}if(Y===j.length)return n(S,K),ke&&gn(S,Y),G;if(K===null){for(;Y<j.length;Y++)K=b(S,j[Y],L),K!==null&&(x=u(K,x,Y),Q===null?G=K:Q.sibling=K,Q=K);return ke&&gn(S,Y),G}for(K=r(S,K);Y<j.length;Y++)Le=M(K,S,Y,j[Y],L),Le!==null&&(e&&Le.alternate!==null&&K.delete(Le.key===null?Y:Le.key),x=u(Le,x,Y),Q===null?G=Le:Q.sibling=Le,Q=Le);return e&&K.forEach(function(ln){return t(S,ln)}),ke&&gn(S,Y),G}function W(S,x,j,L){var G=J(j);if(typeof G!="function")throw Error(i(150));if(j=G.call(j),j==null)throw Error(i(151));for(var Q=G=null,K=x,Y=x=0,Le=null,de=j.next();K!==null&&!de.done;Y++,de=j.next()){K.index>Y?(Le=K,K=null):Le=K.sibling;var ln=N(S,K,de.value,L);if(ln===null){K===null&&(K=Le);break}e&&K&&ln.alternate===null&&t(S,K),x=u(ln,x,Y),Q===null?G=ln:Q.sibling=ln,Q=ln,K=Le}if(de.done)return n(S,K),ke&&gn(S,Y),G;if(K===null){for(;!de.done;Y++,de=j.next())de=b(S,de.value,L),de!==null&&(x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de);return ke&&gn(S,Y),G}for(K=r(S,K);!de.done;Y++,de=j.next())de=M(K,S,Y,de.value,L),de!==null&&(e&&de.alternate!==null&&K.delete(de.key===null?Y:de.key),x=u(de,x,Y),Q===null?G=de:Q.sibling=de,Q=de);return e&&K.forEach(function(Mh){return t(S,Mh)}),ke&&gn(S,Y),G}function Ne(S,x,j,L){if(typeof j=="object"&&j!==null&&j.type===X&&j.key===null&&(j=j.props.children),typeof j=="object"&&j!==null){switch(j.$$typeof){case $:e:{for(var G=j.key,Q=x;Q!==null;){if(Q.key===G){if(G=j.type,G===X){if(Q.tag===7){n(S,Q.sibling),x=o(Q,j.props.children),x.return=S,S=x;break e}}else if(Q.elementType===G||typeof G=="object"&&G!==null&&G.$$typeof===We&&wu(G)===Q.type){n(S,Q.sibling),x=o(Q,j.props),x.ref=Ur(S,Q,j),x.return=S,S=x;break e}n(S,Q);break}else t(S,Q);Q=Q.sibling}j.type===X?(x=En(j.props.children,S.mode,L,j.key),x.return=S,S=x):(L=ui(j.type,j.key,j.props,null,S.mode,L),L.ref=Ur(S,x,j),L.return=S,S=L)}return h(S);case A:e:{for(Q=j.key;x!==null;){if(x.key===Q)if(x.tag===4&&x.stateNode.containerInfo===j.containerInfo&&x.stateNode.implementation===j.implementation){n(S,x.sibling),x=o(x,j.children||[]),x.return=S,S=x;break e}else{n(S,x);break}else t(S,x);x=x.sibling}x=wl(j,S.mode,L),x.return=S,S=x}return h(S);case We:return Q=j._init,Ne(S,x,Q(j._payload),L)}if(pr(j))return H(S,x,j,L);if(J(j))return W(S,x,j,L);Us(S,j)}return typeof j=="string"&&j!==""||typeof j=="number"?(j=""+j,x!==null&&x.tag===6?(n(S,x.sibling),x=o(x,j),x.return=S,S=x):(n(S,x),x=yl(j,S.mode,L),x.return=S,S=x),h(S)):n(S,x)}return Ne}var Wn=_u(!0),xu=_u(!1),Ms=Kt(null),Fs=null,Gn=null,No=null;function To(){No=Gn=Fs=null}function Ro(e){var t=Ms.current;we(Ms),e._currentValue=t}function bo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Qn(e,t){Fs=e,No=Gn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(Ke=!0),e.firstContext=null)}function ct(e){var t=e._currentValue;if(No!==e)if(e={context:e,memoizedValue:t,next:null},Gn===null){if(Fs===null)throw Error(i(308));Gn=e,Fs.dependencies={lanes:0,firstContext:e}}else Gn=Gn.next=e;return t}var vn=null;function Oo(e){vn===null?vn=[e]:vn.push(e)}function ku(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Oo(t)):(n.next=o.next,o.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Xt=!1;function $o(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Su(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Zt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,(le&2)!==0){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Dt(e,n)}return o=r.interleaved,o===null?(t.next=t,Oo(r)):(t.next=o.next,o.next=t),r.interleaved=t,Dt(e,n)}function Vs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wi(e,n)}}function Eu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var h={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};u===null?o=u=h:u=u.next=h,n=n.next}while(n!==null);u===null?o=u=t:u=u.next=t}else o=u=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Bs(e,t,n,r){var o=e.updateQueue;Xt=!1;var u=o.firstBaseUpdate,h=o.lastBaseUpdate,v=o.shared.pending;if(v!==null){o.shared.pending=null;var _=v,C=_.next;_.next=null,h===null?u=C:h.next=C,h=_;var R=e.alternate;R!==null&&(R=R.updateQueue,v=R.lastBaseUpdate,v!==h&&(v===null?R.firstBaseUpdate=C:v.next=C,R.lastBaseUpdate=_))}if(u!==null){var b=o.baseState;h=0,R=C=_=null,v=u;do{var N=v.lane,M=v.eventTime;if((r&N)===N){R!==null&&(R=R.next={eventTime:M,lane:0,tag:v.tag,payload:v.payload,callback:v.callback,next:null});e:{var H=e,W=v;switch(N=t,M=n,W.tag){case 1:if(H=W.payload,typeof H=="function"){b=H.call(M,b,N);break e}b=H;break e;case 3:H.flags=H.flags&-65537|128;case 0:if(H=W.payload,N=typeof H=="function"?H.call(M,b,N):H,N==null)break e;b=B({},b,N);break e;case 2:Xt=!0}}v.callback!==null&&v.lane!==0&&(e.flags|=64,N=o.effects,N===null?o.effects=[v]:N.push(v))}else M={eventTime:M,lane:N,tag:v.tag,payload:v.payload,callback:v.callback,next:null},R===null?(C=R=M,_=b):R=R.next=M,h|=N;if(v=v.next,v===null){if(v=o.shared.pending,v===null)break;N=v,v=N.next,N.next=null,o.lastBaseUpdate=N,o.shared.pending=null}}while(!0);if(R===null&&(_=b),o.baseState=_,o.firstBaseUpdate=C,o.lastBaseUpdate=R,t=o.shared.interleaved,t!==null){o=t;do h|=o.lane,o=o.next;while(o!==t)}else u===null&&(o.shared.lanes=0);_n|=h,e.lanes=h,e.memoizedState=b}}function ju(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(i(191,o));o.call(r)}}}var Mr={},Tt=Kt(Mr),Fr=Kt(Mr),Vr=Kt(Mr);function yn(e){if(e===Mr)throw Error(i(174));return e}function Lo(e,t){switch(ge(Vr,t),ge(Fr,e),ge(Tt,Mr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Li(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Li(t,e)}we(Tt),ge(Tt,t)}function Kn(){we(Tt),we(Fr),we(Vr)}function Cu(e){yn(Vr.current);var t=yn(Tt.current),n=Li(t,e.type);t!==n&&(ge(Fr,e),ge(Tt,n))}function Io(e){Fr.current===e&&(we(Tt),we(Fr))}var Se=Kt(0);function Hs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ao=[];function Do(){for(var e=0;e<Ao.length;e++)Ao[e]._workInProgressVersionPrimary=null;Ao.length=0}var qs=q.ReactCurrentDispatcher,zo=q.ReactCurrentBatchConfig,wn=0,Ee=null,Re=null,Oe=null,Ws=!1,Br=!1,Hr=0,ah=0;function Fe(){throw Error(i(321))}function Uo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!yt(e[n],t[n]))return!1;return!0}function Mo(e,t,n,r,o,u){if(wn=u,Ee=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,qs.current=e===null||e.memoizedState===null?fh:hh,e=n(r,o),Br){u=0;do{if(Br=!1,Hr=0,25<=u)throw Error(i(301));u+=1,Oe=Re=null,t.updateQueue=null,qs.current=ph,e=n(r,o)}while(Br)}if(qs.current=Ks,t=Re!==null&&Re.next!==null,wn=0,Oe=Re=Ee=null,Ws=!1,t)throw Error(i(300));return e}function Fo(){var e=Hr!==0;return Hr=0,e}function Rt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Oe===null?Ee.memoizedState=Oe=e:Oe=Oe.next=e,Oe}function dt(){if(Re===null){var e=Ee.alternate;e=e!==null?e.memoizedState:null}else e=Re.next;var t=Oe===null?Ee.memoizedState:Oe.next;if(t!==null)Oe=t,Re=e;else{if(e===null)throw Error(i(310));Re=e,e={memoizedState:Re.memoizedState,baseState:Re.baseState,baseQueue:Re.baseQueue,queue:Re.queue,next:null},Oe===null?Ee.memoizedState=Oe=e:Oe=Oe.next=e}return Oe}function qr(e,t){return typeof t=="function"?t(e):t}function Vo(e){var t=dt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=Re,o=r.baseQueue,u=n.pending;if(u!==null){if(o!==null){var h=o.next;o.next=u.next,u.next=h}r.baseQueue=o=u,n.pending=null}if(o!==null){u=o.next,r=r.baseState;var v=h=null,_=null,C=u;do{var R=C.lane;if((wn&R)===R)_!==null&&(_=_.next={lane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),r=C.hasEagerState?C.eagerState:e(r,C.action);else{var b={lane:R,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null};_===null?(v=_=b,h=r):_=_.next=b,Ee.lanes|=R,_n|=R}C=C.next}while(C!==null&&C!==u);_===null?h=r:_.next=v,yt(r,t.memoizedState)||(Ke=!0),t.memoizedState=r,t.baseState=h,t.baseQueue=_,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do u=o.lane,Ee.lanes|=u,_n|=u,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Bo(e){var t=dt(),n=t.queue;if(n===null)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,u=t.memoizedState;if(o!==null){n.pending=null;var h=o=o.next;do u=e(u,h.action),h=h.next;while(h!==o);yt(u,t.memoizedState)||(Ke=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),n.lastRenderedState=u}return[u,r]}function Pu(){}function Nu(e,t){var n=Ee,r=dt(),o=t(),u=!yt(r.memoizedState,o);if(u&&(r.memoizedState=o,Ke=!0),r=r.queue,Ho(bu.bind(null,n,r,e),[e]),r.getSnapshot!==t||u||Oe!==null&&Oe.memoizedState.tag&1){if(n.flags|=2048,Wr(9,Ru.bind(null,n,r,o,t),void 0,null),$e===null)throw Error(i(349));(wn&30)!==0||Tu(n,t,o)}return o}function Tu(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ru(e,t,n,r){t.value=n,t.getSnapshot=r,Ou(t)&&$u(e)}function bu(e,t,n){return n(function(){Ou(t)&&$u(e)})}function Ou(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!yt(e,n)}catch{return!0}}function $u(e){var t=Dt(e,1);t!==null&&St(t,e,1,-1)}function Lu(e){var t=Rt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qr,lastRenderedState:e},t.queue=e,e=e.dispatch=dh.bind(null,Ee,e),[t.memoizedState,e]}function Wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Ee.updateQueue,t===null?(t={lastEffect:null,stores:null},Ee.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Iu(){return dt().memoizedState}function Gs(e,t,n,r){var o=Rt();Ee.flags|=e,o.memoizedState=Wr(1|t,n,void 0,r===void 0?null:r)}function Qs(e,t,n,r){var o=dt();r=r===void 0?null:r;var u=void 0;if(Re!==null){var h=Re.memoizedState;if(u=h.destroy,r!==null&&Uo(r,h.deps)){o.memoizedState=Wr(t,n,u,r);return}}Ee.flags|=e,o.memoizedState=Wr(1|t,n,u,r)}function Au(e,t){return Gs(8390656,8,e,t)}function Ho(e,t){return Qs(2048,8,e,t)}function Du(e,t){return Qs(4,2,e,t)}function zu(e,t){return Qs(4,4,e,t)}function Uu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mu(e,t,n){return n=n!=null?n.concat([e]):null,Qs(4,4,Uu.bind(null,t,e),n)}function qo(){}function Fu(e,t){var n=dt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Uo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vu(e,t){var n=dt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Uo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Bu(e,t,n){return(wn&21)===0?(e.baseState&&(e.baseState=!1,Ke=!0),e.memoizedState=n):(yt(n,t)||(n=wa(),Ee.lanes|=n,_n|=n,e.baseState=!0),t)}function uh(e,t){var n=he;he=n!==0&&4>n?n:4,e(!0);var r=zo.transition;zo.transition={};try{e(!1),t()}finally{he=n,zo.transition=r}}function Hu(){return dt().memoizedState}function ch(e,t,n){var r=rn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qu(e))Wu(t,n);else if(n=ku(e,t,n,r),n!==null){var o=qe();St(n,e,r,o),Gu(n,t,r)}}function dh(e,t,n){var r=rn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qu(e))Wu(t,o);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var h=t.lastRenderedState,v=u(h,n);if(o.hasEagerState=!0,o.eagerState=v,yt(v,h)){var _=t.interleaved;_===null?(o.next=o,Oo(t)):(o.next=_.next,_.next=o),t.interleaved=o;return}}catch{}finally{}n=ku(e,t,o,r),n!==null&&(o=qe(),St(n,e,r,o),Gu(n,t,r))}}function qu(e){var t=e.alternate;return e===Ee||t!==null&&t===Ee}function Wu(e,t){Br=Ws=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gu(e,t,n){if((n&4194240)!==0){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wi(e,n)}}var Ks={readContext:ct,useCallback:Fe,useContext:Fe,useEffect:Fe,useImperativeHandle:Fe,useInsertionEffect:Fe,useLayoutEffect:Fe,useMemo:Fe,useReducer:Fe,useRef:Fe,useState:Fe,useDebugValue:Fe,useDeferredValue:Fe,useTransition:Fe,useMutableSource:Fe,useSyncExternalStore:Fe,useId:Fe,unstable_isNewReconciler:!1},fh={readContext:ct,useCallback:function(e,t){return Rt().memoizedState=[e,t===void 0?null:t],e},useContext:ct,useEffect:Au,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gs(4194308,4,Uu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gs(4,2,e,t)},useMemo:function(e,t){var n=Rt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Rt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=ch.bind(null,Ee,e),[r.memoizedState,e]},useRef:function(e){var t=Rt();return e={current:e},t.memoizedState=e},useState:Lu,useDebugValue:qo,useDeferredValue:function(e){return Rt().memoizedState=e},useTransition:function(){var e=Lu(!1),t=e[0];return e=uh.bind(null,e[1]),Rt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Ee,o=Rt();if(ke){if(n===void 0)throw Error(i(407));n=n()}else{if(n=t(),$e===null)throw Error(i(349));(wn&30)!==0||Tu(r,t,n)}o.memoizedState=n;var u={value:n,getSnapshot:t};return o.queue=u,Au(bu.bind(null,r,u,e),[e]),r.flags|=2048,Wr(9,Ru.bind(null,r,u,n,t),void 0,null),n},useId:function(){var e=Rt(),t=$e.identifierPrefix;if(ke){var n=At,r=It;n=(r&~(1<<32-vt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Hr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=ah++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},hh={readContext:ct,useCallback:Fu,useContext:ct,useEffect:Ho,useImperativeHandle:Mu,useInsertionEffect:Du,useLayoutEffect:zu,useMemo:Vu,useReducer:Vo,useRef:Iu,useState:function(){return Vo(qr)},useDebugValue:qo,useDeferredValue:function(e){var t=dt();return Bu(t,Re.memoizedState,e)},useTransition:function(){var e=Vo(qr)[0],t=dt().memoizedState;return[e,t]},useMutableSource:Pu,useSyncExternalStore:Nu,useId:Hu,unstable_isNewReconciler:!1},ph={readContext:ct,useCallback:Fu,useContext:ct,useEffect:Ho,useImperativeHandle:Mu,useInsertionEffect:Du,useLayoutEffect:zu,useMemo:Vu,useReducer:Bo,useRef:Iu,useState:function(){return Bo(qr)},useDebugValue:qo,useDeferredValue:function(e){var t=dt();return Re===null?t.memoizedState=e:Bu(t,Re.memoizedState,e)},useTransition:function(){var e=Bo(qr)[0],t=dt().memoizedState;return[e,t]},useMutableSource:Pu,useSyncExternalStore:Nu,useId:Hu,unstable_isNewReconciler:!1};function _t(e,t){if(e&&e.defaultProps){t=B({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:B({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Js={isMounted:function(e){return(e=e._reactInternals)?fn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=qe(),o=rn(e),u=zt(r,o);u.payload=t,n!=null&&(u.callback=n),t=Zt(e,u,o),t!==null&&(St(t,e,o,r),Vs(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=qe(),o=rn(e),u=zt(r,o);u.tag=1,u.payload=t,n!=null&&(u.callback=n),t=Zt(e,u,o),t!==null&&(St(t,e,o,r),Vs(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=qe(),r=rn(e),o=zt(n,r);o.tag=2,t!=null&&(o.callback=t),t=Zt(e,o,r),t!==null&&(St(t,e,r,n),Vs(t,e,r))}};function Qu(e,t,n,r,o,u,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,u,h):t.prototype&&t.prototype.isPureReactComponent?!Or(n,r)||!Or(o,u):!0}function Ku(e,t,n){var r=!1,o=Jt,u=t.contextType;return typeof u=="object"&&u!==null?u=ct(u):(o=Qe(t)?pn:Me.current,r=t.contextTypes,u=(r=r!=null)?Vn(e,o):Jt),t=new t(n,u),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Js,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=u),t}function Ju(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Js.enqueueReplaceState(t,t.state,null)}function Go(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},$o(e);var u=t.contextType;typeof u=="object"&&u!==null?o.context=ct(u):(u=Qe(t)?pn:Me.current,o.context=Vn(e,u)),o.state=e.memoizedState,u=t.getDerivedStateFromProps,typeof u=="function"&&(Wo(e,t,u,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&Js.enqueueReplaceState(o,o.state,null),Bs(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Jn(e,t){try{var n="",r=t;do n+=ue(r),r=r.return;while(r);var o=n}catch(u){o=`
Error generating stack: `+u.message+`
`+u.stack}return{value:e,source:t,stack:o,digest:null}}function Qo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ko(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var mh=typeof WeakMap=="function"?WeakMap:Map;function Yu(e,t,n){n=zt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ri||(ri=!0,cl=r),Ko(e,t)},n}function Xu(e,t,n){n=zt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){Ko(e,t)}}var u=e.stateNode;return u!==null&&typeof u.componentDidCatch=="function"&&(n.callback=function(){Ko(e,t),typeof r!="function"&&(tn===null?tn=new Set([this]):tn.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),n}function Zu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new mh;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Th.bind(null,e,t,n),t.then(e,e))}function ec(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function tc(e,t,n,r,o){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=zt(-1,1),t.tag=2,Zt(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var gh=q.ReactCurrentOwner,Ke=!1;function He(e,t,n,r){t.child=e===null?xu(t,null,n,r):Wn(t,e.child,n,r)}function nc(e,t,n,r,o){n=n.render;var u=t.ref;return Qn(t,o),r=Mo(e,t,n,r,u,o),n=Fo(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ut(e,t,o)):(ke&&n&&So(t),t.flags|=1,He(e,t,r,o),t.child)}function rc(e,t,n,r,o){if(e===null){var u=n.type;return typeof u=="function"&&!vl(u)&&u.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=u,sc(e,t,u,r,o)):(e=ui(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,(e.lanes&o)===0){var h=u.memoizedProps;if(n=n.compare,n=n!==null?n:Or,n(h,r)&&e.ref===t.ref)return Ut(e,t,o)}return t.flags|=1,e=on(u,r),e.ref=t.ref,e.return=t,t.child=e}function sc(e,t,n,r,o){if(e!==null){var u=e.memoizedProps;if(Or(u,r)&&e.ref===t.ref)if(Ke=!1,t.pendingProps=r=u,(e.lanes&o)!==0)(e.flags&131072)!==0&&(Ke=!0);else return t.lanes=e.lanes,Ut(e,t,o)}return Jo(e,t,n,r,o)}function ic(e,t,n){var r=t.pendingProps,o=r.children,u=e!==null?e.memoizedState:null;if(r.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ge(Xn,it),it|=n;else{if((n&1073741824)===0)return e=u!==null?u.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ge(Xn,it),it|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=u!==null?u.baseLanes:n,ge(Xn,it),it|=r}else u!==null?(r=u.baseLanes|n,t.memoizedState=null):r=n,ge(Xn,it),it|=r;return He(e,t,o,n),t.child}function oc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Jo(e,t,n,r,o){var u=Qe(n)?pn:Me.current;return u=Vn(t,u),Qn(t,o),n=Mo(e,t,n,r,u,o),r=Fo(),e!==null&&!Ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Ut(e,t,o)):(ke&&r&&So(t),t.flags|=1,He(e,t,n,o),t.child)}function lc(e,t,n,r,o){if(Qe(n)){var u=!0;Ls(t)}else u=!1;if(Qn(t,o),t.stateNode===null)Xs(e,t),Ku(t,n,r),Go(t,n,r,o),r=!0;else if(e===null){var h=t.stateNode,v=t.memoizedProps;h.props=v;var _=h.context,C=n.contextType;typeof C=="object"&&C!==null?C=ct(C):(C=Qe(n)?pn:Me.current,C=Vn(t,C));var R=n.getDerivedStateFromProps,b=typeof R=="function"||typeof h.getSnapshotBeforeUpdate=="function";b||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==r||_!==C)&&Ju(t,h,r,C),Xt=!1;var N=t.memoizedState;h.state=N,Bs(t,r,h,o),_=t.memoizedState,v!==r||N!==_||Ge.current||Xt?(typeof R=="function"&&(Wo(t,n,R,r),_=t.memoizedState),(v=Xt||Qu(t,n,v,r,N,_,C))?(b||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=_),h.props=r,h.state=_,h.context=C,r=v):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{h=t.stateNode,Su(e,t),v=t.memoizedProps,C=t.type===t.elementType?v:_t(t.type,v),h.props=C,b=t.pendingProps,N=h.context,_=n.contextType,typeof _=="object"&&_!==null?_=ct(_):(_=Qe(n)?pn:Me.current,_=Vn(t,_));var M=n.getDerivedStateFromProps;(R=typeof M=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(v!==b||N!==_)&&Ju(t,h,r,_),Xt=!1,N=t.memoizedState,h.state=N,Bs(t,r,h,o);var H=t.memoizedState;v!==b||N!==H||Ge.current||Xt?(typeof M=="function"&&(Wo(t,n,M,r),H=t.memoizedState),(C=Xt||Qu(t,n,C,r,N,H,_)||!1)?(R||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(r,H,_),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(r,H,_)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=H),h.props=r,h.state=H,h.context=_,r=C):(typeof h.componentDidUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||v===e.memoizedProps&&N===e.memoizedState||(t.flags|=1024),r=!1)}return Yo(e,t,n,r,u,o)}function Yo(e,t,n,r,o,u){oc(e,t);var h=(t.flags&128)!==0;if(!r&&!h)return o&&fu(t,n,!1),Ut(e,t,u);r=t.stateNode,gh.current=t;var v=h&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&h?(t.child=Wn(t,e.child,null,u),t.child=Wn(t,null,v,u)):He(e,t,v,u),t.memoizedState=r.state,o&&fu(t,n,!0),t.child}function ac(e){var t=e.stateNode;t.pendingContext?cu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&cu(e,t.context,!1),Lo(e,t.containerInfo)}function uc(e,t,n,r,o){return qn(),Po(o),t.flags|=256,He(e,t,n,r),t.child}var Xo={dehydrated:null,treeContext:null,retryLane:0};function Zo(e){return{baseLanes:e,cachePool:null,transitions:null}}function cc(e,t,n){var r=t.pendingProps,o=Se.current,u=!1,h=(t.flags&128)!==0,v;if((v=h)||(v=e!==null&&e.memoizedState===null?!1:(o&2)!==0),v?(u=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ge(Se,o&1),e===null)return Co(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=r.children,e=r.fallback,u?(r=t.mode,u=t.child,h={mode:"hidden",children:h},(r&1)===0&&u!==null?(u.childLanes=0,u.pendingProps=h):u=ci(h,r,0,null),e=En(e,r,n,null),u.return=t,e.return=t,u.sibling=e,t.child=u,t.child.memoizedState=Zo(n),t.memoizedState=Xo,e):el(t,h));if(o=e.memoizedState,o!==null&&(v=o.dehydrated,v!==null))return vh(e,t,h,r,v,o,n);if(u){u=r.fallback,h=t.mode,o=e.child,v=o.sibling;var _={mode:"hidden",children:r.children};return(h&1)===0&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=_,t.deletions=null):(r=on(o,_),r.subtreeFlags=o.subtreeFlags&14680064),v!==null?u=on(v,u):(u=En(u,h,n,null),u.flags|=2),u.return=t,r.return=t,r.sibling=u,t.child=r,r=u,u=t.child,h=e.child.memoizedState,h=h===null?Zo(n):{baseLanes:h.baseLanes|n,cachePool:null,transitions:h.transitions},u.memoizedState=h,u.childLanes=e.childLanes&~n,t.memoizedState=Xo,r}return u=e.child,e=u.sibling,r=on(u,{mode:"visible",children:r.children}),(t.mode&1)===0&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function el(e,t){return t=ci({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ys(e,t,n,r){return r!==null&&Po(r),Wn(t,e.child,null,n),e=el(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function vh(e,t,n,r,o,u,h){if(n)return t.flags&256?(t.flags&=-257,r=Qo(Error(i(422))),Ys(e,t,h,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(u=r.fallback,o=t.mode,r=ci({mode:"visible",children:r.children},o,0,null),u=En(u,o,h,null),u.flags|=2,r.return=t,u.return=t,r.sibling=u,t.child=r,(t.mode&1)!==0&&Wn(t,e.child,null,h),t.child.memoizedState=Zo(h),t.memoizedState=Xo,u);if((t.mode&1)===0)return Ys(e,t,h,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var v=r.dgst;return r=v,u=Error(i(419)),r=Qo(u,r,void 0),Ys(e,t,h,r)}if(v=(h&e.childLanes)!==0,Ke||v){if(r=$e,r!==null){switch(h&-h){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=(o&(r.suspendedLanes|h))!==0?0:o,o!==0&&o!==u.retryLane&&(u.retryLane=o,Dt(e,o),St(r,e,o,-1))}return gl(),r=Qo(Error(i(421))),Ys(e,t,h,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Rh.bind(null,e),o._reactRetry=t,null):(e=u.treeContext,st=Qt(o.nextSibling),rt=t,ke=!0,wt=null,e!==null&&(at[ut++]=It,at[ut++]=At,at[ut++]=mn,It=e.id,At=e.overflow,mn=t),t=el(t,r.children),t.flags|=4096,t)}function dc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),bo(e.return,t,n)}function tl(e,t,n,r,o){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=r,u.tail=n,u.tailMode=o)}function fc(e,t,n){var r=t.pendingProps,o=r.revealOrder,u=r.tail;if(He(e,t,r.children,n),r=Se.current,(r&2)!==0)r=r&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&dc(e,n,t);else if(e.tag===19)dc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ge(Se,r),(t.mode&1)===0)t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Hs(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),tl(t,!1,o,n,u);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Hs(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}tl(t,!0,n,null,u);break;case"together":tl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xs(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ut(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_n|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(i(153));if(t.child!==null){for(e=t.child,n=on(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=on(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function yh(e,t,n){switch(t.tag){case 3:ac(t),qn();break;case 5:Cu(t);break;case 1:Qe(t.type)&&Ls(t);break;case 4:Lo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ge(Ms,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ge(Se,Se.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?cc(e,t,n):(ge(Se,Se.current&1),e=Ut(e,t,n),e!==null?e.sibling:null);ge(Se,Se.current&1);break;case 19:if(r=(n&t.childLanes)!==0,(e.flags&128)!==0){if(r)return fc(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ge(Se,Se.current),r)break;return null;case 22:case 23:return t.lanes=0,ic(e,t,n)}return Ut(e,t,n)}var hc,nl,pc,mc;hc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},nl=function(){},pc=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,yn(Tt.current);var u=null;switch(n){case"input":o=Ri(e,o),r=Ri(e,r),u=[];break;case"select":o=B({},o,{value:void 0}),r=B({},r,{value:void 0}),u=[];break;case"textarea":o=$i(e,o),r=$i(e,r),u=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=bs)}Ii(n,r);var h;n=null;for(C in o)if(!r.hasOwnProperty(C)&&o.hasOwnProperty(C)&&o[C]!=null)if(C==="style"){var v=o[C];for(h in v)v.hasOwnProperty(h)&&(n||(n={}),n[h]="")}else C!=="dangerouslySetInnerHTML"&&C!=="children"&&C!=="suppressContentEditableWarning"&&C!=="suppressHydrationWarning"&&C!=="autoFocus"&&(a.hasOwnProperty(C)?u||(u=[]):(u=u||[]).push(C,null));for(C in r){var _=r[C];if(v=o!=null?o[C]:void 0,r.hasOwnProperty(C)&&_!==v&&(_!=null||v!=null))if(C==="style")if(v){for(h in v)!v.hasOwnProperty(h)||_&&_.hasOwnProperty(h)||(n||(n={}),n[h]="");for(h in _)_.hasOwnProperty(h)&&v[h]!==_[h]&&(n||(n={}),n[h]=_[h])}else n||(u||(u=[]),u.push(C,n)),n=_;else C==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,v=v?v.__html:void 0,_!=null&&v!==_&&(u=u||[]).push(C,_)):C==="children"?typeof _!="string"&&typeof _!="number"||(u=u||[]).push(C,""+_):C!=="suppressContentEditableWarning"&&C!=="suppressHydrationWarning"&&(a.hasOwnProperty(C)?(_!=null&&C==="onScroll"&&ye("scroll",e),u||v===_||(u=[])):(u=u||[]).push(C,_))}n&&(u=u||[]).push("style",n);var C=u;(t.updateQueue=C)&&(t.flags|=4)}},mc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Gr(e,t){if(!ke)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function wh(e,t,n){var r=t.pendingProps;switch(Eo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ve(t),null;case 1:return Qe(t.type)&&$s(),Ve(t),null;case 3:return r=t.stateNode,Kn(),we(Ge),we(Me),Do(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(zs(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,wt!==null&&(hl(wt),wt=null))),nl(e,t),Ve(t),null;case 5:Io(t);var o=yn(Vr.current);if(n=t.type,e!==null&&t.stateNode!=null)pc(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(i(166));return Ve(t),null}if(e=yn(Tt.current),zs(t)){r=t.stateNode,n=t.type;var u=t.memoizedProps;switch(r[Nt]=t,r[Dr]=u,e=(t.mode&1)!==0,n){case"dialog":ye("cancel",r),ye("close",r);break;case"iframe":case"object":case"embed":ye("load",r);break;case"video":case"audio":for(o=0;o<Lr.length;o++)ye(Lr[o],r);break;case"source":ye("error",r);break;case"img":case"image":case"link":ye("error",r),ye("load",r);break;case"details":ye("toggle",r);break;case"input":Kl(r,u),ye("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!u.multiple},ye("invalid",r);break;case"textarea":Xl(r,u),ye("invalid",r)}Ii(n,u),o=null;for(var h in u)if(u.hasOwnProperty(h)){var v=u[h];h==="children"?typeof v=="string"?r.textContent!==v&&(u.suppressHydrationWarning!==!0&&Rs(r.textContent,v,e),o=["children",v]):typeof v=="number"&&r.textContent!==""+v&&(u.suppressHydrationWarning!==!0&&Rs(r.textContent,v,e),o=["children",""+v]):a.hasOwnProperty(h)&&v!=null&&h==="onScroll"&&ye("scroll",r)}switch(n){case"input":ls(r),Yl(r,u,!0);break;case"textarea":ls(r),ea(r);break;case"select":case"option":break;default:typeof u.onClick=="function"&&(r.onclick=bs)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{h=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ta(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=h.createElement(n,{is:r.is}):(e=h.createElement(n),n==="select"&&(h=e,r.multiple?h.multiple=!0:r.size&&(h.size=r.size))):e=h.createElementNS(e,n),e[Nt]=t,e[Dr]=r,hc(e,t,!1,!1),t.stateNode=e;e:{switch(h=Ai(n,r),n){case"dialog":ye("cancel",e),ye("close",e),o=r;break;case"iframe":case"object":case"embed":ye("load",e),o=r;break;case"video":case"audio":for(o=0;o<Lr.length;o++)ye(Lr[o],e);o=r;break;case"source":ye("error",e),o=r;break;case"img":case"image":case"link":ye("error",e),ye("load",e),o=r;break;case"details":ye("toggle",e),o=r;break;case"input":Kl(e,r),o=Ri(e,r),ye("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=B({},r,{value:void 0}),ye("invalid",e);break;case"textarea":Xl(e,r),o=$i(e,r),ye("invalid",e);break;default:o=r}Ii(n,o),v=o;for(u in v)if(v.hasOwnProperty(u)){var _=v[u];u==="style"?sa(e,_):u==="dangerouslySetInnerHTML"?(_=_?_.__html:void 0,_!=null&&na(e,_)):u==="children"?typeof _=="string"?(n!=="textarea"||_!=="")&&mr(e,_):typeof _=="number"&&mr(e,""+_):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(a.hasOwnProperty(u)?_!=null&&u==="onScroll"&&ye("scroll",e):_!=null&&U(e,u,_,h))}switch(n){case"input":ls(e),Yl(e,r,!1);break;case"textarea":ls(e),ea(e);break;case"option":r.value!=null&&e.setAttribute("value",""+fe(r.value));break;case"select":e.multiple=!!r.multiple,u=r.value,u!=null?Rn(e,!!r.multiple,u,!1):r.defaultValue!=null&&Rn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=bs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ve(t),null;case 6:if(e&&t.stateNode!=null)mc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(i(166));if(n=yn(Vr.current),yn(Tt.current),zs(t)){if(r=t.stateNode,n=t.memoizedProps,r[Nt]=t,(u=r.nodeValue!==n)&&(e=rt,e!==null))switch(e.tag){case 3:Rs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Rs(r.nodeValue,n,(e.mode&1)!==0)}u&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=t,t.stateNode=r}return Ve(t),null;case 13:if(we(Se),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ke&&st!==null&&(t.mode&1)!==0&&(t.flags&128)===0)yu(),qn(),t.flags|=98560,u=!1;else if(u=zs(t),r!==null&&r.dehydrated!==null){if(e===null){if(!u)throw Error(i(318));if(u=t.memoizedState,u=u!==null?u.dehydrated:null,!u)throw Error(i(317));u[Nt]=t}else qn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ve(t),u=!1}else wt!==null&&(hl(wt),wt=null),u=!0;if(!u)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Se.current&1)!==0?be===0&&(be=3):gl())),t.updateQueue!==null&&(t.flags|=4),Ve(t),null);case 4:return Kn(),nl(e,t),e===null&&Ir(t.stateNode.containerInfo),Ve(t),null;case 10:return Ro(t.type._context),Ve(t),null;case 17:return Qe(t.type)&&$s(),Ve(t),null;case 19:if(we(Se),u=t.memoizedState,u===null)return Ve(t),null;if(r=(t.flags&128)!==0,h=u.rendering,h===null)if(r)Gr(u,!1);else{if(be!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=Hs(e),h!==null){for(t.flags|=128,Gr(u,!1),r=h.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)u=n,e=r,u.flags&=14680066,h=u.alternate,h===null?(u.childLanes=0,u.lanes=e,u.child=null,u.subtreeFlags=0,u.memoizedProps=null,u.memoizedState=null,u.updateQueue=null,u.dependencies=null,u.stateNode=null):(u.childLanes=h.childLanes,u.lanes=h.lanes,u.child=h.child,u.subtreeFlags=0,u.deletions=null,u.memoizedProps=h.memoizedProps,u.memoizedState=h.memoizedState,u.updateQueue=h.updateQueue,u.type=h.type,e=h.dependencies,u.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ge(Se,Se.current&1|2),t.child}e=e.sibling}u.tail!==null&&Pe()>Zn&&(t.flags|=128,r=!0,Gr(u,!1),t.lanes=4194304)}else{if(!r)if(e=Hs(h),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Gr(u,!0),u.tail===null&&u.tailMode==="hidden"&&!h.alternate&&!ke)return Ve(t),null}else 2*Pe()-u.renderingStartTime>Zn&&n!==1073741824&&(t.flags|=128,r=!0,Gr(u,!1),t.lanes=4194304);u.isBackwards?(h.sibling=t.child,t.child=h):(n=u.last,n!==null?n.sibling=h:t.child=h,u.last=h)}return u.tail!==null?(t=u.tail,u.rendering=t,u.tail=t.sibling,u.renderingStartTime=Pe(),t.sibling=null,n=Se.current,ge(Se,r?n&1|2:n&1),t):(Ve(t),null);case 22:case 23:return ml(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&(t.mode&1)!==0?(it&1073741824)!==0&&(Ve(t),t.subtreeFlags&6&&(t.flags|=8192)):Ve(t),null;case 24:return null;case 25:return null}throw Error(i(156,t.tag))}function _h(e,t){switch(Eo(t),t.tag){case 1:return Qe(t.type)&&$s(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kn(),we(Ge),we(Me),Do(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Io(t),null;case 13:if(we(Se),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(i(340));qn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return we(Se),null;case 4:return Kn(),null;case 10:return Ro(t.type._context),null;case 22:case 23:return ml(),null;case 24:return null;default:return null}}var Zs=!1,Be=!1,xh=typeof WeakSet=="function"?WeakSet:Set,V=null;function Yn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ce(e,t,r)}else n.current=null}function rl(e,t,n){try{n()}catch(r){Ce(e,t,r)}}var gc=!1;function kh(e,t){if(mo=ws,e=Qa(),oo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,u=r.focusNode;r=r.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break e}var h=0,v=-1,_=-1,C=0,R=0,b=e,N=null;t:for(;;){for(var M;b!==n||o!==0&&b.nodeType!==3||(v=h+o),b!==u||r!==0&&b.nodeType!==3||(_=h+r),b.nodeType===3&&(h+=b.nodeValue.length),(M=b.firstChild)!==null;)N=b,b=M;for(;;){if(b===e)break t;if(N===n&&++C===o&&(v=h),N===u&&++R===r&&(_=h),(M=b.nextSibling)!==null)break;b=N,N=b.parentNode}b=M}n=v===-1||_===-1?null:{start:v,end:_}}else n=null}n=n||{start:0,end:0}}else n=null;for(go={focusedElem:e,selectionRange:n},ws=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var H=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(H!==null){var W=H.memoizedProps,Ne=H.memoizedState,S=t.stateNode,x=S.getSnapshotBeforeUpdate(t.elementType===t.type?W:_t(t.type,W),Ne);S.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var j=t.stateNode.containerInfo;j.nodeType===1?j.textContent="":j.nodeType===9&&j.documentElement&&j.removeChild(j.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(i(163))}}catch(L){Ce(t,t.return,L)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return H=gc,gc=!1,H}function Qr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var u=o.destroy;o.destroy=void 0,u!==void 0&&rl(t,n,u)}o=o.next}while(o!==r)}}function ei(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function sl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function vc(e){var t=e.alternate;t!==null&&(e.alternate=null,vc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[Dr],delete t[_o],delete t[sh],delete t[ih])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function yc(e){return e.tag===5||e.tag===3||e.tag===4}function wc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||yc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function il(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=bs));else if(r!==4&&(e=e.child,e!==null))for(il(e,t,n),e=e.sibling;e!==null;)il(e,t,n),e=e.sibling}function ol(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ol(e,t,n),e=e.sibling;e!==null;)ol(e,t,n),e=e.sibling}var De=null,xt=!1;function en(e,t,n){for(n=n.child;n!==null;)_c(e,t,n),n=n.sibling}function _c(e,t,n){if(Pt&&typeof Pt.onCommitFiberUnmount=="function")try{Pt.onCommitFiberUnmount(hs,n)}catch{}switch(n.tag){case 5:Be||Yn(n,t);case 6:var r=De,o=xt;De=null,en(e,t,n),De=r,xt=o,De!==null&&(xt?(e=De,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):De.removeChild(n.stateNode));break;case 18:De!==null&&(xt?(e=De,n=n.stateNode,e.nodeType===8?wo(e.parentNode,n):e.nodeType===1&&wo(e,n),Cr(e)):wo(De,n.stateNode));break;case 4:r=De,o=xt,De=n.stateNode.containerInfo,xt=!0,en(e,t,n),De=r,xt=o;break;case 0:case 11:case 14:case 15:if(!Be&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var u=o,h=u.destroy;u=u.tag,h!==void 0&&((u&2)!==0||(u&4)!==0)&&rl(n,t,h),o=o.next}while(o!==r)}en(e,t,n);break;case 1:if(!Be&&(Yn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(v){Ce(n,t,v)}en(e,t,n);break;case 21:en(e,t,n);break;case 22:n.mode&1?(Be=(r=Be)||n.memoizedState!==null,en(e,t,n),Be=r):en(e,t,n);break;default:en(e,t,n)}}function xc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new xh),t.forEach(function(r){var o=bh.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function kt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var u=e,h=t,v=h;e:for(;v!==null;){switch(v.tag){case 5:De=v.stateNode,xt=!1;break e;case 3:De=v.stateNode.containerInfo,xt=!0;break e;case 4:De=v.stateNode.containerInfo,xt=!0;break e}v=v.return}if(De===null)throw Error(i(160));_c(u,h,o),De=null,xt=!1;var _=o.alternate;_!==null&&(_.return=null),o.return=null}catch(C){Ce(o,t,C)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)kc(t,e),t=t.sibling}function kc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(kt(t,e),bt(e),r&4){try{Qr(3,e,e.return),ei(3,e)}catch(W){Ce(e,e.return,W)}try{Qr(5,e,e.return)}catch(W){Ce(e,e.return,W)}}break;case 1:kt(t,e),bt(e),r&512&&n!==null&&Yn(n,n.return);break;case 5:if(kt(t,e),bt(e),r&512&&n!==null&&Yn(n,n.return),e.flags&32){var o=e.stateNode;try{mr(o,"")}catch(W){Ce(e,e.return,W)}}if(r&4&&(o=e.stateNode,o!=null)){var u=e.memoizedProps,h=n!==null?n.memoizedProps:u,v=e.type,_=e.updateQueue;if(e.updateQueue=null,_!==null)try{v==="input"&&u.type==="radio"&&u.name!=null&&Jl(o,u),Ai(v,h);var C=Ai(v,u);for(h=0;h<_.length;h+=2){var R=_[h],b=_[h+1];R==="style"?sa(o,b):R==="dangerouslySetInnerHTML"?na(o,b):R==="children"?mr(o,b):U(o,R,b,C)}switch(v){case"input":bi(o,u);break;case"textarea":Zl(o,u);break;case"select":var N=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!u.multiple;var M=u.value;M!=null?Rn(o,!!u.multiple,M,!1):N!==!!u.multiple&&(u.defaultValue!=null?Rn(o,!!u.multiple,u.defaultValue,!0):Rn(o,!!u.multiple,u.multiple?[]:"",!1))}o[Dr]=u}catch(W){Ce(e,e.return,W)}}break;case 6:if(kt(t,e),bt(e),r&4){if(e.stateNode===null)throw Error(i(162));o=e.stateNode,u=e.memoizedProps;try{o.nodeValue=u}catch(W){Ce(e,e.return,W)}}break;case 3:if(kt(t,e),bt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Cr(t.containerInfo)}catch(W){Ce(e,e.return,W)}break;case 4:kt(t,e),bt(e);break;case 13:kt(t,e),bt(e),o=e.child,o.flags&8192&&(u=o.memoizedState!==null,o.stateNode.isHidden=u,!u||o.alternate!==null&&o.alternate.memoizedState!==null||(ul=Pe())),r&4&&xc(e);break;case 22:if(R=n!==null&&n.memoizedState!==null,e.mode&1?(Be=(C=Be)||R,kt(t,e),Be=C):kt(t,e),bt(e),r&8192){if(C=e.memoizedState!==null,(e.stateNode.isHidden=C)&&!R&&(e.mode&1)!==0)for(V=e,R=e.child;R!==null;){for(b=V=R;V!==null;){switch(N=V,M=N.child,N.tag){case 0:case 11:case 14:case 15:Qr(4,N,N.return);break;case 1:Yn(N,N.return);var H=N.stateNode;if(typeof H.componentWillUnmount=="function"){r=N,n=N.return;try{t=r,H.props=t.memoizedProps,H.state=t.memoizedState,H.componentWillUnmount()}catch(W){Ce(r,n,W)}}break;case 5:Yn(N,N.return);break;case 22:if(N.memoizedState!==null){jc(b);continue}}M!==null?(M.return=N,V=M):jc(b)}R=R.sibling}e:for(R=null,b=e;;){if(b.tag===5){if(R===null){R=b;try{o=b.stateNode,C?(u=o.style,typeof u.setProperty=="function"?u.setProperty("display","none","important"):u.display="none"):(v=b.stateNode,_=b.memoizedProps.style,h=_!=null&&_.hasOwnProperty("display")?_.display:null,v.style.display=ra("display",h))}catch(W){Ce(e,e.return,W)}}}else if(b.tag===6){if(R===null)try{b.stateNode.nodeValue=C?"":b.memoizedProps}catch(W){Ce(e,e.return,W)}}else if((b.tag!==22&&b.tag!==23||b.memoizedState===null||b===e)&&b.child!==null){b.child.return=b,b=b.child;continue}if(b===e)break e;for(;b.sibling===null;){if(b.return===null||b.return===e)break e;R===b&&(R=null),b=b.return}R===b&&(R=null),b.sibling.return=b.return,b=b.sibling}}break;case 19:kt(t,e),bt(e),r&4&&xc(e);break;case 21:break;default:kt(t,e),bt(e)}}function bt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(yc(n)){var r=n;break e}n=n.return}throw Error(i(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(mr(o,""),r.flags&=-33);var u=wc(e);ol(e,u,o);break;case 3:case 4:var h=r.stateNode.containerInfo,v=wc(e);il(e,v,h);break;default:throw Error(i(161))}}catch(_){Ce(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Sh(e,t,n){V=e,Sc(e)}function Sc(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var o=V,u=o.child;if(o.tag===22&&r){var h=o.memoizedState!==null||Zs;if(!h){var v=o.alternate,_=v!==null&&v.memoizedState!==null||Be;v=Zs;var C=Be;if(Zs=h,(Be=_)&&!C)for(V=o;V!==null;)h=V,_=h.child,h.tag===22&&h.memoizedState!==null?Cc(o):_!==null?(_.return=h,V=_):Cc(o);for(;u!==null;)V=u,Sc(u),u=u.sibling;V=o,Zs=v,Be=C}Ec(e)}else(o.subtreeFlags&8772)!==0&&u!==null?(u.return=o,V=u):Ec(e)}}function Ec(e){for(;V!==null;){var t=V;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:Be||ei(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Be)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:_t(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var u=t.updateQueue;u!==null&&ju(t,u,r);break;case 3:var h=t.updateQueue;if(h!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ju(t,h,n)}break;case 5:var v=t.stateNode;if(n===null&&t.flags&4){n=v;var _=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":_.autoFocus&&n.focus();break;case"img":_.src&&(n.src=_.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var C=t.alternate;if(C!==null){var R=C.memoizedState;if(R!==null){var b=R.dehydrated;b!==null&&Cr(b)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(i(163))}Be||t.flags&512&&sl(t)}catch(N){Ce(t,t.return,N)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function jc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function Cc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ei(4,t)}catch(_){Ce(t,n,_)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(_){Ce(t,o,_)}}var u=t.return;try{sl(t)}catch(_){Ce(t,u,_)}break;case 5:var h=t.return;try{sl(t)}catch(_){Ce(t,h,_)}}}catch(_){Ce(t,t.return,_)}if(t===e){V=null;break}var v=t.sibling;if(v!==null){v.return=t.return,V=v;break}V=t.return}}var Eh=Math.ceil,ti=q.ReactCurrentDispatcher,ll=q.ReactCurrentOwner,ft=q.ReactCurrentBatchConfig,le=0,$e=null,Te=null,ze=0,it=0,Xn=Kt(0),be=0,Kr=null,_n=0,ni=0,al=0,Jr=null,Je=null,ul=0,Zn=1/0,Mt=null,ri=!1,cl=null,tn=null,si=!1,nn=null,ii=0,Yr=0,dl=null,oi=-1,li=0;function qe(){return(le&6)!==0?Pe():oi!==-1?oi:oi=Pe()}function rn(e){return(e.mode&1)===0?1:(le&2)!==0&&ze!==0?ze&-ze:lh.transition!==null?(li===0&&(li=wa()),li):(e=he,e!==0||(e=window.event,e=e===void 0?16:Na(e.type)),e)}function St(e,t,n,r){if(50<Yr)throw Yr=0,dl=null,Error(i(185));xr(e,n,r),((le&2)===0||e!==$e)&&(e===$e&&((le&2)===0&&(ni|=n),be===4&&sn(e,ze)),Ye(e,r),n===1&&le===0&&(t.mode&1)===0&&(Zn=Pe()+500,Is&&Yt()))}function Ye(e,t){var n=e.callbackNode;lf(e,t);var r=gs(e,e===$e?ze:0);if(r===0)n!==null&&ga(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ga(n),t===1)e.tag===0?oh(Nc.bind(null,e)):hu(Nc.bind(null,e)),nh(function(){(le&6)===0&&Yt()}),n=null;else{switch(_a(r)){case 1:n=Bi;break;case 4:n=va;break;case 16:n=fs;break;case 536870912:n=ya;break;default:n=fs}n=Ac(n,Pc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Pc(e,t){if(oi=-1,li=0,(le&6)!==0)throw Error(i(327));var n=e.callbackNode;if(er()&&e.callbackNode!==n)return null;var r=gs(e,e===$e?ze:0);if(r===0)return null;if((r&30)!==0||(r&e.expiredLanes)!==0||t)t=ai(e,r);else{t=r;var o=le;le|=2;var u=Rc();($e!==e||ze!==t)&&(Mt=null,Zn=Pe()+500,kn(e,t));do try{Ph();break}catch(v){Tc(e,v)}while(!0);To(),ti.current=u,le=o,Te!==null?t=0:($e=null,ze=0,t=be)}if(t!==0){if(t===2&&(o=Hi(e),o!==0&&(r=o,t=fl(e,o))),t===1)throw n=Kr,kn(e,0),sn(e,r),Ye(e,Pe()),n;if(t===6)sn(e,r);else{if(o=e.current.alternate,(r&30)===0&&!jh(o)&&(t=ai(e,r),t===2&&(u=Hi(e),u!==0&&(r=u,t=fl(e,u))),t===1))throw n=Kr,kn(e,0),sn(e,r),Ye(e,Pe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(i(345));case 2:Sn(e,Je,Mt);break;case 3:if(sn(e,r),(r&130023424)===r&&(t=ul+500-Pe(),10<t)){if(gs(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){qe(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=yo(Sn.bind(null,e,Je,Mt),t);break}Sn(e,Je,Mt);break;case 4:if(sn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var h=31-vt(r);u=1<<h,h=t[h],h>o&&(o=h),r&=~u}if(r=o,r=Pe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Eh(r/1960))-r,10<r){e.timeoutHandle=yo(Sn.bind(null,e,Je,Mt),r);break}Sn(e,Je,Mt);break;case 5:Sn(e,Je,Mt);break;default:throw Error(i(329))}}}return Ye(e,Pe()),e.callbackNode===n?Pc.bind(null,e):null}function fl(e,t){var n=Jr;return e.current.memoizedState.isDehydrated&&(kn(e,t).flags|=256),e=ai(e,t),e!==2&&(t=Je,Je=n,t!==null&&hl(t)),e}function hl(e){Je===null?Je=e:Je.push.apply(Je,e)}function jh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],u=o.getSnapshot;o=o.value;try{if(!yt(u(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function sn(e,t){for(t&=~al,t&=~ni,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-vt(t),r=1<<n;e[n]=-1,t&=~r}}function Nc(e){if((le&6)!==0)throw Error(i(327));er();var t=gs(e,0);if((t&1)===0)return Ye(e,Pe()),null;var n=ai(e,t);if(e.tag!==0&&n===2){var r=Hi(e);r!==0&&(t=r,n=fl(e,r))}if(n===1)throw n=Kr,kn(e,0),sn(e,t),Ye(e,Pe()),n;if(n===6)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Sn(e,Je,Mt),Ye(e,Pe()),null}function pl(e,t){var n=le;le|=1;try{return e(t)}finally{le=n,le===0&&(Zn=Pe()+500,Is&&Yt())}}function xn(e){nn!==null&&nn.tag===0&&(le&6)===0&&er();var t=le;le|=1;var n=ft.transition,r=he;try{if(ft.transition=null,he=1,e)return e()}finally{he=r,ft.transition=n,le=t,(le&6)===0&&Yt()}}function ml(){it=Xn.current,we(Xn)}function kn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,th(n)),Te!==null)for(n=Te.return;n!==null;){var r=n;switch(Eo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&$s();break;case 3:Kn(),we(Ge),we(Me),Do();break;case 5:Io(r);break;case 4:Kn();break;case 13:we(Se);break;case 19:we(Se);break;case 10:Ro(r.type._context);break;case 22:case 23:ml()}n=n.return}if($e=e,Te=e=on(e.current,null),ze=it=t,be=0,Kr=null,al=ni=_n=0,Je=Jr=null,vn!==null){for(t=0;t<vn.length;t++)if(n=vn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,u=n.pending;if(u!==null){var h=u.next;u.next=o,r.next=h}n.pending=r}vn=null}return e}function Tc(e,t){do{var n=Te;try{if(To(),qs.current=Ks,Ws){for(var r=Ee.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ws=!1}if(wn=0,Oe=Re=Ee=null,Br=!1,Hr=0,ll.current=null,n===null||n.return===null){be=1,Kr=t,Te=null;break}e:{var u=e,h=n.return,v=n,_=t;if(t=ze,v.flags|=32768,_!==null&&typeof _=="object"&&typeof _.then=="function"){var C=_,R=v,b=R.tag;if((R.mode&1)===0&&(b===0||b===11||b===15)){var N=R.alternate;N?(R.updateQueue=N.updateQueue,R.memoizedState=N.memoizedState,R.lanes=N.lanes):(R.updateQueue=null,R.memoizedState=null)}var M=ec(h);if(M!==null){M.flags&=-257,tc(M,h,v,u,t),M.mode&1&&Zu(u,C,t),t=M,_=C;var H=t.updateQueue;if(H===null){var W=new Set;W.add(_),t.updateQueue=W}else H.add(_);break e}else{if((t&1)===0){Zu(u,C,t),gl();break e}_=Error(i(426))}}else if(ke&&v.mode&1){var Ne=ec(h);if(Ne!==null){(Ne.flags&65536)===0&&(Ne.flags|=256),tc(Ne,h,v,u,t),Po(Jn(_,v));break e}}u=_=Jn(_,v),be!==4&&(be=2),Jr===null?Jr=[u]:Jr.push(u),u=h;do{switch(u.tag){case 3:u.flags|=65536,t&=-t,u.lanes|=t;var S=Yu(u,_,t);Eu(u,S);break e;case 1:v=_;var x=u.type,j=u.stateNode;if((u.flags&128)===0&&(typeof x.getDerivedStateFromError=="function"||j!==null&&typeof j.componentDidCatch=="function"&&(tn===null||!tn.has(j)))){u.flags|=65536,t&=-t,u.lanes|=t;var L=Xu(u,v,t);Eu(u,L);break e}}u=u.return}while(u!==null)}Oc(n)}catch(G){t=G,Te===n&&n!==null&&(Te=n=n.return);continue}break}while(!0)}function Rc(){var e=ti.current;return ti.current=Ks,e===null?Ks:e}function gl(){(be===0||be===3||be===2)&&(be=4),$e===null||(_n&268435455)===0&&(ni&268435455)===0||sn($e,ze)}function ai(e,t){var n=le;le|=2;var r=Rc();($e!==e||ze!==t)&&(Mt=null,kn(e,t));do try{Ch();break}catch(o){Tc(e,o)}while(!0);if(To(),le=n,ti.current=r,Te!==null)throw Error(i(261));return $e=null,ze=0,be}function Ch(){for(;Te!==null;)bc(Te)}function Ph(){for(;Te!==null&&!Yd();)bc(Te)}function bc(e){var t=Ic(e.alternate,e,it);e.memoizedProps=e.pendingProps,t===null?Oc(e):Te=t,ll.current=null}function Oc(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=wh(n,t,it),n!==null){Te=n;return}}else{if(n=_h(n,t),n!==null){n.flags&=32767,Te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{be=6,Te=null;return}}if(t=t.sibling,t!==null){Te=t;return}Te=t=e}while(t!==null);be===0&&(be=5)}function Sn(e,t,n){var r=he,o=ft.transition;try{ft.transition=null,he=1,Nh(e,t,n,r)}finally{ft.transition=o,he=r}return null}function Nh(e,t,n,r){do er();while(nn!==null);if((le&6)!==0)throw Error(i(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var u=n.lanes|n.childLanes;if(af(e,u),e===$e&&(Te=$e=null,ze=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||si||(si=!0,Ac(fs,function(){return er(),null})),u=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||u){u=ft.transition,ft.transition=null;var h=he;he=1;var v=le;le|=4,ll.current=null,kh(e,n),kc(n,e),Qf(go),ws=!!mo,go=mo=null,e.current=n,Sh(n),Xd(),le=v,he=h,ft.transition=u}else e.current=n;if(si&&(si=!1,nn=e,ii=o),u=e.pendingLanes,u===0&&(tn=null),tf(n.stateNode),Ye(e,Pe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(ri)throw ri=!1,e=cl,cl=null,e;return(ii&1)!==0&&e.tag!==0&&er(),u=e.pendingLanes,(u&1)!==0?e===dl?Yr++:(Yr=0,dl=e):Yr=0,Yt(),null}function er(){if(nn!==null){var e=_a(ii),t=ft.transition,n=he;try{if(ft.transition=null,he=16>e?16:e,nn===null)var r=!1;else{if(e=nn,nn=null,ii=0,(le&6)!==0)throw Error(i(331));var o=le;for(le|=4,V=e.current;V!==null;){var u=V,h=u.child;if((V.flags&16)!==0){var v=u.deletions;if(v!==null){for(var _=0;_<v.length;_++){var C=v[_];for(V=C;V!==null;){var R=V;switch(R.tag){case 0:case 11:case 15:Qr(8,R,u)}var b=R.child;if(b!==null)b.return=R,V=b;else for(;V!==null;){R=V;var N=R.sibling,M=R.return;if(vc(R),R===C){V=null;break}if(N!==null){N.return=M,V=N;break}V=M}}}var H=u.alternate;if(H!==null){var W=H.child;if(W!==null){H.child=null;do{var Ne=W.sibling;W.sibling=null,W=Ne}while(W!==null)}}V=u}}if((u.subtreeFlags&2064)!==0&&h!==null)h.return=u,V=h;else e:for(;V!==null;){if(u=V,(u.flags&2048)!==0)switch(u.tag){case 0:case 11:case 15:Qr(9,u,u.return)}var S=u.sibling;if(S!==null){S.return=u.return,V=S;break e}V=u.return}}var x=e.current;for(V=x;V!==null;){h=V;var j=h.child;if((h.subtreeFlags&2064)!==0&&j!==null)j.return=h,V=j;else e:for(h=x;V!==null;){if(v=V,(v.flags&2048)!==0)try{switch(v.tag){case 0:case 11:case 15:ei(9,v)}}catch(G){Ce(v,v.return,G)}if(v===h){V=null;break e}var L=v.sibling;if(L!==null){L.return=v.return,V=L;break e}V=v.return}}if(le=o,Yt(),Pt&&typeof Pt.onPostCommitFiberRoot=="function")try{Pt.onPostCommitFiberRoot(hs,e)}catch{}r=!0}return r}finally{he=n,ft.transition=t}}return!1}function $c(e,t,n){t=Jn(n,t),t=Yu(e,t,1),e=Zt(e,t,1),t=qe(),e!==null&&(xr(e,1,t),Ye(e,t))}function Ce(e,t,n){if(e.tag===3)$c(e,e,n);else for(;t!==null;){if(t.tag===3){$c(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(tn===null||!tn.has(r))){e=Jn(n,e),e=Xu(t,e,1),t=Zt(t,e,1),e=qe(),t!==null&&(xr(t,1,e),Ye(t,e));break}}t=t.return}}function Th(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=qe(),e.pingedLanes|=e.suspendedLanes&n,$e===e&&(ze&n)===n&&(be===4||be===3&&(ze&130023424)===ze&&500>Pe()-ul?kn(e,0):al|=n),Ye(e,t)}function Lc(e,t){t===0&&((e.mode&1)===0?t=1:(t=ms,ms<<=1,(ms&130023424)===0&&(ms=4194304)));var n=qe();e=Dt(e,t),e!==null&&(xr(e,t,n),Ye(e,n))}function Rh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Lc(e,n)}function bh(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(i(314))}r!==null&&r.delete(t),Lc(e,n)}var Ic;Ic=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ge.current)Ke=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return Ke=!1,yh(e,t,n);Ke=(e.flags&131072)!==0}else Ke=!1,ke&&(t.flags&1048576)!==0&&pu(t,Ds,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Xs(e,t),e=t.pendingProps;var o=Vn(t,Me.current);Qn(t,n),o=Mo(null,t,r,e,o,n);var u=Fo();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(r)?(u=!0,Ls(t)):u=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,$o(t),o.updater=Js,t.stateNode=o,o._reactInternals=t,Go(t,r,e,n),t=Yo(null,t,r,!0,u,n)):(t.tag=0,ke&&u&&So(t),He(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Xs(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=$h(r),e=_t(r,e),o){case 0:t=Jo(null,t,r,e,n);break e;case 1:t=lc(null,t,r,e,n);break e;case 11:t=nc(null,t,r,e,n);break e;case 14:t=rc(null,t,r,_t(r.type,e),n);break e}throw Error(i(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),Jo(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),lc(e,t,r,o,n);case 3:e:{if(ac(t),e===null)throw Error(i(387));r=t.pendingProps,u=t.memoizedState,o=u.element,Su(e,t),Bs(t,r,null,n);var h=t.memoizedState;if(r=h.element,u.isDehydrated)if(u={element:r,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){o=Jn(Error(i(423)),t),t=uc(e,t,r,n,o);break e}else if(r!==o){o=Jn(Error(i(424)),t),t=uc(e,t,r,n,o);break e}else for(st=Qt(t.stateNode.containerInfo.firstChild),rt=t,ke=!0,wt=null,n=xu(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(qn(),r===o){t=Ut(e,t,n);break e}He(e,t,r,n)}t=t.child}return t;case 5:return Cu(t),e===null&&Co(t),r=t.type,o=t.pendingProps,u=e!==null?e.memoizedProps:null,h=o.children,vo(r,o)?h=null:u!==null&&vo(r,u)&&(t.flags|=32),oc(e,t),He(e,t,h,n),t.child;case 6:return e===null&&Co(t),null;case 13:return cc(e,t,n);case 4:return Lo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wn(t,null,r,n):He(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),nc(e,t,r,o,n);case 7:return He(e,t,t.pendingProps,n),t.child;case 8:return He(e,t,t.pendingProps.children,n),t.child;case 12:return He(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,u=t.memoizedProps,h=o.value,ge(Ms,r._currentValue),r._currentValue=h,u!==null)if(yt(u.value,h)){if(u.children===o.children&&!Ge.current){t=Ut(e,t,n);break e}}else for(u=t.child,u!==null&&(u.return=t);u!==null;){var v=u.dependencies;if(v!==null){h=u.child;for(var _=v.firstContext;_!==null;){if(_.context===r){if(u.tag===1){_=zt(-1,n&-n),_.tag=2;var C=u.updateQueue;if(C!==null){C=C.shared;var R=C.pending;R===null?_.next=_:(_.next=R.next,R.next=_),C.pending=_}}u.lanes|=n,_=u.alternate,_!==null&&(_.lanes|=n),bo(u.return,n,t),v.lanes|=n;break}_=_.next}}else if(u.tag===10)h=u.type===t.type?null:u.child;else if(u.tag===18){if(h=u.return,h===null)throw Error(i(341));h.lanes|=n,v=h.alternate,v!==null&&(v.lanes|=n),bo(h,n,t),h=u.sibling}else h=u.child;if(h!==null)h.return=u;else for(h=u;h!==null;){if(h===t){h=null;break}if(u=h.sibling,u!==null){u.return=h.return,h=u;break}h=h.return}u=h}He(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Qn(t,n),o=ct(o),r=r(o),t.flags|=1,He(e,t,r,n),t.child;case 14:return r=t.type,o=_t(r,t.pendingProps),o=_t(r.type,o),rc(e,t,r,o,n);case 15:return sc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:_t(r,o),Xs(e,t),t.tag=1,Qe(r)?(e=!0,Ls(t)):e=!1,Qn(t,n),Ku(t,r,o),Go(t,r,o,n),Yo(null,t,r,!0,e,n);case 19:return fc(e,t,n);case 22:return ic(e,t,n)}throw Error(i(156,t.tag))};function Ac(e,t){return ma(e,t)}function Oh(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ht(e,t,n,r){return new Oh(e,t,n,r)}function vl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function $h(e){if(typeof e=="function")return vl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===mt)return 11;if(e===Ct)return 14}return 2}function on(e,t){var n=e.alternate;return n===null?(n=ht(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ui(e,t,n,r,o,u){var h=2;if(r=e,typeof e=="function")vl(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case X:return En(n.children,o,u,t);case oe:h=8,o|=8;break;case pe:return e=ht(12,n,t,o|2),e.elementType=pe,e.lanes=u,e;case et:return e=ht(13,n,t,o),e.elementType=et,e.lanes=u,e;case gt:return e=ht(19,n,t,o),e.elementType=gt,e.lanes=u,e;case je:return ci(n,o,u,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case _e:h=10;break e;case lt:h=9;break e;case mt:h=11;break e;case Ct:h=14;break e;case We:h=16,r=null;break e}throw Error(i(130,e==null?e:typeof e,""))}return t=ht(h,n,t,o),t.elementType=e,t.type=r,t.lanes=u,t}function En(e,t,n,r){return e=ht(7,e,r,t),e.lanes=n,e}function ci(e,t,n,r){return e=ht(22,e,r,t),e.elementType=je,e.lanes=n,e.stateNode={isHidden:!1},e}function yl(e,t,n){return e=ht(6,e,null,t),e.lanes=n,e}function wl(e,t,n){return t=ht(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Lh(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=qi(0),this.expirationTimes=qi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=qi(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function _l(e,t,n,r,o,u,h,v,_){return e=new Lh(e,t,n,v,_),t===1?(t=1,u===!0&&(t|=8)):t=0,u=ht(3,null,null,t),e.current=u,u.stateNode=e,u.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},$o(u),e}function Ih(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:A,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Dc(e){if(!e)return Jt;e=e._reactInternals;e:{if(fn(e)!==e||e.tag!==1)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(i(171))}if(e.tag===1){var n=e.type;if(Qe(n))return du(e,n,t)}return t}function zc(e,t,n,r,o,u,h,v,_){return e=_l(n,r,!0,e,o,u,h,v,_),e.context=Dc(null),n=e.current,r=qe(),o=rn(n),u=zt(r,o),u.callback=t??null,Zt(n,u,o),e.current.lanes=o,xr(e,o,r),Ye(e,r),e}function di(e,t,n,r){var o=t.current,u=qe(),h=rn(o);return n=Dc(n),t.context===null?t.context=n:t.pendingContext=n,t=zt(u,h),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Zt(o,t,h),e!==null&&(St(e,o,h,u),Vs(e,o,h)),h}function fi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Uc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function xl(e,t){Uc(e,t),(e=e.alternate)&&Uc(e,t)}function Ah(){return null}var Mc=typeof reportError=="function"?reportError:function(e){console.error(e)};function kl(e){this._internalRoot=e}hi.prototype.render=kl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(i(409));di(e,t,null,null)},hi.prototype.unmount=kl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;xn(function(){di(null,e,null,null)}),t[$t]=null}};function hi(e){this._internalRoot=e}hi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Sa();e={blockedOn:null,target:e,priority:t};for(var n=0;n<qt.length&&t!==0&&t<qt[n].priority;n++);qt.splice(n,0,e),n===0&&Ca(e)}};function Sl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function pi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Fc(){}function Dh(e,t,n,r,o){if(o){if(typeof r=="function"){var u=r;r=function(){var C=fi(h);u.call(C)}}var h=zc(t,r,e,0,null,!1,!1,"",Fc);return e._reactRootContainer=h,e[$t]=h.current,Ir(e.nodeType===8?e.parentNode:e),xn(),h}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var v=r;r=function(){var C=fi(_);v.call(C)}}var _=_l(e,0,!1,null,null,!1,!1,"",Fc);return e._reactRootContainer=_,e[$t]=_.current,Ir(e.nodeType===8?e.parentNode:e),xn(function(){di(t,_,n,r)}),_}function mi(e,t,n,r,o){var u=n._reactRootContainer;if(u){var h=u;if(typeof o=="function"){var v=o;o=function(){var _=fi(h);v.call(_)}}di(t,h,e,o)}else h=Dh(n,t,e,o,r);return fi(h)}xa=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=_r(t.pendingLanes);n!==0&&(Wi(t,n|1),Ye(t,Pe()),(le&6)===0&&(Zn=Pe()+500,Yt()))}break;case 13:xn(function(){var r=Dt(e,1);if(r!==null){var o=qe();St(r,e,1,o)}}),xl(e,1)}},Gi=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=qe();St(t,e,134217728,n)}xl(e,134217728)}},ka=function(e){if(e.tag===13){var t=rn(e),n=Dt(e,t);if(n!==null){var r=qe();St(n,e,t,r)}xl(e,t)}},Sa=function(){return he},Ea=function(e,t){var n=he;try{return he=e,t()}finally{he=n}},Ui=function(e,t,n){switch(t){case"input":if(bi(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Os(r);if(!o)throw Error(i(90));Ql(r),bi(r,o)}}}break;case"textarea":Zl(e,n);break;case"select":t=n.value,t!=null&&Rn(e,!!n.multiple,t,!1)}},aa=pl,ua=xn;var zh={usingClientEntryPoint:!1,Events:[zr,Mn,Os,oa,la,pl]},Xr={findFiberByHostInstance:hn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Uh={bundleType:Xr.bundleType,version:Xr.version,rendererPackageName:Xr.rendererPackageName,rendererConfig:Xr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ha(e),e===null?null:e.stateNode},findFiberByHostInstance:Xr.findFiberByHostInstance||Ah,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gi.isDisabled&&gi.supportsFiber)try{hs=gi.inject(Uh),Pt=gi}catch{}}return Xe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=zh,Xe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Sl(t))throw Error(i(200));return Ih(e,t,null,n)},Xe.createRoot=function(e,t){if(!Sl(e))throw Error(i(299));var n=!1,r="",o=Mc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=_l(e,1,!1,null,null,n,!1,r,o),e[$t]=t.current,Ir(e.nodeType===8?e.parentNode:e),new kl(t)},Xe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(i(188)):(e=Object.keys(e).join(","),Error(i(268,e)));return e=ha(t),e=e===null?null:e.stateNode,e},Xe.flushSync=function(e){return xn(e)},Xe.hydrate=function(e,t,n){if(!pi(t))throw Error(i(200));return mi(null,e,t,!0,n)},Xe.hydrateRoot=function(e,t,n){if(!Sl(e))throw Error(i(405));var r=n!=null&&n.hydratedSources||null,o=!1,u="",h=Mc;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(h=n.onRecoverableError)),t=zc(t,null,e,1,n??null,o,!1,u,h),e[$t]=t.current,Ir(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new hi(t)},Xe.render=function(e,t,n){if(!pi(t))throw Error(i(200));return mi(null,e,t,!1,n)},Xe.unmountComponentAtNode=function(e){if(!pi(e))throw Error(i(40));return e._reactRootContainer?(xn(function(){mi(null,null,e,!1,function(){e._reactRootContainer=null,e[$t]=null})}),!0):!1},Xe.unstable_batchedUpdates=pl,Xe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!pi(n))throw Error(i(200));if(e==null||e._reactInternals===void 0)throw Error(i(38));return mi(e,t,n,!1,r)},Xe.version="18.3.1-next-f1338f8080-20240426",Xe}var Kc;function Kh(){if(Kc)return Cl.exports;Kc=1;function c(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(c)}catch(s){console.error(s)}}return c(),Cl.exports=Qh(),Cl.exports}var Jc;function Jh(){if(Jc)return vi;Jc=1;var c=Kh();return vi.createRoot=c.createRoot,vi.hydrateRoot=c.hydrateRoot,vi}var Yh=Jh();const Xh="modulepreload",Zh=function(c){return"/"+c},Yc={},dr=function(s,i,l){let a=Promise.resolve();if(i&&i.length>0){document.getElementsByTagName("link");const f=document.querySelector("meta[property=csp-nonce]"),m=(f==null?void 0:f.nonce)||(f==null?void 0:f.getAttribute("nonce"));a=Promise.allSettled(i.map(p=>{if(p=Zh(p),p in Yc)return;Yc[p]=!0;const g=p.endsWith(".css"),y=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${y}`))return;const E=document.createElement("link");if(E.rel=g?"stylesheet":Xh,g||(E.as="script"),E.crossOrigin="",E.href=p,m&&E.setAttribute("nonce",m),document.head.appendChild(E),g)return new Promise((P,O)=>{E.addEventListener("load",P),E.addEventListener("error",()=>O(new Error(`Unable to preload CSS for ${p}`)))})}))}function d(f){const m=new Event("vite:preloadError",{cancelable:!0});if(m.payload=f,window.dispatchEvent(m),!m.defaultPrevented)throw f}return a.then(f=>{for(const m of f||[])m.status==="rejected"&&d(m.reason);return s().catch(d)})},ep=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>dr(async()=>{const{default:l}=await Promise.resolve().then(()=>hr);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)};class Bl extends Error{constructor(s,i="FunctionsError",l){super(s),this.name=i,this.context=l}}class tp extends Bl{constructor(s){super("Failed to send a request to the Edge Function","FunctionsFetchError",s)}}class np extends Bl{constructor(s){super("Relay Error invoking the Edge Function","FunctionsRelayError",s)}}class rp extends Bl{constructor(s){super("Edge Function returned a non-2xx status code","FunctionsHttpError",s)}}var Ll;(function(c){c.Any="any",c.ApNortheast1="ap-northeast-1",c.ApNortheast2="ap-northeast-2",c.ApSouth1="ap-south-1",c.ApSoutheast1="ap-southeast-1",c.ApSoutheast2="ap-southeast-2",c.CaCentral1="ca-central-1",c.EuCentral1="eu-central-1",c.EuWest1="eu-west-1",c.EuWest2="eu-west-2",c.EuWest3="eu-west-3",c.SaEast1="sa-east-1",c.UsEast1="us-east-1",c.UsWest1="us-west-1",c.UsWest2="us-west-2"})(Ll||(Ll={}));var sp=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};class ip{constructor(s,{headers:i={},customFetch:l,region:a=Ll.Any}={}){this.url=s,this.headers=i,this.region=a,this.fetch=ep(l)}setAuth(s){this.headers.Authorization=`Bearer ${s}`}invoke(s,i={}){var l;return sp(this,void 0,void 0,function*(){try{const{headers:a,method:d,body:f}=i;let m={},{region:p}=i;p||(p=this.region),p&&p!=="any"&&(m["x-region"]=p);let g;f&&(a&&!Object.prototype.hasOwnProperty.call(a,"Content-Type")||!a)&&(typeof Blob<"u"&&f instanceof Blob||f instanceof ArrayBuffer?(m["Content-Type"]="application/octet-stream",g=f):typeof f=="string"?(m["Content-Type"]="text/plain",g=f):typeof FormData<"u"&&f instanceof FormData?g=f:(m["Content-Type"]="application/json",g=JSON.stringify(f)));const y=yield this.fetch(`${this.url}/${s}`,{method:d||"POST",headers:Object.assign(Object.assign(Object.assign({},m),this.headers),a),body:g}).catch(I=>{throw new tp(I)}),E=y.headers.get("x-relay-error");if(E&&E==="true")throw new np(y);if(!y.ok)throw new rp(y);let P=((l=y.headers.get("Content-Type"))!==null&&l!==void 0?l:"text/plain").split(";")[0].trim(),O;return P==="application/json"?O=yield y.json():P==="application/octet-stream"?O=yield y.blob():P==="text/event-stream"?O=y:P==="multipart/form-data"?O=yield y.formData():O=yield y.text(),{data:O,error:null}}catch(a){return{data:null,error:a}}})}}var Ie={},tr={},nr={},rr={},sr={},ir={},op=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},fr=op();const lp=fr.fetch,_d=fr.fetch.bind(fr),xd=fr.Headers,ap=fr.Request,up=fr.Response,hr=Object.freeze(Object.defineProperty({__proto__:null,Headers:xd,Request:ap,Response:up,default:_d,fetch:lp},Symbol.toStringTag,{value:"Module"})),cp=Vh(hr);var yi={},Xc;function kd(){if(Xc)return yi;Xc=1,Object.defineProperty(yi,"__esModule",{value:!0});class c extends Error{constructor(i){super(i.message),this.name="PostgrestError",this.details=i.details,this.hint=i.hint,this.code=i.code}}return yi.default=c,yi}var Zc;function Sd(){if(Zc)return ir;Zc=1;var c=ir&&ir.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(ir,"__esModule",{value:!0});const s=c(cp),i=c(kd());class l{constructor(d){this.shouldThrowOnError=!1,this.method=d.method,this.url=d.url,this.headers=d.headers,this.schema=d.schema,this.body=d.body,this.shouldThrowOnError=d.shouldThrowOnError,this.signal=d.signal,this.isMaybeSingle=d.isMaybeSingle,d.fetch?this.fetch=d.fetch:typeof fetch>"u"?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(d,f){return this.headers=Object.assign({},this.headers),this.headers[d]=f,this}then(d,f){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const m=this.fetch;let p=m(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async g=>{var y,E,P;let O=null,I=null,F=null,D=g.status,ne=g.statusText;if(g.ok){if(this.method!=="HEAD"){const $=await g.text();$===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?I=$:I=JSON.parse($))}const U=(y=this.headers.Prefer)===null||y===void 0?void 0:y.match(/count=(exact|planned|estimated)/),q=(E=g.headers.get("content-range"))===null||E===void 0?void 0:E.split("/");U&&q&&q.length>1&&(F=parseInt(q[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(I)&&(I.length>1?(O={code:"PGRST116",details:`Results contain ${I.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},I=null,F=null,D=406,ne="Not Acceptable"):I.length===1?I=I[0]:I=null)}else{const U=await g.text();try{O=JSON.parse(U),Array.isArray(O)&&g.status===404&&(I=[],O=null,D=200,ne="OK")}catch{g.status===404&&U===""?(D=204,ne="No Content"):O={message:U}}if(O&&this.isMaybeSingle&&(!((P=O==null?void 0:O.details)===null||P===void 0)&&P.includes("0 rows"))&&(O=null,D=200,ne="OK"),O&&this.shouldThrowOnError)throw new i.default(O)}return{error:O,data:I,count:F,status:D,statusText:ne}});return this.shouldThrowOnError||(p=p.catch(g=>{var y,E,P;return{error:{message:`${(y=g==null?void 0:g.name)!==null&&y!==void 0?y:"FetchError"}: ${g==null?void 0:g.message}`,details:`${(E=g==null?void 0:g.stack)!==null&&E!==void 0?E:""}`,hint:"",code:`${(P=g==null?void 0:g.code)!==null&&P!==void 0?P:""}`},data:null,count:null,status:0,statusText:""}})),p.then(d,f)}returns(){return this}overrideTypes(){return this}}return ir.default=l,ir}var ed;function Ed(){if(ed)return sr;ed=1;var c=sr&&sr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(sr,"__esModule",{value:!0});const s=c(Sd());class i extends s.default{select(a){let d=!1;const f=(a??"*").split("").map(m=>/\s/.test(m)&&!d?"":(m==='"'&&(d=!d),m)).join("");return this.url.searchParams.set("select",f),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(a,{ascending:d=!0,nullsFirst:f,foreignTable:m,referencedTable:p=m}={}){const g=p?`${p}.order`:"order",y=this.url.searchParams.get(g);return this.url.searchParams.set(g,`${y?`${y},`:""}${a}.${d?"asc":"desc"}${f===void 0?"":f?".nullsfirst":".nullslast"}`),this}limit(a,{foreignTable:d,referencedTable:f=d}={}){const m=typeof f>"u"?"limit":`${f}.limit`;return this.url.searchParams.set(m,`${a}`),this}range(a,d,{foreignTable:f,referencedTable:m=f}={}){const p=typeof m>"u"?"offset":`${m}.offset`,g=typeof m>"u"?"limit":`${m}.limit`;return this.url.searchParams.set(p,`${a}`),this.url.searchParams.set(g,`${d-a+1}`),this}abortSignal(a){return this.signal=a,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:a=!1,verbose:d=!1,settings:f=!1,buffers:m=!1,wal:p=!1,format:g="text"}={}){var y;const E=[a?"analyze":null,d?"verbose":null,f?"settings":null,m?"buffers":null,p?"wal":null].filter(Boolean).join("|"),P=(y=this.headers.Accept)!==null&&y!==void 0?y:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${g}; for="${P}"; options=${E};`,g==="json"?this:this}rollback(){var a;return((a=this.headers.Prefer)!==null&&a!==void 0?a:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return sr.default=i,sr}var td;function Hl(){if(td)return rr;td=1;var c=rr&&rr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(rr,"__esModule",{value:!0});const s=c(Ed());class i extends s.default{eq(a,d){return this.url.searchParams.append(a,`eq.${d}`),this}neq(a,d){return this.url.searchParams.append(a,`neq.${d}`),this}gt(a,d){return this.url.searchParams.append(a,`gt.${d}`),this}gte(a,d){return this.url.searchParams.append(a,`gte.${d}`),this}lt(a,d){return this.url.searchParams.append(a,`lt.${d}`),this}lte(a,d){return this.url.searchParams.append(a,`lte.${d}`),this}like(a,d){return this.url.searchParams.append(a,`like.${d}`),this}likeAllOf(a,d){return this.url.searchParams.append(a,`like(all).{${d.join(",")}}`),this}likeAnyOf(a,d){return this.url.searchParams.append(a,`like(any).{${d.join(",")}}`),this}ilike(a,d){return this.url.searchParams.append(a,`ilike.${d}`),this}ilikeAllOf(a,d){return this.url.searchParams.append(a,`ilike(all).{${d.join(",")}}`),this}ilikeAnyOf(a,d){return this.url.searchParams.append(a,`ilike(any).{${d.join(",")}}`),this}is(a,d){return this.url.searchParams.append(a,`is.${d}`),this}in(a,d){const f=Array.from(new Set(d)).map(m=>typeof m=="string"&&new RegExp("[,()]").test(m)?`"${m}"`:`${m}`).join(",");return this.url.searchParams.append(a,`in.(${f})`),this}contains(a,d){return typeof d=="string"?this.url.searchParams.append(a,`cs.${d}`):Array.isArray(d)?this.url.searchParams.append(a,`cs.{${d.join(",")}}`):this.url.searchParams.append(a,`cs.${JSON.stringify(d)}`),this}containedBy(a,d){return typeof d=="string"?this.url.searchParams.append(a,`cd.${d}`):Array.isArray(d)?this.url.searchParams.append(a,`cd.{${d.join(",")}}`):this.url.searchParams.append(a,`cd.${JSON.stringify(d)}`),this}rangeGt(a,d){return this.url.searchParams.append(a,`sr.${d}`),this}rangeGte(a,d){return this.url.searchParams.append(a,`nxl.${d}`),this}rangeLt(a,d){return this.url.searchParams.append(a,`sl.${d}`),this}rangeLte(a,d){return this.url.searchParams.append(a,`nxr.${d}`),this}rangeAdjacent(a,d){return this.url.searchParams.append(a,`adj.${d}`),this}overlaps(a,d){return typeof d=="string"?this.url.searchParams.append(a,`ov.${d}`):this.url.searchParams.append(a,`ov.{${d.join(",")}}`),this}textSearch(a,d,{config:f,type:m}={}){let p="";m==="plain"?p="pl":m==="phrase"?p="ph":m==="websearch"&&(p="w");const g=f===void 0?"":`(${f})`;return this.url.searchParams.append(a,`${p}fts${g}.${d}`),this}match(a){return Object.entries(a).forEach(([d,f])=>{this.url.searchParams.append(d,`eq.${f}`)}),this}not(a,d,f){return this.url.searchParams.append(a,`not.${d}.${f}`),this}or(a,{foreignTable:d,referencedTable:f=d}={}){const m=f?`${f}.or`:"or";return this.url.searchParams.append(m,`(${a})`),this}filter(a,d,f){return this.url.searchParams.append(a,`${d}.${f}`),this}}return rr.default=i,rr}var nd;function jd(){if(nd)return nr;nd=1;var c=nr&&nr.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(nr,"__esModule",{value:!0});const s=c(Hl());class i{constructor(a,{headers:d={},schema:f,fetch:m}){this.url=a,this.headers=d,this.schema=f,this.fetch=m}select(a,{head:d=!1,count:f}={}){const m=d?"HEAD":"GET";let p=!1;const g=(a??"*").split("").map(y=>/\s/.test(y)&&!p?"":(y==='"'&&(p=!p),y)).join("");return this.url.searchParams.set("select",g),f&&(this.headers.Prefer=`count=${f}`),new s.default({method:m,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(a,{count:d,defaultToNull:f=!0}={}){const m="POST",p=[];if(this.headers.Prefer&&p.push(this.headers.Prefer),d&&p.push(`count=${d}`),f||p.push("missing=default"),this.headers.Prefer=p.join(","),Array.isArray(a)){const g=a.reduce((y,E)=>y.concat(Object.keys(E)),[]);if(g.length>0){const y=[...new Set(g)].map(E=>`"${E}"`);this.url.searchParams.set("columns",y.join(","))}}return new s.default({method:m,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}upsert(a,{onConflict:d,ignoreDuplicates:f=!1,count:m,defaultToNull:p=!0}={}){const g="POST",y=[`resolution=${f?"ignore":"merge"}-duplicates`];if(d!==void 0&&this.url.searchParams.set("on_conflict",d),this.headers.Prefer&&y.push(this.headers.Prefer),m&&y.push(`count=${m}`),p||y.push("missing=default"),this.headers.Prefer=y.join(","),Array.isArray(a)){const E=a.reduce((P,O)=>P.concat(Object.keys(O)),[]);if(E.length>0){const P=[...new Set(E)].map(O=>`"${O}"`);this.url.searchParams.set("columns",P.join(","))}}return new s.default({method:g,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}update(a,{count:d}={}){const f="PATCH",m=[];return this.headers.Prefer&&m.push(this.headers.Prefer),d&&m.push(`count=${d}`),this.headers.Prefer=m.join(","),new s.default({method:f,url:this.url,headers:this.headers,schema:this.schema,body:a,fetch:this.fetch,allowEmpty:!1})}delete({count:a}={}){const d="DELETE",f=[];return a&&f.push(`count=${a}`),this.headers.Prefer&&f.unshift(this.headers.Prefer),this.headers.Prefer=f.join(","),new s.default({method:d,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}return nr.default=i,nr}var es={},ts={},rd;function dp(){return rd||(rd=1,Object.defineProperty(ts,"__esModule",{value:!0}),ts.version=void 0,ts.version="0.0.0-automated"),ts}var sd;function fp(){if(sd)return es;sd=1,Object.defineProperty(es,"__esModule",{value:!0}),es.DEFAULT_HEADERS=void 0;const c=dp();return es.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${c.version}`},es}var id;function hp(){if(id)return tr;id=1;var c=tr&&tr.__importDefault||function(d){return d&&d.__esModule?d:{default:d}};Object.defineProperty(tr,"__esModule",{value:!0});const s=c(jd()),i=c(Hl()),l=fp();class a{constructor(f,{headers:m={},schema:p,fetch:g}={}){this.url=f,this.headers=Object.assign(Object.assign({},l.DEFAULT_HEADERS),m),this.schemaName=p,this.fetch=g}from(f){const m=new URL(`${this.url}/${f}`);return new s.default(m,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(f){return new a(this.url,{headers:this.headers,schema:f,fetch:this.fetch})}rpc(f,m={},{head:p=!1,get:g=!1,count:y}={}){let E;const P=new URL(`${this.url}/rpc/${f}`);let O;p||g?(E=p?"HEAD":"GET",Object.entries(m).filter(([F,D])=>D!==void 0).map(([F,D])=>[F,Array.isArray(D)?`{${D.join(",")}}`:`${D}`]).forEach(([F,D])=>{P.searchParams.append(F,D)})):(E="POST",O=m);const I=Object.assign({},this.headers);return y&&(I.Prefer=`count=${y}`),new i.default({method:E,url:P,headers:I,schema:this.schemaName,body:O,fetch:this.fetch,allowEmpty:!1})}}return tr.default=a,tr}var od;function pp(){if(od)return Ie;od=1;var c=Ie&&Ie.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(Ie,"__esModule",{value:!0}),Ie.PostgrestError=Ie.PostgrestBuilder=Ie.PostgrestTransformBuilder=Ie.PostgrestFilterBuilder=Ie.PostgrestQueryBuilder=Ie.PostgrestClient=void 0;const s=c(hp());Ie.PostgrestClient=s.default;const i=c(jd());Ie.PostgrestQueryBuilder=i.default;const l=c(Hl());Ie.PostgrestFilterBuilder=l.default;const a=c(Ed());Ie.PostgrestTransformBuilder=a.default;const d=c(Sd());Ie.PostgrestBuilder=d.default;const f=c(kd());return Ie.PostgrestError=f.default,Ie.default={PostgrestClient:s.default,PostgrestQueryBuilder:i.default,PostgrestFilterBuilder:l.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:d.default,PostgrestError:f.default},Ie}var mp=pp();const gp=Fh(mp),{PostgrestClient:vp,PostgrestQueryBuilder:wg,PostgrestFilterBuilder:_g,PostgrestTransformBuilder:xg,PostgrestBuilder:kg,PostgrestError:Sg}=gp,yp="2.11.2",wp={"X-Client-Info":`realtime-js/${yp}`},_p="1.0.0",Cd=1e4,xp=1e3;var cr;(function(c){c[c.connecting=0]="connecting",c[c.open=1]="open",c[c.closing=2]="closing",c[c.closed=3]="closed"})(cr||(cr={}));var ot;(function(c){c.closed="closed",c.errored="errored",c.joined="joined",c.joining="joining",c.leaving="leaving"})(ot||(ot={}));var Et;(function(c){c.close="phx_close",c.error="phx_error",c.join="phx_join",c.reply="phx_reply",c.leave="phx_leave",c.access_token="access_token"})(Et||(Et={}));var Il;(function(c){c.websocket="websocket"})(Il||(Il={}));var Pn;(function(c){c.Connecting="connecting",c.Open="open",c.Closing="closing",c.Closed="closed"})(Pn||(Pn={}));class kp{constructor(){this.HEADER_LENGTH=1}decode(s,i){return s.constructor===ArrayBuffer?i(this._binaryDecode(s)):i(typeof s=="string"?JSON.parse(s):{})}_binaryDecode(s){const i=new DataView(s),l=new TextDecoder;return this._decodeBroadcast(s,i,l)}_decodeBroadcast(s,i,l){const a=i.getUint8(1),d=i.getUint8(2);let f=this.HEADER_LENGTH+2;const m=l.decode(s.slice(f,f+a));f=f+a;const p=l.decode(s.slice(f,f+d));f=f+d;const g=JSON.parse(l.decode(s.slice(f,s.byteLength)));return{ref:null,topic:m,event:p,payload:g}}}class Pd{constructor(s,i){this.callback=s,this.timerCalc=i,this.timer=void 0,this.tries=0,this.callback=s,this.timerCalc=i}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var ve;(function(c){c.abstime="abstime",c.bool="bool",c.date="date",c.daterange="daterange",c.float4="float4",c.float8="float8",c.int2="int2",c.int4="int4",c.int4range="int4range",c.int8="int8",c.int8range="int8range",c.json="json",c.jsonb="jsonb",c.money="money",c.numeric="numeric",c.oid="oid",c.reltime="reltime",c.text="text",c.time="time",c.timestamp="timestamp",c.timestamptz="timestamptz",c.timetz="timetz",c.tsrange="tsrange",c.tstzrange="tstzrange"})(ve||(ve={}));const ld=(c,s,i={})=>{var l;const a=(l=i.skipTypes)!==null&&l!==void 0?l:[];return Object.keys(s).reduce((d,f)=>(d[f]=Sp(f,c,s,a),d),{})},Sp=(c,s,i,l)=>{const a=s.find(m=>m.name===c),d=a==null?void 0:a.type,f=i[c];return d&&!l.includes(d)?Nd(d,f):Al(f)},Nd=(c,s)=>{if(c.charAt(0)==="_"){const i=c.slice(1,c.length);return Pp(s,i)}switch(c){case ve.bool:return Ep(s);case ve.float4:case ve.float8:case ve.int2:case ve.int4:case ve.int8:case ve.numeric:case ve.oid:return jp(s);case ve.json:case ve.jsonb:return Cp(s);case ve.timestamp:return Np(s);case ve.abstime:case ve.date:case ve.daterange:case ve.int4range:case ve.int8range:case ve.money:case ve.reltime:case ve.text:case ve.time:case ve.timestamptz:case ve.timetz:case ve.tsrange:case ve.tstzrange:return Al(s);default:return Al(s)}},Al=c=>c,Ep=c=>{switch(c){case"t":return!0;case"f":return!1;default:return c}},jp=c=>{if(typeof c=="string"){const s=parseFloat(c);if(!Number.isNaN(s))return s}return c},Cp=c=>{if(typeof c=="string")try{return JSON.parse(c)}catch(s){return console.log(`JSON parse error: ${s}`),c}return c},Pp=(c,s)=>{if(typeof c!="string")return c;const i=c.length-1,l=c[i];if(c[0]==="{"&&l==="}"){let d;const f=c.slice(1,i);try{d=JSON.parse("["+f+"]")}catch{d=f?f.split(","):[]}return d.map(m=>Nd(s,m))}return c},Np=c=>typeof c=="string"?c.replace(" ","T"):c,Td=c=>{let s=c;return s=s.replace(/^ws/i,"http"),s=s.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),s.replace(/\/+$/,"")};class Tl{constructor(s,i,l={},a=Cd){this.channel=s,this.event=i,this.payload=l,this.timeout=a,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(s){this.timeout=s,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(s){this.payload=Object.assign(Object.assign({},this.payload),s)}receive(s,i){var l;return this._hasReceived(s)&&i((l=this.receivedResp)===null||l===void 0?void 0:l.response),this.recHooks.push({status:s,callback:i}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const s=i=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=i,this._matchReceive(i)};this.channel._on(this.refEvent,{},s),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(s,i){this.refEvent&&this.channel._trigger(this.refEvent,{status:s,response:i})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:s,response:i}){this.recHooks.filter(l=>l.status===s).forEach(l=>l.callback(i))}_hasReceived(s){return this.receivedResp&&this.receivedResp.status===s}}var ad;(function(c){c.SYNC="sync",c.JOIN="join",c.LEAVE="leave"})(ad||(ad={}));class rs{constructor(s,i){this.channel=s,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const l=(i==null?void 0:i.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(l.state,{},a=>{const{onJoin:d,onLeave:f,onSync:m}=this.caller;this.joinRef=this.channel._joinRef(),this.state=rs.syncState(this.state,a,d,f),this.pendingDiffs.forEach(p=>{this.state=rs.syncDiff(this.state,p,d,f)}),this.pendingDiffs=[],m()}),this.channel._on(l.diff,{},a=>{const{onJoin:d,onLeave:f,onSync:m}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(a):(this.state=rs.syncDiff(this.state,a,d,f),m())}),this.onJoin((a,d,f)=>{this.channel._trigger("presence",{event:"join",key:a,currentPresences:d,newPresences:f})}),this.onLeave((a,d,f)=>{this.channel._trigger("presence",{event:"leave",key:a,currentPresences:d,leftPresences:f})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(s,i,l,a){const d=this.cloneDeep(s),f=this.transformState(i),m={},p={};return this.map(d,(g,y)=>{f[g]||(p[g]=y)}),this.map(f,(g,y)=>{const E=d[g];if(E){const P=y.map(D=>D.presence_ref),O=E.map(D=>D.presence_ref),I=y.filter(D=>O.indexOf(D.presence_ref)<0),F=E.filter(D=>P.indexOf(D.presence_ref)<0);I.length>0&&(m[g]=I),F.length>0&&(p[g]=F)}else m[g]=y}),this.syncDiff(d,{joins:m,leaves:p},l,a)}static syncDiff(s,i,l,a){const{joins:d,leaves:f}={joins:this.transformState(i.joins),leaves:this.transformState(i.leaves)};return l||(l=()=>{}),a||(a=()=>{}),this.map(d,(m,p)=>{var g;const y=(g=s[m])!==null&&g!==void 0?g:[];if(s[m]=this.cloneDeep(p),y.length>0){const E=s[m].map(O=>O.presence_ref),P=y.filter(O=>E.indexOf(O.presence_ref)<0);s[m].unshift(...P)}l(m,y,p)}),this.map(f,(m,p)=>{let g=s[m];if(!g)return;const y=p.map(E=>E.presence_ref);g=g.filter(E=>y.indexOf(E.presence_ref)<0),s[m]=g,a(m,g,p),g.length===0&&delete s[m]}),s}static map(s,i){return Object.getOwnPropertyNames(s).map(l=>i(l,s[l]))}static transformState(s){return s=this.cloneDeep(s),Object.getOwnPropertyNames(s).reduce((i,l)=>{const a=s[l];return"metas"in a?i[l]=a.metas.map(d=>(d.presence_ref=d.phx_ref,delete d.phx_ref,delete d.phx_ref_prev,d)):i[l]=a,i},{})}static cloneDeep(s){return JSON.parse(JSON.stringify(s))}onJoin(s){this.caller.onJoin=s}onLeave(s){this.caller.onLeave=s}onSync(s){this.caller.onSync=s}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var ud;(function(c){c.ALL="*",c.INSERT="INSERT",c.UPDATE="UPDATE",c.DELETE="DELETE"})(ud||(ud={}));var cd;(function(c){c.BROADCAST="broadcast",c.PRESENCE="presence",c.POSTGRES_CHANGES="postgres_changes",c.SYSTEM="system"})(cd||(cd={}));var Ft;(function(c){c.SUBSCRIBED="SUBSCRIBED",c.TIMED_OUT="TIMED_OUT",c.CLOSED="CLOSED",c.CHANNEL_ERROR="CHANNEL_ERROR"})(Ft||(Ft={}));class ql{constructor(s,i={config:{}},l){this.topic=s,this.params=i,this.socket=l,this.bindings={},this.state=ot.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=s.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},i.config),this.timeout=this.socket.timeout,this.joinPush=new Tl(this,Et.join,this.params,this.timeout),this.rejoinTimer=new Pd(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=ot.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(a=>a.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ot.closed,this.socket._remove(this)}),this._onError(a=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,a),this.state=ot.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ot.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Et.reply,{},(a,d)=>{this._trigger(this._replyEventName(d),a)}),this.presence=new rs(this),this.broadcastEndpointURL=Td(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(s,i=this.timeout){var l,a;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:d,presence:f,private:m}}=this.params;this._onError(y=>s==null?void 0:s(Ft.CHANNEL_ERROR,y)),this._onClose(()=>s==null?void 0:s(Ft.CLOSED));const p={},g={broadcast:d,presence:f,postgres_changes:(a=(l=this.bindings.postgres_changes)===null||l===void 0?void 0:l.map(y=>y.filter))!==null&&a!==void 0?a:[],private:m};this.socket.accessTokenValue&&(p.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:g},p)),this.joinedOnce=!0,this._rejoin(i),this.joinPush.receive("ok",async({postgres_changes:y})=>{var E;if(this.socket.setAuth(),y===void 0){s==null||s(Ft.SUBSCRIBED);return}else{const P=this.bindings.postgres_changes,O=(E=P==null?void 0:P.length)!==null&&E!==void 0?E:0,I=[];for(let F=0;F<O;F++){const D=P[F],{filter:{event:ne,schema:ae,table:U,filter:q}}=D,$=y&&y[F];if($&&$.event===ne&&$.schema===ae&&$.table===U&&$.filter===q)I.push(Object.assign(Object.assign({},D),{id:$.id}));else{this.unsubscribe(),s==null||s(Ft.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=I,s&&s(Ft.SUBSCRIBED);return}}).receive("error",y=>{s==null||s(Ft.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(y).join(", ")||"error")))}).receive("timeout",()=>{s==null||s(Ft.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(s,i={}){return await this.send({type:"presence",event:"track",payload:s},i.timeout||this.timeout)}async untrack(s={}){return await this.send({type:"presence",event:"untrack"},s)}on(s,i,l){return this._on(s,i,l)}async send(s,i={}){var l,a;if(!this._canPush()&&s.type==="broadcast"){const{event:d,payload:f}=s,p={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:d,payload:f,private:this.private}]})};try{const g=await this._fetchWithTimeout(this.broadcastEndpointURL,p,(l=i.timeout)!==null&&l!==void 0?l:this.timeout);return await((a=g.body)===null||a===void 0?void 0:a.cancel()),g.ok?"ok":"error"}catch(g){return g.name==="AbortError"?"timed out":"error"}}else return new Promise(d=>{var f,m,p;const g=this._push(s.type,s,i.timeout||this.timeout);s.type==="broadcast"&&!(!((p=(m=(f=this.params)===null||f===void 0?void 0:f.config)===null||m===void 0?void 0:m.broadcast)===null||p===void 0)&&p.ack)&&d("ok"),g.receive("ok",()=>d("ok")),g.receive("error",()=>d("error")),g.receive("timeout",()=>d("timed out"))})}updateJoinPayload(s){this.joinPush.updatePayload(s)}unsubscribe(s=this.timeout){this.state=ot.leaving;const i=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Et.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(l=>{const a=new Tl(this,Et.leave,{},s);a.receive("ok",()=>{i(),l("ok")}).receive("timeout",()=>{i(),l("timed out")}).receive("error",()=>{l("error")}),a.send(),this._canPush()||a.trigger("ok",{})})}async _fetchWithTimeout(s,i,l){const a=new AbortController,d=setTimeout(()=>a.abort(),l),f=await this.socket.fetch(s,Object.assign(Object.assign({},i),{signal:a.signal}));return clearTimeout(d),f}_push(s,i,l=this.timeout){if(!this.joinedOnce)throw`tried to push '${s}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let a=new Tl(this,s,i,l);return this._canPush()?a.send():(a.startTimeout(),this.pushBuffer.push(a)),a}_onMessage(s,i,l){return i}_isMember(s){return this.topic===s}_joinRef(){return this.joinPush.ref}_trigger(s,i,l){var a,d;const f=s.toLocaleLowerCase(),{close:m,error:p,leave:g,join:y}=Et;if(l&&[m,p,g,y].indexOf(f)>=0&&l!==this._joinRef())return;let P=this._onMessage(f,i,l);if(i&&!P)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(f)?(a=this.bindings.postgres_changes)===null||a===void 0||a.filter(O=>{var I,F,D;return((I=O.filter)===null||I===void 0?void 0:I.event)==="*"||((D=(F=O.filter)===null||F===void 0?void 0:F.event)===null||D===void 0?void 0:D.toLocaleLowerCase())===f}).map(O=>O.callback(P,l)):(d=this.bindings[f])===null||d===void 0||d.filter(O=>{var I,F,D,ne,ae,U;if(["broadcast","presence","postgres_changes"].includes(f))if("id"in O){const q=O.id,$=(I=O.filter)===null||I===void 0?void 0:I.event;return q&&((F=i.ids)===null||F===void 0?void 0:F.includes(q))&&($==="*"||($==null?void 0:$.toLocaleLowerCase())===((D=i.data)===null||D===void 0?void 0:D.type.toLocaleLowerCase()))}else{const q=(ae=(ne=O==null?void 0:O.filter)===null||ne===void 0?void 0:ne.event)===null||ae===void 0?void 0:ae.toLocaleLowerCase();return q==="*"||q===((U=i==null?void 0:i.event)===null||U===void 0?void 0:U.toLocaleLowerCase())}else return O.type.toLocaleLowerCase()===f}).map(O=>{if(typeof P=="object"&&"ids"in P){const I=P.data,{schema:F,table:D,commit_timestamp:ne,type:ae,errors:U}=I;P=Object.assign(Object.assign({},{schema:F,table:D,commit_timestamp:ne,eventType:ae,new:{},old:{},errors:U}),this._getPayloadRecords(I))}O.callback(P,l)})}_isClosed(){return this.state===ot.closed}_isJoined(){return this.state===ot.joined}_isJoining(){return this.state===ot.joining}_isLeaving(){return this.state===ot.leaving}_replyEventName(s){return`chan_reply_${s}`}_on(s,i,l){const a=s.toLocaleLowerCase(),d={type:a,filter:i,callback:l};return this.bindings[a]?this.bindings[a].push(d):this.bindings[a]=[d],this}_off(s,i){const l=s.toLocaleLowerCase();return this.bindings[l]=this.bindings[l].filter(a=>{var d;return!(((d=a.type)===null||d===void 0?void 0:d.toLocaleLowerCase())===l&&ql.isEqual(a.filter,i))}),this}static isEqual(s,i){if(Object.keys(s).length!==Object.keys(i).length)return!1;for(const l in s)if(s[l]!==i[l])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(s){this._on(Et.close,{},s)}_onError(s){this._on(Et.error,{},i=>s(i))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(s=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ot.joining,this.joinPush.resend(s))}_getPayloadRecords(s){const i={new:{},old:{}};return(s.type==="INSERT"||s.type==="UPDATE")&&(i.new=ld(s.columns,s.record)),(s.type==="UPDATE"||s.type==="DELETE")&&(i.old=ld(s.columns,s.old_record)),i}}const Tp=()=>{},Rp=typeof WebSocket<"u",bp=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Op{constructor(s,i){var l;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=wp,this.params={},this.timeout=Cd,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Tp,this.conn=null,this.sendBuffer=[],this.serializer=new kp,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=d=>{let f;return d?f=d:typeof fetch>"u"?f=(...m)=>dr(async()=>{const{default:p}=await Promise.resolve().then(()=>hr);return{default:p}},void 0).then(({default:p})=>p(...m)):f=fetch,(...m)=>f(...m)},this.endPoint=`${s}/${Il.websocket}`,this.httpEndpoint=Td(s),i!=null&&i.transport?this.transport=i.transport:this.transport=null,i!=null&&i.params&&(this.params=i.params),i!=null&&i.headers&&(this.headers=Object.assign(Object.assign({},this.headers),i.headers)),i!=null&&i.timeout&&(this.timeout=i.timeout),i!=null&&i.logger&&(this.logger=i.logger),i!=null&&i.heartbeatIntervalMs&&(this.heartbeatIntervalMs=i.heartbeatIntervalMs);const a=(l=i==null?void 0:i.params)===null||l===void 0?void 0:l.apikey;if(a&&(this.accessTokenValue=a,this.apiKey=a),this.reconnectAfterMs=i!=null&&i.reconnectAfterMs?i.reconnectAfterMs:d=>[1e3,2e3,5e3,1e4][d-1]||1e4,this.encode=i!=null&&i.encode?i.encode:(d,f)=>f(JSON.stringify(d)),this.decode=i!=null&&i.decode?i.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Pd(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(i==null?void 0:i.fetch),i!=null&&i.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(i==null?void 0:i.worker)||!1,this.workerUrl=i==null?void 0:i.workerUrl}this.accessToken=(i==null?void 0:i.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if(Rp){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new $p(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),dr(async()=>{const{default:s}=await import("./browser-BB6g2RXg.js").then(i=>i.b);return{default:s}},[]).then(({default:s})=>{this.conn=new s(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:_p}))}disconnect(s,i){this.conn&&(this.conn.onclose=function(){},s?this.conn.close(s,i??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(s){const i=await s.unsubscribe();return this.channels.length===0&&this.disconnect(),i}async removeAllChannels(){const s=await Promise.all(this.channels.map(i=>i.unsubscribe()));return this.disconnect(),s}log(s,i,l){this.logger(s,i,l)}connectionState(){switch(this.conn&&this.conn.readyState){case cr.connecting:return Pn.Connecting;case cr.open:return Pn.Open;case cr.closing:return Pn.Closing;default:return Pn.Closed}}isConnected(){return this.connectionState()===Pn.Open}channel(s,i={config:{}}){const l=new ql(`realtime:${s}`,i,this);return this.channels.push(l),l}push(s){const{topic:i,event:l,payload:a,ref:d}=s,f=()=>{this.encode(s,m=>{var p;(p=this.conn)===null||p===void 0||p.send(m)})};this.log("push",`${i} ${l} (${d})`,a),this.isConnected()?f():this.sendBuffer.push(f)}async setAuth(s=null){let i=s||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(i){let l=null;try{l=JSON.parse(atob(i.split(".")[1]))}catch{}if(l&&l.exp&&!(Math.floor(Date.now()/1e3)-l.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${l.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${l.exp}`);this.accessTokenValue=i,this.channels.forEach(a=>{i&&a.updateJoinPayload({access_token:i}),a.joinedOnce&&a._isJoined()&&a._push(Et.access_token,{access_token:i})})}}async sendHeartbeat(){var s;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),(s=this.conn)===null||s===void 0||s.close(xp,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(s=>s()),this.sendBuffer=[])}_makeRef(){let s=this.ref+1;return s===this.ref?this.ref=0:this.ref=s,this.ref.toString()}_leaveOpenTopic(s){let i=this.channels.find(l=>l.topic===s&&(l._isJoined()||l._isJoining()));i&&(this.log("transport",`leaving duplicate topic "${s}"`),i.unsubscribe())}_remove(s){this.channels=this.channels.filter(i=>i._joinRef()!==s._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=s=>this._onConnError(s),this.conn.onmessage=s=>this._onConnMessage(s),this.conn.onclose=s=>this._onConnClose(s))}_onConnMessage(s){this.decode(s.data,i=>{let{topic:l,event:a,payload:d,ref:f}=i;f&&f===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${d.status||""} ${l} ${a} ${f&&"("+f+")"||""}`,d),this.channels.filter(m=>m._isMember(l)).forEach(m=>m._trigger(a,d,f)),this.stateChangeCallbacks.message.forEach(m=>m(i))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),!this.worker)this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);else{this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const s=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(s),this.workerRef.onerror=i=>{this.log("worker","worker error",i.message),this.workerRef.terminate()},this.workerRef.onmessage=i=>{i.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}this.stateChangeCallbacks.open.forEach(s=>s())}_onConnClose(s){this.log("transport","close",s),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(i=>i(s))}_onConnError(s){this.log("transport",s.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(i=>i(s))}_triggerChanError(){this.channels.forEach(s=>s._trigger(Et.error))}_appendParams(s,i){if(Object.keys(i).length===0)return s;const l=s.match(/\?/)?"&":"?",a=new URLSearchParams(i);return`${s}${l}${a}`}_workerObjectUrl(s){let i;if(s)i=s;else{const l=new Blob([bp],{type:"application/javascript"});i=URL.createObjectURL(l)}return i}}class $p{constructor(s,i,l){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=cr.connecting,this.send=()=>{},this.url=null,this.url=s,this.close=l.close}}class Wl extends Error{constructor(s){super(s),this.__isStorageError=!0,this.name="StorageError"}}function Ae(c){return typeof c=="object"&&c!==null&&"__isStorageError"in c}class Lp extends Wl{constructor(s,i){super(s),this.name="StorageApiError",this.status=i}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Dl extends Wl{constructor(s,i){super(s),this.name="StorageUnknownError",this.originalError=i}}var Ip=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};const Rd=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>dr(async()=>{const{default:l}=await Promise.resolve().then(()=>hr);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)},Ap=()=>Ip(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield dr(()=>Promise.resolve().then(()=>hr),void 0)).Response:Response}),zl=c=>{if(Array.isArray(c))return c.map(i=>zl(i));if(typeof c=="function"||c!==Object(c))return c;const s={};return Object.entries(c).forEach(([i,l])=>{const a=i.replace(/([-_][a-z])/gi,d=>d.toUpperCase().replace(/[-_]/g,""));s[a]=zl(l)}),s};var Nn=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};const Rl=c=>c.msg||c.message||c.error_description||c.error||JSON.stringify(c),Dp=(c,s,i)=>Nn(void 0,void 0,void 0,function*(){const l=yield Ap();c instanceof l&&!(i!=null&&i.noResolveJson)?c.json().then(a=>{s(new Lp(Rl(a),c.status||500))}).catch(a=>{s(new Dl(Rl(a),a))}):s(new Dl(Rl(c),c))}),zp=(c,s,i,l)=>{const a={method:c,headers:(s==null?void 0:s.headers)||{}};return c==="GET"?a:(a.headers=Object.assign({"Content-Type":"application/json"},s==null?void 0:s.headers),l&&(a.body=JSON.stringify(l)),Object.assign(Object.assign({},a),i))};function os(c,s,i,l,a,d){return Nn(this,void 0,void 0,function*(){return new Promise((f,m)=>{c(i,zp(s,l,a,d)).then(p=>{if(!p.ok)throw p;return l!=null&&l.noResolveJson?p:p.json()}).then(p=>f(p)).catch(p=>Dp(p,m,l))})})}function ji(c,s,i,l){return Nn(this,void 0,void 0,function*(){return os(c,"GET",s,i,l)})}function cn(c,s,i,l,a){return Nn(this,void 0,void 0,function*(){return os(c,"POST",s,l,a,i)})}function Up(c,s,i,l,a){return Nn(this,void 0,void 0,function*(){return os(c,"PUT",s,l,a,i)})}function Mp(c,s,i,l){return Nn(this,void 0,void 0,function*(){return os(c,"HEAD",s,Object.assign(Object.assign({},i),{noResolveJson:!0}),l)})}function bd(c,s,i,l,a){return Nn(this,void 0,void 0,function*(){return os(c,"DELETE",s,l,a,i)})}var Ze=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};const Fp={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},dd={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Vp{constructor(s,i={},l,a){this.url=s,this.headers=i,this.bucketId=l,this.fetch=Rd(a)}uploadOrUpdate(s,i,l,a){return Ze(this,void 0,void 0,function*(){try{let d;const f=Object.assign(Object.assign({},dd),a);let m=Object.assign(Object.assign({},this.headers),s==="POST"&&{"x-upsert":String(f.upsert)});const p=f.metadata;typeof Blob<"u"&&l instanceof Blob?(d=new FormData,d.append("cacheControl",f.cacheControl),p&&d.append("metadata",this.encodeMetadata(p)),d.append("",l)):typeof FormData<"u"&&l instanceof FormData?(d=l,d.append("cacheControl",f.cacheControl),p&&d.append("metadata",this.encodeMetadata(p))):(d=l,m["cache-control"]=`max-age=${f.cacheControl}`,m["content-type"]=f.contentType,p&&(m["x-metadata"]=this.toBase64(this.encodeMetadata(p)))),a!=null&&a.headers&&(m=Object.assign(Object.assign({},m),a.headers));const g=this._removeEmptyFolders(i),y=this._getFinalPath(g),E=yield this.fetch(`${this.url}/object/${y}`,Object.assign({method:s,body:d,headers:m},f!=null&&f.duplex?{duplex:f.duplex}:{})),P=yield E.json();return E.ok?{data:{path:g,id:P.Id,fullPath:P.Key},error:null}:{data:null,error:P}}catch(d){if(Ae(d))return{data:null,error:d};throw d}})}upload(s,i,l){return Ze(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",s,i,l)})}uploadToSignedUrl(s,i,l,a){return Ze(this,void 0,void 0,function*(){const d=this._removeEmptyFolders(s),f=this._getFinalPath(d),m=new URL(this.url+`/object/upload/sign/${f}`);m.searchParams.set("token",i);try{let p;const g=Object.assign({upsert:dd.upsert},a),y=Object.assign(Object.assign({},this.headers),{"x-upsert":String(g.upsert)});typeof Blob<"u"&&l instanceof Blob?(p=new FormData,p.append("cacheControl",g.cacheControl),p.append("",l)):typeof FormData<"u"&&l instanceof FormData?(p=l,p.append("cacheControl",g.cacheControl)):(p=l,y["cache-control"]=`max-age=${g.cacheControl}`,y["content-type"]=g.contentType);const E=yield this.fetch(m.toString(),{method:"PUT",body:p,headers:y}),P=yield E.json();return E.ok?{data:{path:d,fullPath:P.Key},error:null}:{data:null,error:P}}catch(p){if(Ae(p))return{data:null,error:p};throw p}})}createSignedUploadUrl(s,i){return Ze(this,void 0,void 0,function*(){try{let l=this._getFinalPath(s);const a=Object.assign({},this.headers);i!=null&&i.upsert&&(a["x-upsert"]="true");const d=yield cn(this.fetch,`${this.url}/object/upload/sign/${l}`,{},{headers:a}),f=new URL(this.url+d.url),m=f.searchParams.get("token");if(!m)throw new Wl("No token returned by API");return{data:{signedUrl:f.toString(),path:s,token:m},error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}update(s,i,l){return Ze(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",s,i,l)})}move(s,i,l){return Ze(this,void 0,void 0,function*(){try{return{data:yield cn(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:s,destinationKey:i,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers}),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}copy(s,i,l){return Ze(this,void 0,void 0,function*(){try{return{data:{path:(yield cn(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:s,destinationKey:i,destinationBucket:l==null?void 0:l.destinationBucket},{headers:this.headers})).Key},error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrl(s,i,l){return Ze(this,void 0,void 0,function*(){try{let a=this._getFinalPath(s),d=yield cn(this.fetch,`${this.url}/object/sign/${a}`,Object.assign({expiresIn:i},l!=null&&l.transform?{transform:l.transform}:{}),{headers:this.headers});const f=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return d={signedUrl:encodeURI(`${this.url}${d.signedURL}${f}`)},{data:d,error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}createSignedUrls(s,i,l){return Ze(this,void 0,void 0,function*(){try{const a=yield cn(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:i,paths:s},{headers:this.headers}),d=l!=null&&l.download?`&download=${l.download===!0?"":l.download}`:"";return{data:a.map(f=>Object.assign(Object.assign({},f),{signedUrl:f.signedURL?encodeURI(`${this.url}${f.signedURL}${d}`):null})),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}download(s,i){return Ze(this,void 0,void 0,function*(){const a=typeof(i==null?void 0:i.transform)<"u"?"render/image/authenticated":"object",d=this.transformOptsToQueryString((i==null?void 0:i.transform)||{}),f=d?`?${d}`:"";try{const m=this._getFinalPath(s);return{data:yield(yield ji(this.fetch,`${this.url}/${a}/${m}${f}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(m){if(Ae(m))return{data:null,error:m};throw m}})}info(s){return Ze(this,void 0,void 0,function*(){const i=this._getFinalPath(s);try{const l=yield ji(this.fetch,`${this.url}/object/info/${i}`,{headers:this.headers});return{data:zl(l),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}exists(s){return Ze(this,void 0,void 0,function*(){const i=this._getFinalPath(s);try{return yield Mp(this.fetch,`${this.url}/object/${i}`,{headers:this.headers}),{data:!0,error:null}}catch(l){if(Ae(l)&&l instanceof Dl){const a=l.originalError;if([400,404].includes(a==null?void 0:a.status))return{data:!1,error:l}}throw l}})}getPublicUrl(s,i){const l=this._getFinalPath(s),a=[],d=i!=null&&i.download?`download=${i.download===!0?"":i.download}`:"";d!==""&&a.push(d);const m=typeof(i==null?void 0:i.transform)<"u"?"render/image":"object",p=this.transformOptsToQueryString((i==null?void 0:i.transform)||{});p!==""&&a.push(p);let g=a.join("&");return g!==""&&(g=`?${g}`),{data:{publicUrl:encodeURI(`${this.url}/${m}/public/${l}${g}`)}}}remove(s){return Ze(this,void 0,void 0,function*(){try{return{data:yield bd(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:s},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}list(s,i,l){return Ze(this,void 0,void 0,function*(){try{const a=Object.assign(Object.assign(Object.assign({},Fp),i),{prefix:s||""});return{data:yield cn(this.fetch,`${this.url}/object/list/${this.bucketId}`,a,{headers:this.headers},l),error:null}}catch(a){if(Ae(a))return{data:null,error:a};throw a}})}encodeMetadata(s){return JSON.stringify(s)}toBase64(s){return typeof Buffer<"u"?Buffer.from(s).toString("base64"):btoa(s)}_getFinalPath(s){return`${this.bucketId}/${s}`}_removeEmptyFolders(s){return s.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(s){const i=[];return s.width&&i.push(`width=${s.width}`),s.height&&i.push(`height=${s.height}`),s.resize&&i.push(`resize=${s.resize}`),s.format&&i.push(`format=${s.format}`),s.quality&&i.push(`quality=${s.quality}`),i.join("&")}}const Bp="2.7.1",Hp={"X-Client-Info":`storage-js/${Bp}`};var or=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};class qp{constructor(s,i={},l){this.url=s,this.headers=Object.assign(Object.assign({},Hp),i),this.fetch=Rd(l)}listBuckets(){return or(this,void 0,void 0,function*(){try{return{data:yield ji(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(s){if(Ae(s))return{data:null,error:s};throw s}})}getBucket(s){return or(this,void 0,void 0,function*(){try{return{data:yield ji(this.fetch,`${this.url}/bucket/${s}`,{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}createBucket(s,i={public:!1}){return or(this,void 0,void 0,function*(){try{return{data:yield cn(this.fetch,`${this.url}/bucket`,{id:s,name:s,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}updateBucket(s,i){return or(this,void 0,void 0,function*(){try{return{data:yield Up(this.fetch,`${this.url}/bucket/${s}`,{id:s,name:s,public:i.public,file_size_limit:i.fileSizeLimit,allowed_mime_types:i.allowedMimeTypes},{headers:this.headers}),error:null}}catch(l){if(Ae(l))return{data:null,error:l};throw l}})}emptyBucket(s){return or(this,void 0,void 0,function*(){try{return{data:yield cn(this.fetch,`${this.url}/bucket/${s}/empty`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}deleteBucket(s){return or(this,void 0,void 0,function*(){try{return{data:yield bd(this.fetch,`${this.url}/bucket/${s}`,{},{headers:this.headers}),error:null}}catch(i){if(Ae(i))return{data:null,error:i};throw i}})}}class Wp extends qp{constructor(s,i={},l){super(s,i,l)}from(s){return new Vp(this.url,this.headers,s,this.fetch)}}const Gp="2.49.1";let ns="";typeof Deno<"u"?ns="deno":typeof document<"u"?ns="web":typeof navigator<"u"&&navigator.product==="ReactNative"?ns="react-native":ns="node";const Qp={"X-Client-Info":`supabase-js-${ns}/${Gp}`},Kp={headers:Qp},Jp={schema:"public"},Yp={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},Xp={};var Zp=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};const em=c=>{let s;return c?s=c:typeof fetch>"u"?s=_d:s=fetch,(...i)=>s(...i)},tm=()=>typeof Headers>"u"?xd:Headers,nm=(c,s,i)=>{const l=em(i),a=tm();return(d,f)=>Zp(void 0,void 0,void 0,function*(){var m;const p=(m=yield s())!==null&&m!==void 0?m:c;let g=new a(f==null?void 0:f.headers);return g.has("apikey")||g.set("apikey",c),g.has("Authorization")||g.set("Authorization",`Bearer ${p}`),l(d,Object.assign(Object.assign({},f),{headers:g}))})};var rm=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};function sm(c){return c.replace(/\/$/,"")}function im(c,s){const{db:i,auth:l,realtime:a,global:d}=c,{db:f,auth:m,realtime:p,global:g}=s,y={db:Object.assign(Object.assign({},f),i),auth:Object.assign(Object.assign({},m),l),realtime:Object.assign(Object.assign({},p),a),global:Object.assign(Object.assign({},g),d),accessToken:()=>rm(this,void 0,void 0,function*(){return""})};return c.accessToken?y.accessToken=c.accessToken:delete y.accessToken,y}const Od="2.68.0",ur=30*1e3,Ul=3,bl=Ul*ur,om="http://localhost:9999",lm="supabase.auth.token",am={"X-Client-Info":`gotrue-js/${Od}`},Ml="X-Supabase-Api-Version",$d={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}};function um(c){return Math.round(Date.now()/1e3)+c}function cm(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(c){const s=Math.random()*16|0;return(c=="x"?s:s&3|8).toString(16)})}const Ot=()=>typeof window<"u"&&typeof document<"u",jn={tested:!1,writable:!1},ss=()=>{if(!Ot())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(jn.tested)return jn.writable;const c=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(c,c),globalThis.localStorage.removeItem(c),jn.tested=!0,jn.writable=!0}catch{jn.tested=!0,jn.writable=!1}return jn.writable};function dm(c){const s={},i=new URL(c);if(i.hash&&i.hash[0]==="#")try{new URLSearchParams(i.hash.substring(1)).forEach((a,d)=>{s[d]=a})}catch{}return i.searchParams.forEach((l,a)=>{s[a]=l}),s}const Ld=c=>{let s;return c?s=c:typeof fetch>"u"?s=(...i)=>dr(async()=>{const{default:l}=await Promise.resolve().then(()=>hr);return{default:l}},void 0).then(({default:l})=>l(...i)):s=fetch,(...i)=>s(...i)},fm=c=>typeof c=="object"&&c!==null&&"status"in c&&"ok"in c&&"json"in c&&typeof c.json=="function",Id=async(c,s,i)=>{await c.setItem(s,JSON.stringify(i))},wi=async(c,s)=>{const i=await c.getItem(s);if(!i)return null;try{return JSON.parse(i)}catch{return i}},_i=async(c,s)=>{await c.removeItem(s)};function hm(c){const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let i="",l,a,d,f,m,p,g,y=0;for(c=c.replace("-","+").replace("_","/");y<c.length;)f=s.indexOf(c.charAt(y++)),m=s.indexOf(c.charAt(y++)),p=s.indexOf(c.charAt(y++)),g=s.indexOf(c.charAt(y++)),l=f<<2|m>>4,a=(m&15)<<4|p>>2,d=(p&3)<<6|g,i=i+String.fromCharCode(l),p!=64&&a!=0&&(i=i+String.fromCharCode(a)),g!=64&&d!=0&&(i=i+String.fromCharCode(d));return i}class Ti{constructor(){this.promise=new Ti.promiseConstructor((s,i)=>{this.resolve=s,this.reject=i})}}Ti.promiseConstructor=Promise;function fd(c){const s=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}=?$|[a-z0-9_-]{2}(==)?$)$/i,i=c.split(".");if(i.length!==3)throw new Error("JWT is not valid: not a JWT structure");if(!s.test(i[1]))throw new Error("JWT is not valid: payload is not in base64url format");const l=i[1];return JSON.parse(hm(l))}async function pm(c){return await new Promise(s=>{setTimeout(()=>s(null),c)})}function mm(c,s){return new Promise((l,a)=>{(async()=>{for(let d=0;d<1/0;d++)try{const f=await c(d);if(!s(d,null,f)){l(f);return}}catch(f){if(!s(d,f)){a(f);return}}})()})}function gm(c){return("0"+c.toString(16)).substr(-2)}function vm(){const s=new Uint32Array(56);if(typeof crypto>"u"){const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",l=i.length;let a="";for(let d=0;d<56;d++)a+=i.charAt(Math.floor(Math.random()*l));return a}return crypto.getRandomValues(s),Array.from(s,gm).join("")}async function ym(c){const i=new TextEncoder().encode(c),l=await crypto.subtle.digest("SHA-256",i),a=new Uint8Array(l);return Array.from(a).map(d=>String.fromCharCode(d)).join("")}function wm(c){return btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function _m(c){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),c;const i=await ym(c);return wm(i)}async function lr(c,s,i=!1){const l=vm();let a=l;i&&(a+="/PASSWORD_RECOVERY"),await Id(c,`${s}-code-verifier`,a);const d=await _m(l);return[d,l===d?"plain":"s256"]}const xm=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function km(c){const s=c.headers.get(Ml);if(!s||!s.match(xm))return null;try{return new Date(`${s}T00:00:00.0Z`)}catch{return null}}class Gl extends Error{constructor(s,i,l){super(s),this.__isAuthError=!0,this.name="AuthError",this.status=i,this.code=l}}function Z(c){return typeof c=="object"&&c!==null&&"__isAuthError"in c}class Sm extends Gl{constructor(s,i,l){super(s,i,l),this.name="AuthApiError",this.status=i,this.code=l}}function Em(c){return Z(c)&&c.name==="AuthApiError"}class Ad extends Gl{constructor(s,i){super(s),this.name="AuthUnknownError",this.originalError=i}}class Tn extends Gl{constructor(s,i,l,a){super(s,l,a),this.name=i,this.status=l}}class an extends Tn{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function jm(c){return Z(c)&&c.name==="AuthSessionMissingError"}class Ol extends Tn{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class xi extends Tn{constructor(s){super(s,"AuthInvalidCredentialsError",400,void 0)}}class ki extends Tn{constructor(s,i=null){super(s,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Cm(c){return Z(c)&&c.name==="AuthImplicitGrantRedirectError"}class hd extends Tn{constructor(s,i=null){super(s,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=i}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Fl extends Tn{constructor(s,i){super(s,"AuthRetryableFetchError",i,void 0)}}function $l(c){return Z(c)&&c.name==="AuthRetryableFetchError"}class pd extends Tn{constructor(s,i,l){super(s,"AuthWeakPasswordError",i,"weak_password"),this.reasons=l}}var Pm=function(c,s){var i={};for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&s.indexOf(l)<0&&(i[l]=c[l]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(c);a<l.length;a++)s.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(c,l[a])&&(i[l[a]]=c[l[a]]);return i};const Cn=c=>c.msg||c.message||c.error_description||c.error||JSON.stringify(c),Nm=[502,503,504];async function md(c){var s;if(!fm(c))throw new Fl(Cn(c),0);if(Nm.includes(c.status))throw new Fl(Cn(c),c.status);let i;try{i=await c.json()}catch(d){throw new Ad(Cn(d),d)}let l;const a=km(c);if(a&&a.getTime()>=$d["2024-01-01"].timestamp&&typeof i=="object"&&i&&typeof i.code=="string"?l=i.code:typeof i=="object"&&i&&typeof i.error_code=="string"&&(l=i.error_code),l){if(l==="weak_password")throw new pd(Cn(i),c.status,((s=i.weak_password)===null||s===void 0?void 0:s.reasons)||[]);if(l==="session_not_found")throw new an}else if(typeof i=="object"&&i&&typeof i.weak_password=="object"&&i.weak_password&&Array.isArray(i.weak_password.reasons)&&i.weak_password.reasons.length&&i.weak_password.reasons.reduce((d,f)=>d&&typeof f=="string",!0))throw new pd(Cn(i),c.status,i.weak_password.reasons);throw new Sm(Cn(i),c.status||500,l)}const Tm=(c,s,i,l)=>{const a={method:c,headers:(s==null?void 0:s.headers)||{}};return c==="GET"?a:(a.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},s==null?void 0:s.headers),a.body=JSON.stringify(l),Object.assign(Object.assign({},a),i))};async function ie(c,s,i,l){var a;const d=Object.assign({},l==null?void 0:l.headers);d[Ml]||(d[Ml]=$d["2024-01-01"].name),l!=null&&l.jwt&&(d.Authorization=`Bearer ${l.jwt}`);const f=(a=l==null?void 0:l.query)!==null&&a!==void 0?a:{};l!=null&&l.redirectTo&&(f.redirect_to=l.redirectTo);const m=Object.keys(f).length?"?"+new URLSearchParams(f).toString():"",p=await Rm(c,s,i+m,{headers:d,noResolveJson:l==null?void 0:l.noResolveJson},{},l==null?void 0:l.body);return l!=null&&l.xform?l==null?void 0:l.xform(p):{data:Object.assign({},p),error:null}}async function Rm(c,s,i,l,a,d){const f=Tm(s,l,a,d);let m;try{m=await c(i,Object.assign({},f))}catch(p){throw console.error(p),new Fl(Cn(p),0)}if(m.ok||await md(m),l!=null&&l.noResolveJson)return m;try{return await m.json()}catch(p){await md(p)}}function un(c){var s;let i=null;Lm(c)&&(i=Object.assign({},c),c.expires_at||(i.expires_at=um(c.expires_in)));const l=(s=c.user)!==null&&s!==void 0?s:c;return{data:{session:i,user:l},error:null}}function gd(c){const s=un(c);return!s.error&&c.weak_password&&typeof c.weak_password=="object"&&Array.isArray(c.weak_password.reasons)&&c.weak_password.reasons.length&&c.weak_password.message&&typeof c.weak_password.message=="string"&&c.weak_password.reasons.reduce((i,l)=>i&&typeof l=="string",!0)&&(s.data.weak_password=c.weak_password),s}function dn(c){var s;return{data:{user:(s=c.user)!==null&&s!==void 0?s:c},error:null}}function bm(c){return{data:c,error:null}}function Om(c){const{action_link:s,email_otp:i,hashed_token:l,redirect_to:a,verification_type:d}=c,f=Pm(c,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),m={action_link:s,email_otp:i,hashed_token:l,redirect_to:a,verification_type:d},p=Object.assign({},f);return{data:{properties:m,user:p},error:null}}function $m(c){return c}function Lm(c){return c.access_token&&c.refresh_token&&c.expires_in}var Im=function(c,s){var i={};for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&s.indexOf(l)<0&&(i[l]=c[l]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,l=Object.getOwnPropertySymbols(c);a<l.length;a++)s.indexOf(l[a])<0&&Object.prototype.propertyIsEnumerable.call(c,l[a])&&(i[l[a]]=c[l[a]]);return i};class Am{constructor({url:s="",headers:i={},fetch:l}){this.url=s,this.headers=i,this.fetch=Ld(l),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(s,i="global"){try{return await ie(this.fetch,"POST",`${this.url}/logout?scope=${i}`,{headers:this.headers,jwt:s,noResolveJson:!0}),{data:null,error:null}}catch(l){if(Z(l))return{data:null,error:l};throw l}}async inviteUserByEmail(s,i={}){try{return await ie(this.fetch,"POST",`${this.url}/invite`,{body:{email:s,data:i.data},headers:this.headers,redirectTo:i.redirectTo,xform:dn})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async generateLink(s){try{const{options:i}=s,l=Im(s,["options"]),a=Object.assign(Object.assign({},l),i);return"newEmail"in l&&(a.new_email=l==null?void 0:l.newEmail,delete a.newEmail),await ie(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:a,headers:this.headers,xform:Om,redirectTo:i==null?void 0:i.redirectTo})}catch(i){if(Z(i))return{data:{properties:null,user:null},error:i};throw i}}async createUser(s){try{return await ie(this.fetch,"POST",`${this.url}/admin/users`,{body:s,headers:this.headers,xform:dn})}catch(i){if(Z(i))return{data:{user:null},error:i};throw i}}async listUsers(s){var i,l,a,d,f,m,p;try{const g={nextPage:null,lastPage:0,total:0},y=await ie(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(l=(i=s==null?void 0:s.page)===null||i===void 0?void 0:i.toString())!==null&&l!==void 0?l:"",per_page:(d=(a=s==null?void 0:s.perPage)===null||a===void 0?void 0:a.toString())!==null&&d!==void 0?d:""},xform:$m});if(y.error)throw y.error;const E=await y.json(),P=(f=y.headers.get("x-total-count"))!==null&&f!==void 0?f:0,O=(p=(m=y.headers.get("link"))===null||m===void 0?void 0:m.split(","))!==null&&p!==void 0?p:[];return O.length>0&&(O.forEach(I=>{const F=parseInt(I.split(";")[0].split("=")[1].substring(0,1)),D=JSON.parse(I.split(";")[1].split("=")[1]);g[`${D}Page`]=F}),g.total=parseInt(P)),{data:Object.assign(Object.assign({},E),g),error:null}}catch(g){if(Z(g))return{data:{users:[]},error:g};throw g}}async getUserById(s){try{return await ie(this.fetch,"GET",`${this.url}/admin/users/${s}`,{headers:this.headers,xform:dn})}catch(i){if(Z(i))return{data:{user:null},error:i};throw i}}async updateUserById(s,i){try{return await ie(this.fetch,"PUT",`${this.url}/admin/users/${s}`,{body:i,headers:this.headers,xform:dn})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async deleteUser(s,i=!1){try{return await ie(this.fetch,"DELETE",`${this.url}/admin/users/${s}`,{headers:this.headers,body:{should_soft_delete:i},xform:dn})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}async _listFactors(s){try{const{data:i,error:l}=await ie(this.fetch,"GET",`${this.url}/admin/users/${s.userId}/factors`,{headers:this.headers,xform:a=>({data:{factors:a},error:null})});return{data:i,error:l}}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _deleteFactor(s){try{return{data:await ie(this.fetch,"DELETE",`${this.url}/admin/users/${s.userId}/factors/${s.id}`,{headers:this.headers}),error:null}}catch(i){if(Z(i))return{data:null,error:i};throw i}}}const Dm={getItem:c=>ss()?globalThis.localStorage.getItem(c):null,setItem:(c,s)=>{ss()&&globalThis.localStorage.setItem(c,s)},removeItem:c=>{ss()&&globalThis.localStorage.removeItem(c)}};function vd(c={}){return{getItem:s=>c[s]||null,setItem:(s,i)=>{c[s]=i},removeItem:s=>{delete c[s]}}}function zm(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const ar={debug:!!(globalThis&&ss()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Dd extends Error{constructor(s){super(s),this.isAcquireTimeout=!0}}class Um extends Dd{}async function Mm(c,s,i){ar.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",c,s);const l=new globalThis.AbortController;return s>0&&setTimeout(()=>{l.abort(),ar.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",c)},s),await Promise.resolve().then(()=>globalThis.navigator.locks.request(c,s===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:l.signal},async a=>{if(a){ar.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",c,a.name);try{return await i()}finally{ar.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",c,a.name)}}else{if(s===0)throw ar.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",c),new Um(`Acquiring an exclusive Navigator LockManager lock "${c}" immediately failed`);if(ar.debug)try{const d=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(d,null,"  "))}catch(d){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",d)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await i()}}))}zm();const Fm={url:om,storageKey:lm,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:am,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function yd(c,s,i){return await i()}class is{constructor(s){var i,l;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=is.nextInstanceID,is.nextInstanceID+=1,this.instanceID>0&&Ot()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const a=Object.assign(Object.assign({},Fm),s);if(this.logDebugMessages=!!a.debug,typeof a.debug=="function"&&(this.logger=a.debug),this.persistSession=a.persistSession,this.storageKey=a.storageKey,this.autoRefreshToken=a.autoRefreshToken,this.admin=new Am({url:a.url,headers:a.headers,fetch:a.fetch}),this.url=a.url,this.headers=a.headers,this.fetch=Ld(a.fetch),this.lock=a.lock||yd,this.detectSessionInUrl=a.detectSessionInUrl,this.flowType=a.flowType,this.hasCustomAuthorizationHeader=a.hasCustomAuthorizationHeader,a.lock?this.lock=a.lock:Ot()&&(!((i=globalThis==null?void 0:globalThis.navigator)===null||i===void 0)&&i.locks)?this.lock=Mm:this.lock=yd,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?a.storage?this.storage=a.storage:ss()?this.storage=Dm:(this.memoryStorage={},this.storage=vd(this.memoryStorage)):(this.memoryStorage={},this.storage=vd(this.memoryStorage)),Ot()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(d){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",d)}(l=this.broadcastChannel)===null||l===void 0||l.addEventListener("message",async d=>{this._debug("received broadcast notification from other tab or client",d),await this._notifyAllSubscribers(d.data.event,d.data.session,!1)})}this.initialize()}_debug(...s){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Od}) ${new Date().toISOString()}`,...s),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var s;try{const i=dm(window.location.href);let l="none";if(this._isImplicitGrantCallback(i)?l="implicit":await this._isPKCECallback(i)&&(l="pkce"),Ot()&&this.detectSessionInUrl&&l!=="none"){const{data:a,error:d}=await this._getSessionFromURL(i,l);if(d){if(this._debug("#_initialize()","error detecting session from URL",d),Cm(d)){const p=(s=d.details)===null||s===void 0?void 0:s.code;if(p==="identity_already_exists"||p==="identity_not_found"||p==="single_identity_not_deletable")return{error:d}}return await this._removeSession(),{error:d}}const{session:f,redirectType:m}=a;return this._debug("#_initialize()","detected session in URL",f,"redirect type",m),await this._saveSession(f),setTimeout(async()=>{m==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",f):await this._notifyAllSubscribers("SIGNED_IN",f)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(i){return Z(i)?{error:i}:{error:new Ad("Unexpected error during initialization",i)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(s){var i,l,a;try{const d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(l=(i=s==null?void 0:s.options)===null||i===void 0?void 0:i.data)!==null&&l!==void 0?l:{},gotrue_meta_security:{captcha_token:(a=s==null?void 0:s.options)===null||a===void 0?void 0:a.captchaToken}},xform:un}),{data:f,error:m}=d;if(m||!f)return{data:{user:null,session:null},error:m};const p=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(d){if(Z(d))return{data:{user:null,session:null},error:d};throw d}}async signUp(s){var i,l,a;try{let d;if("email"in s){const{email:y,password:E,options:P}=s;let O=null,I=null;this.flowType==="pkce"&&([O,I]=await lr(this.storage,this.storageKey)),d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:P==null?void 0:P.emailRedirectTo,body:{email:y,password:E,data:(i=P==null?void 0:P.data)!==null&&i!==void 0?i:{},gotrue_meta_security:{captcha_token:P==null?void 0:P.captchaToken},code_challenge:O,code_challenge_method:I},xform:un})}else if("phone"in s){const{phone:y,password:E,options:P}=s;d=await ie(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:y,password:E,data:(l=P==null?void 0:P.data)!==null&&l!==void 0?l:{},channel:(a=P==null?void 0:P.channel)!==null&&a!==void 0?a:"sms",gotrue_meta_security:{captcha_token:P==null?void 0:P.captchaToken}},xform:un})}else throw new xi("You must provide either an email or phone number and a password");const{data:f,error:m}=d;if(m||!f)return{data:{user:null,session:null},error:m};const p=f.session,g=f.user;return f.session&&(await this._saveSession(f.session),await this._notifyAllSubscribers("SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(d){if(Z(d))return{data:{user:null,session:null},error:d};throw d}}async signInWithPassword(s){try{let i;if("email"in s){const{email:d,password:f,options:m}=s;i=await ie(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:d,password:f,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken}},xform:gd})}else if("phone"in s){const{phone:d,password:f,options:m}=s;i=await ie(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:d,password:f,gotrue_meta_security:{captcha_token:m==null?void 0:m.captchaToken}},xform:gd})}else throw new xi("You must provide either an email or phone number and a password");const{data:l,error:a}=i;return a?{data:{user:null,session:null},error:a}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new Ol}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:Object.assign({user:l.user,session:l.session},l.weak_password?{weakPassword:l.weak_password}:null),error:a})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOAuth(s){var i,l,a,d;return await this._handleProviderSignIn(s.provider,{redirectTo:(i=s.options)===null||i===void 0?void 0:i.redirectTo,scopes:(l=s.options)===null||l===void 0?void 0:l.scopes,queryParams:(a=s.options)===null||a===void 0?void 0:a.queryParams,skipBrowserRedirect:(d=s.options)===null||d===void 0?void 0:d.skipBrowserRedirect})}async exchangeCodeForSession(s){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(s))}async _exchangeCodeForSession(s){const i=await wi(this.storage,`${this.storageKey}-code-verifier`),[l,a]=(i??"").split("/");try{const{data:d,error:f}=await ie(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:s,code_verifier:l},xform:un});if(await _i(this.storage,`${this.storageKey}-code-verifier`),f)throw f;return!d||!d.session||!d.user?{data:{user:null,session:null,redirectType:null},error:new Ol}:(d.session&&(await this._saveSession(d.session),await this._notifyAllSubscribers("SIGNED_IN",d.session)),{data:Object.assign(Object.assign({},d),{redirectType:a??null}),error:f})}catch(d){if(Z(d))return{data:{user:null,session:null,redirectType:null},error:d};throw d}}async signInWithIdToken(s){try{const{options:i,provider:l,token:a,access_token:d,nonce:f}=s,m=await ie(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:l,id_token:a,access_token:d,nonce:f,gotrue_meta_security:{captcha_token:i==null?void 0:i.captchaToken}},xform:un}),{data:p,error:g}=m;return g?{data:{user:null,session:null},error:g}:!p||!p.session||!p.user?{data:{user:null,session:null},error:new Ol}:(p.session&&(await this._saveSession(p.session),await this._notifyAllSubscribers("SIGNED_IN",p.session)),{data:p,error:g})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithOtp(s){var i,l,a,d,f;try{if("email"in s){const{email:m,options:p}=s;let g=null,y=null;this.flowType==="pkce"&&([g,y]=await lr(this.storage,this.storageKey));const{error:E}=await ie(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:m,data:(i=p==null?void 0:p.data)!==null&&i!==void 0?i:{},create_user:(l=p==null?void 0:p.shouldCreateUser)!==null&&l!==void 0?l:!0,gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken},code_challenge:g,code_challenge_method:y},redirectTo:p==null?void 0:p.emailRedirectTo});return{data:{user:null,session:null},error:E}}if("phone"in s){const{phone:m,options:p}=s,{data:g,error:y}=await ie(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:m,data:(a=p==null?void 0:p.data)!==null&&a!==void 0?a:{},create_user:(d=p==null?void 0:p.shouldCreateUser)!==null&&d!==void 0?d:!0,gotrue_meta_security:{captcha_token:p==null?void 0:p.captchaToken},channel:(f=p==null?void 0:p.channel)!==null&&f!==void 0?f:"sms"}});return{data:{user:null,session:null,messageId:g==null?void 0:g.message_id},error:y}}throw new xi("You must provide either an email or phone number.")}catch(m){if(Z(m))return{data:{user:null,session:null},error:m};throw m}}async verifyOtp(s){var i,l;try{let a,d;"options"in s&&(a=(i=s.options)===null||i===void 0?void 0:i.redirectTo,d=(l=s.options)===null||l===void 0?void 0:l.captchaToken);const{data:f,error:m}=await ie(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},s),{gotrue_meta_security:{captcha_token:d}}),redirectTo:a,xform:un});if(m)throw m;if(!f)throw new Error("An error occurred on token verification.");const p=f.session,g=f.user;return p!=null&&p.access_token&&(await this._saveSession(p),await this._notifyAllSubscribers(s.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",p)),{data:{user:g,session:p},error:null}}catch(a){if(Z(a))return{data:{user:null,session:null},error:a};throw a}}async signInWithSSO(s){var i,l,a;try{let d=null,f=null;return this.flowType==="pkce"&&([d,f]=await lr(this.storage,this.storageKey)),await ie(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in s?{provider_id:s.providerId}:null),"domain"in s?{domain:s.domain}:null),{redirect_to:(l=(i=s.options)===null||i===void 0?void 0:i.redirectTo)!==null&&l!==void 0?l:void 0}),!((a=s==null?void 0:s.options)===null||a===void 0)&&a.captchaToken?{gotrue_meta_security:{captcha_token:s.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:d,code_challenge_method:f}),headers:this.headers,xform:bm})}catch(d){if(Z(d))return{data:null,error:d};throw d}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async s=>{const{data:{session:i},error:l}=s;if(l)throw l;if(!i)throw new an;const{error:a}=await ie(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:i.access_token});return{data:{user:null,session:null},error:a}})}catch(s){if(Z(s))return{data:{user:null,session:null},error:s};throw s}}async resend(s){try{const i=`${this.url}/resend`;if("email"in s){const{email:l,type:a,options:d}=s,{error:f}=await ie(this.fetch,"POST",i,{headers:this.headers,body:{email:l,type:a,gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}},redirectTo:d==null?void 0:d.emailRedirectTo});return{data:{user:null,session:null},error:f}}else if("phone"in s){const{phone:l,type:a,options:d}=s,{data:f,error:m}=await ie(this.fetch,"POST",i,{headers:this.headers,body:{phone:l,type:a,gotrue_meta_security:{captcha_token:d==null?void 0:d.captchaToken}}});return{data:{user:null,session:null,messageId:f==null?void 0:f.message_id},error:m}}throw new xi("You must provide either an email or phone number and a type")}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async i=>i))}async _acquireLock(s,i){this._debug("#_acquireLock","begin",s);try{if(this.lockAcquired){const l=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),a=(async()=>(await l,await i()))();return this.pendingInLock.push((async()=>{try{await a}catch{}})()),a}return await this.lock(`lock:${this.storageKey}`,s,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const l=i();for(this.pendingInLock.push((async()=>{try{await l}catch{}})()),await l;this.pendingInLock.length;){const a=[...this.pendingInLock];await Promise.all(a),this.pendingInLock.splice(0,a.length)}return await l}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(s){this._debug("#_useSession","begin");try{const i=await this.__loadSession();return await s(i)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let s=null;const i=await wi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",i),i!==null&&(this._isValidSession(i)?s=i:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!s)return{data:{session:null},error:null};const l=s.expires_at?s.expires_at*1e3-Date.now()<bl:!1;if(this._debug("#__loadSession()",`session has${l?"":" not"} expired`,"expires_at",s.expires_at),!l){if(this.storage.isServer){let f=this.suppressGetSessionWarning;s=new Proxy(s,{get:(p,g,y)=>(!f&&g==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),f=!0,this.suppressGetSessionWarning=!0),Reflect.get(p,g,y))})}return{data:{session:s},error:null}}const{session:a,error:d}=await this._callRefreshToken(s.refresh_token);return d?{data:{session:null},error:d}:{data:{session:a},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(s){return s?await this._getUser(s):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(s){try{return s?await ie(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:s,xform:dn}):await this._useSession(async i=>{var l,a,d;const{data:f,error:m}=i;if(m)throw m;return!(!((l=f.session)===null||l===void 0)&&l.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new an}:await ie(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(d=(a=f.session)===null||a===void 0?void 0:a.access_token)!==null&&d!==void 0?d:void 0,xform:dn})})}catch(i){if(Z(i))return jm(i)&&(await this._removeSession(),await _i(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:i};throw i}}async updateUser(s,i={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(s,i))}async _updateUser(s,i={}){try{return await this._useSession(async l=>{const{data:a,error:d}=l;if(d)throw d;if(!a.session)throw new an;const f=a.session;let m=null,p=null;this.flowType==="pkce"&&s.email!=null&&([m,p]=await lr(this.storage,this.storageKey));const{data:g,error:y}=await ie(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:i==null?void 0:i.emailRedirectTo,body:Object.assign(Object.assign({},s),{code_challenge:m,code_challenge_method:p}),jwt:f.access_token,xform:dn});if(y)throw y;return f.user=g.user,await this._saveSession(f),await this._notifyAllSubscribers("USER_UPDATED",f),{data:{user:f.user},error:null}})}catch(l){if(Z(l))return{data:{user:null},error:l};throw l}}_decodeJWT(s){return fd(s)}async setSession(s){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(s))}async _setSession(s){try{if(!s.access_token||!s.refresh_token)throw new an;const i=Date.now()/1e3;let l=i,a=!0,d=null;const f=fd(s.access_token);if(f.exp&&(l=f.exp,a=l<=i),a){const{session:m,error:p}=await this._callRefreshToken(s.refresh_token);if(p)return{data:{user:null,session:null},error:p};if(!m)return{data:{user:null,session:null},error:null};d=m}else{const{data:m,error:p}=await this._getUser(s.access_token);if(p)throw p;d={access_token:s.access_token,refresh_token:s.refresh_token,user:m.user,token_type:"bearer",expires_in:l-i,expires_at:l},await this._saveSession(d),await this._notifyAllSubscribers("SIGNED_IN",d)}return{data:{user:d.user,session:d},error:null}}catch(i){if(Z(i))return{data:{session:null,user:null},error:i};throw i}}async refreshSession(s){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(s))}async _refreshSession(s){try{return await this._useSession(async i=>{var l;if(!s){const{data:f,error:m}=i;if(m)throw m;s=(l=f.session)!==null&&l!==void 0?l:void 0}if(!(s!=null&&s.refresh_token))throw new an;const{session:a,error:d}=await this._callRefreshToken(s.refresh_token);return d?{data:{user:null,session:null},error:d}:a?{data:{user:a.user,session:a},error:null}:{data:{user:null,session:null},error:null}})}catch(i){if(Z(i))return{data:{user:null,session:null},error:i};throw i}}async _getSessionFromURL(s,i){try{if(!Ot())throw new ki("No browser detected.");if(s.error||s.error_description||s.error_code)throw new ki(s.error_description||"Error in URL with unspecified error_description",{error:s.error||"unspecified_error",code:s.error_code||"unspecified_code"});switch(i){case"implicit":if(this.flowType==="pkce")throw new hd("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new ki("Not a valid implicit grant flow url.");break;default:}if(i==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!s.code)throw new hd("No code detected.");const{data:ae,error:U}=await this._exchangeCodeForSession(s.code);if(U)throw U;const q=new URL(window.location.href);return q.searchParams.delete("code"),window.history.replaceState(window.history.state,"",q.toString()),{data:{session:ae.session,redirectType:null},error:null}}const{provider_token:l,provider_refresh_token:a,access_token:d,refresh_token:f,expires_in:m,expires_at:p,token_type:g}=s;if(!d||!m||!f||!g)throw new ki("No session defined in URL");const y=Math.round(Date.now()/1e3),E=parseInt(m);let P=y+E;p&&(P=parseInt(p));const O=P-y;O*1e3<=ur&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${O}s, should have been closer to ${E}s`);const I=P-E;y-I>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",I,P,y):y-I<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",I,P,y);const{data:F,error:D}=await this._getUser(d);if(D)throw D;const ne={provider_token:l,provider_refresh_token:a,access_token:d,expires_in:E,expires_at:P,refresh_token:f,token_type:g,user:F.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:ne,redirectType:s.type},error:null}}catch(l){if(Z(l))return{data:{session:null,redirectType:null},error:l};throw l}}_isImplicitGrantCallback(s){return!!(s.access_token||s.error_description)}async _isPKCECallback(s){const i=await wi(this.storage,`${this.storageKey}-code-verifier`);return!!(s.code&&i)}async signOut(s={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(s))}async _signOut({scope:s}={scope:"global"}){return await this._useSession(async i=>{var l;const{data:a,error:d}=i;if(d)return{error:d};const f=(l=a.session)===null||l===void 0?void 0:l.access_token;if(f){const{error:m}=await this.admin.signOut(f,s);if(m&&!(Em(m)&&(m.status===404||m.status===401||m.status===403)))return{error:m}}return s!=="others"&&(await this._removeSession(),await _i(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(s){const i=cm(),l={id:i,callback:s,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",i),this.stateChangeEmitters.delete(i)}};return this._debug("#onAuthStateChange()","registered callback with id",i),this.stateChangeEmitters.set(i,l),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(i)})))(),{data:{subscription:l}}}async _emitInitialSession(s){return await this._useSession(async i=>{var l,a;try{const{data:{session:d},error:f}=i;if(f)throw f;await((l=this.stateChangeEmitters.get(s))===null||l===void 0?void 0:l.callback("INITIAL_SESSION",d)),this._debug("INITIAL_SESSION","callback id",s,"session",d)}catch(d){await((a=this.stateChangeEmitters.get(s))===null||a===void 0?void 0:a.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",s,"error",d),console.error(d)}})}async resetPasswordForEmail(s,i={}){let l=null,a=null;this.flowType==="pkce"&&([l,a]=await lr(this.storage,this.storageKey,!0));try{return await ie(this.fetch,"POST",`${this.url}/recover`,{body:{email:s,code_challenge:l,code_challenge_method:a,gotrue_meta_security:{captcha_token:i.captchaToken}},headers:this.headers,redirectTo:i.redirectTo})}catch(d){if(Z(d))return{data:null,error:d};throw d}}async getUserIdentities(){var s;try{const{data:i,error:l}=await this.getUser();if(l)throw l;return{data:{identities:(s=i.user.identities)!==null&&s!==void 0?s:[]},error:null}}catch(i){if(Z(i))return{data:null,error:i};throw i}}async linkIdentity(s){var i;try{const{data:l,error:a}=await this._useSession(async d=>{var f,m,p,g,y;const{data:E,error:P}=d;if(P)throw P;const O=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,s.provider,{redirectTo:(f=s.options)===null||f===void 0?void 0:f.redirectTo,scopes:(m=s.options)===null||m===void 0?void 0:m.scopes,queryParams:(p=s.options)===null||p===void 0?void 0:p.queryParams,skipBrowserRedirect:!0});return await ie(this.fetch,"GET",O,{headers:this.headers,jwt:(y=(g=E.session)===null||g===void 0?void 0:g.access_token)!==null&&y!==void 0?y:void 0})});if(a)throw a;return Ot()&&!(!((i=s.options)===null||i===void 0)&&i.skipBrowserRedirect)&&window.location.assign(l==null?void 0:l.url),{data:{provider:s.provider,url:l==null?void 0:l.url},error:null}}catch(l){if(Z(l))return{data:{provider:s.provider,url:null},error:l};throw l}}async unlinkIdentity(s){try{return await this._useSession(async i=>{var l,a;const{data:d,error:f}=i;if(f)throw f;return await ie(this.fetch,"DELETE",`${this.url}/user/identities/${s.identity_id}`,{headers:this.headers,jwt:(a=(l=d.session)===null||l===void 0?void 0:l.access_token)!==null&&a!==void 0?a:void 0})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _refreshAccessToken(s){const i=`#_refreshAccessToken(${s.substring(0,5)}...)`;this._debug(i,"begin");try{const l=Date.now();return await mm(async a=>(a>0&&await pm(200*Math.pow(2,a-1)),this._debug(i,"refreshing attempt",a),await ie(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:s},headers:this.headers,xform:un})),(a,d)=>{const f=200*Math.pow(2,a);return d&&$l(d)&&Date.now()+f-l<ur})}catch(l){if(this._debug(i,"error",l),Z(l))return{data:{session:null,user:null},error:l};throw l}finally{this._debug(i,"end")}}_isValidSession(s){return typeof s=="object"&&s!==null&&"access_token"in s&&"refresh_token"in s&&"expires_at"in s}async _handleProviderSignIn(s,i){const l=await this._getUrlForProvider(`${this.url}/authorize`,s,{redirectTo:i.redirectTo,scopes:i.scopes,queryParams:i.queryParams});return this._debug("#_handleProviderSignIn()","provider",s,"options",i,"url",l),Ot()&&!i.skipBrowserRedirect&&window.location.assign(l),{data:{provider:s,url:l},error:null}}async _recoverAndRefresh(){var s;const i="#_recoverAndRefresh()";this._debug(i,"begin");try{const l=await wi(this.storage,this.storageKey);if(this._debug(i,"session from storage",l),!this._isValidSession(l)){this._debug(i,"session is not valid"),l!==null&&await this._removeSession();return}const a=((s=l.expires_at)!==null&&s!==void 0?s:1/0)*1e3-Date.now()<bl;if(this._debug(i,`session has${a?"":" not"} expired with margin of ${bl}s`),a){if(this.autoRefreshToken&&l.refresh_token){const{error:d}=await this._callRefreshToken(l.refresh_token);d&&(console.error(d),$l(d)||(this._debug(i,"refresh failed with a non-retryable error, removing the session",d),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",l)}catch(l){this._debug(i,"error",l),console.error(l);return}finally{this._debug(i,"end")}}async _callRefreshToken(s){var i,l;if(!s)throw new an;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const a=`#_callRefreshToken(${s.substring(0,5)}...)`;this._debug(a,"begin");try{this.refreshingDeferred=new Ti;const{data:d,error:f}=await this._refreshAccessToken(s);if(f)throw f;if(!d.session)throw new an;await this._saveSession(d.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",d.session);const m={session:d.session,error:null};return this.refreshingDeferred.resolve(m),m}catch(d){if(this._debug(a,"error",d),Z(d)){const f={session:null,error:d};return $l(d)||await this._removeSession(),(i=this.refreshingDeferred)===null||i===void 0||i.resolve(f),f}throw(l=this.refreshingDeferred)===null||l===void 0||l.reject(d),d}finally{this.refreshingDeferred=null,this._debug(a,"end")}}async _notifyAllSubscribers(s,i,l=!0){const a=`#_notifyAllSubscribers(${s})`;this._debug(a,"begin",i,`broadcast = ${l}`);try{this.broadcastChannel&&l&&this.broadcastChannel.postMessage({event:s,session:i});const d=[],f=Array.from(this.stateChangeEmitters.values()).map(async m=>{try{await m.callback(s,i)}catch(p){d.push(p)}});if(await Promise.all(f),d.length>0){for(let m=0;m<d.length;m+=1)console.error(d[m]);throw d[0]}}finally{this._debug(a,"end")}}async _saveSession(s){this._debug("#_saveSession()",s),this.suppressGetSessionWarning=!0,await Id(this.storage,this.storageKey,s)}async _removeSession(){this._debug("#_removeSession()"),await _i(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const s=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{s&&Ot()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",s)}catch(i){console.error("removing visibilitychange callback failed",i)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const s=setInterval(()=>this._autoRefreshTokenTick(),ur);this.autoRefreshTicker=s,s&&typeof s=="object"&&typeof s.unref=="function"?s.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(s),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const s=this.autoRefreshTicker;this.autoRefreshTicker=null,s&&clearInterval(s)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const s=Date.now();try{return await this._useSession(async i=>{const{data:{session:l}}=i;if(!l||!l.refresh_token||!l.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const a=Math.floor((l.expires_at*1e3-s)/ur);this._debug("#_autoRefreshTokenTick()",`access token expires in ${a} ticks, a tick lasts ${ur}ms, refresh threshold is ${Ul} ticks`),a<=Ul&&await this._callRefreshToken(l.refresh_token)})}catch(i){console.error("Auto refresh tick failed with error. This is likely a transient error.",i)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(s){if(s.isAcquireTimeout||s instanceof Dd)this._debug("auto refresh token tick lock not available");else throw s}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Ot()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(s){console.error("_handleVisibilityChange",s)}}async _onVisibilityChanged(s){const i=`#_onVisibilityChanged(${s})`;this._debug(i,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),s||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(i,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(s,i,l){const a=[`provider=${encodeURIComponent(i)}`];if(l!=null&&l.redirectTo&&a.push(`redirect_to=${encodeURIComponent(l.redirectTo)}`),l!=null&&l.scopes&&a.push(`scopes=${encodeURIComponent(l.scopes)}`),this.flowType==="pkce"){const[d,f]=await lr(this.storage,this.storageKey),m=new URLSearchParams({code_challenge:`${encodeURIComponent(d)}`,code_challenge_method:`${encodeURIComponent(f)}`});a.push(m.toString())}if(l!=null&&l.queryParams){const d=new URLSearchParams(l.queryParams);a.push(d.toString())}return l!=null&&l.skipBrowserRedirect&&a.push(`skip_http_redirect=${l.skipBrowserRedirect}`),`${s}?${a.join("&")}`}async _unenroll(s){try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;return d?{data:null,error:d}:await ie(this.fetch,"DELETE",`${this.url}/factors/${s.factorId}`,{headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _enroll(s){try{return await this._useSession(async i=>{var l,a;const{data:d,error:f}=i;if(f)return{data:null,error:f};const m=Object.assign({friendly_name:s.friendlyName,factor_type:s.factorType},s.factorType==="phone"?{phone:s.phone}:{issuer:s.issuer}),{data:p,error:g}=await ie(this.fetch,"POST",`${this.url}/factors`,{body:m,headers:this.headers,jwt:(l=d==null?void 0:d.session)===null||l===void 0?void 0:l.access_token});return g?{data:null,error:g}:(s.factorType==="totp"&&(!((a=p==null?void 0:p.totp)===null||a===void 0)&&a.qr_code)&&(p.totp.qr_code=`data:image/svg+xml;utf-8,${p.totp.qr_code}`),{data:p,error:null})})}catch(i){if(Z(i))return{data:null,error:i};throw i}}async _verify(s){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;if(d)return{data:null,error:d};const{data:f,error:m}=await ie(this.fetch,"POST",`${this.url}/factors/${s.factorId}/verify`,{body:{code:s.code,challenge_id:s.challengeId},headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token});return m?{data:null,error:m}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+f.expires_in},f)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",f),{data:f,error:m})})}catch(i){if(Z(i))return{data:null,error:i};throw i}})}async _challenge(s){return this._acquireLock(-1,async()=>{try{return await this._useSession(async i=>{var l;const{data:a,error:d}=i;return d?{data:null,error:d}:await ie(this.fetch,"POST",`${this.url}/factors/${s.factorId}/challenge`,{body:{channel:s.channel},headers:this.headers,jwt:(l=a==null?void 0:a.session)===null||l===void 0?void 0:l.access_token})})}catch(i){if(Z(i))return{data:null,error:i};throw i}})}async _challengeAndVerify(s){const{data:i,error:l}=await this._challenge({factorId:s.factorId});return l?{data:null,error:l}:await this._verify({factorId:s.factorId,challengeId:i.id,code:s.code})}async _listFactors(){const{data:{user:s},error:i}=await this.getUser();if(i)return{data:null,error:i};const l=(s==null?void 0:s.factors)||[],a=l.filter(f=>f.factor_type==="totp"&&f.status==="verified"),d=l.filter(f=>f.factor_type==="phone"&&f.status==="verified");return{data:{all:l,totp:a,phone:d},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async s=>{var i,l;const{data:{session:a},error:d}=s;if(d)return{data:null,error:d};if(!a)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const f=this._decodeJWT(a.access_token);let m=null;f.aal&&(m=f.aal);let p=m;((l=(i=a.user.factors)===null||i===void 0?void 0:i.filter(E=>E.status==="verified"))!==null&&l!==void 0?l:[]).length>0&&(p="aal2");const y=f.amr||[];return{data:{currentLevel:m,nextLevel:p,currentAuthenticationMethods:y},error:null}}))}}is.nextInstanceID=0;const Vm=is;class Bm extends Vm{constructor(s){super(s)}}var Hm=function(c,s,i,l){function a(d){return d instanceof i?d:new i(function(f){f(d)})}return new(i||(i=Promise))(function(d,f){function m(y){try{g(l.next(y))}catch(E){f(E)}}function p(y){try{g(l.throw(y))}catch(E){f(E)}}function g(y){y.done?d(y.value):a(y.value).then(m,p)}g((l=l.apply(c,s||[])).next())})};class qm{constructor(s,i,l){var a,d,f;if(this.supabaseUrl=s,this.supabaseKey=i,!s)throw new Error("supabaseUrl is required.");if(!i)throw new Error("supabaseKey is required.");const m=sm(s);this.realtimeUrl=`${m}/realtime/v1`.replace(/^http/i,"ws"),this.authUrl=`${m}/auth/v1`,this.storageUrl=`${m}/storage/v1`,this.functionsUrl=`${m}/functions/v1`;const p=`sb-${new URL(this.authUrl).hostname.split(".")[0]}-auth-token`,g={db:Jp,realtime:Xp,auth:Object.assign(Object.assign({},Yp),{storageKey:p}),global:Kp},y=im(l??{},g);this.storageKey=(a=y.auth.storageKey)!==null&&a!==void 0?a:"",this.headers=(d=y.global.headers)!==null&&d!==void 0?d:{},y.accessToken?(this.accessToken=y.accessToken,this.auth=new Proxy({},{get:(E,P)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(P)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((f=y.auth)!==null&&f!==void 0?f:{},this.headers,y.global.fetch),this.fetch=nm(i,this._getAccessToken.bind(this),y.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},y.realtime)),this.rest=new vp(`${m}/rest/v1`,{headers:this.headers,schema:y.db.schema,fetch:this.fetch}),y.accessToken||this._listenForAuthEvents()}get functions(){return new ip(this.functionsUrl,{headers:this.headers,customFetch:this.fetch})}get storage(){return new Wp(this.storageUrl,this.headers,this.fetch)}from(s){return this.rest.from(s)}schema(s){return this.rest.schema(s)}rpc(s,i={},l={}){return this.rest.rpc(s,i,l)}channel(s,i={config:{}}){return this.realtime.channel(s,i)}getChannels(){return this.realtime.getChannels()}removeChannel(s){return this.realtime.removeChannel(s)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var s,i;return Hm(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:l}=yield this.auth.getSession();return(i=(s=l.session)===null||s===void 0?void 0:s.access_token)!==null&&i!==void 0?i:null})}_initSupabaseAuthClient({autoRefreshToken:s,persistSession:i,detectSessionInUrl:l,storage:a,storageKey:d,flowType:f,lock:m,debug:p},g,y){const E={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new Bm({url:this.authUrl,headers:Object.assign(Object.assign({},E),g),storageKey:d,autoRefreshToken:s,persistSession:i,detectSessionInUrl:l,storage:a,flowType:f,lock:m,debug:p,fetch:y,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(s){return new Op(this.realtimeUrl,Object.assign(Object.assign({},s),{params:Object.assign({apikey:this.supabaseKey},s==null?void 0:s.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((i,l)=>{this._handleTokenChanged(i,"CLIENT",l==null?void 0:l.access_token)})}_handleTokenChanged(s,i,l){(s==="TOKEN_REFRESHED"||s==="SIGNED_IN")&&this.changedAccessToken!==l?this.changedAccessToken=l:s==="SIGNED_OUT"&&(this.realtime.setAuth(),i=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Wm=(c,s,i)=>new qm(c,s,i),Gm="https://nzluxtdhyiwmyuhmiili.supabase.co",Qm="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.d8dIrFP5nsY2ZHHAvnMnacPDkvix9-xHEvgPfSrmbyI",Si=Wm(Gm,Qm);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Km={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jm=c=>c.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),pt=(c,s)=>{const i=se.forwardRef(({color:l="currentColor",size:a=24,strokeWidth:d=2,absoluteStrokeWidth:f,className:m="",children:p,...g},y)=>se.createElement("svg",{ref:y,...Km,width:a,height:a,stroke:l,strokeWidth:f?Number(d)*24/Number(a):d,className:["lucide",`lucide-${Jm(c)}`,m].join(" "),...g},[...s.map(([E,P])=>se.createElement(E,P)),...Array.isArray(p)?p:[p]]));return i.displayName=`${c}`,i};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ym=pt("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xm=pt("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zm=pt("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eg=pt("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zd=pt("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tg=pt("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ci=pt("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ng=pt("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pi=pt("Printer",[["polyline",{points:"6 9 6 2 18 2 18 9",key:"1306q4"}],["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["rect",{width:"12",height:"8",x:"6",y:"14",key:"5ipwut"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=pt("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Md=pt("Unlock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 9.9-1",key:"1mm8w8"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rg=pt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function sg({activeTab:c,onTabChange:s,isAuthenticated:i}){const l=i?["sell","bulk","redeem","list"]:["sell","redeem","list"];return w.jsx("div",{style:{display:"flex",gap:"0.5rem"},children:l.map(a=>w.jsxs("button",{onClick:()=>s(a),style:{backgroundColor:"black",color:c===a?"#dc2626":"white",border:c===a?"2px solid #dc2626":"none",padding:"0.5rem 1rem",borderRadius:"0.375rem",fontWeight:"600",display:"flex",alignItems:"center",transition:"all 300ms"},children:[a==="sell"&&w.jsx(eg,{className:"h-5 w-5 mr-2"}),a==="bulk"&&w.jsx(ng,{className:"h-5 w-5 mr-2"}),a==="redeem"&&w.jsx(Ud,{className:"h-5 w-5 mr-2"}),a==="list"&&w.jsx(tg,{className:"h-5 w-5 mr-2"}),w.jsxs("span",{children:[a==="sell"&&"Vender Vale-Presente",a==="bulk"&&"Gerar em Lote",a==="redeem"&&"Resgatar Vale-Presente",a==="list"&&"Listar Vale-Presentes"]})]},a))},"tabs-container-updated")}let Ei;const ig=new Uint8Array(16);function og(){if(!Ei&&(Ei=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!Ei))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Ei(ig)}const Ue=[];for(let c=0;c<256;++c)Ue.push((c+256).toString(16).slice(1));function lg(c,s=0){return Ue[c[s+0]]+Ue[c[s+1]]+Ue[c[s+2]]+Ue[c[s+3]]+"-"+Ue[c[s+4]]+Ue[c[s+5]]+"-"+Ue[c[s+6]]+Ue[c[s+7]]+"-"+Ue[c[s+8]]+Ue[c[s+9]]+"-"+Ue[c[s+10]]+Ue[c[s+11]]+Ue[c[s+12]]+Ue[c[s+13]]+Ue[c[s+14]]+Ue[c[s+15]]}const ag=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),wd={randomUUID:ag};function Ni(c,s,i){if(wd.randomUUID&&!c)return wd.randomUUID();c=c||{};const l=c.random||(c.rng||og)();return l[6]=l[6]&15|64,l[8]=l[8]&63|128,lg(l)}function ug({onSell:c}){const[s,i]=se.useState({value:"",recipientName:"",buyerName:""}),[l,a]=se.useState(null),[d,f]=se.useState(!1),[m,p]=se.useState(null),g=async E=>{if(E.preventDefault(),a(null),!s.value||!s.buyerName){a("Preencha todos os campos obrigatórios");return}try{f(!0);const P={id:Ni(),code:y(),value:Number(s.value),recipientName:s.recipientName,buyerName:s.buyerName,createdAt:new Date,used:!1};await c(P),p(P),i({value:"",recipientName:"",buyerName:""})}catch(P){console.error("Error generating gift card:",P),a("Erro ao gerar vale-presente")}finally{f(!1)}},y=()=>`N${Ni().replace(/-/g,"").substring(0,15).toUpperCase()}`;return w.jsx("div",{className:"space-y-8",children:w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Vender Vale-Presente"}),w.jsxs("form",{onSubmit:g,className:"space-y-6",children:[l&&w.jsx("div",{className:"p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200",children:l}),d&&w.jsx("div",{className:"p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200",children:"Gerando vale-presente..."}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Valor do Vale-Presente (R$)"}),w.jsx("input",{type:"number",step:"0.01",required:!0,value:s.value,onChange:E=>i({...s,value:E.target.value}),className:"form-input",placeholder:"0.00"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Destinatário"}),w.jsx("input",{type:"text",value:s.recipientName,onChange:E=>i({...s,recipientName:E.target.value}),className:"form-input",placeholder:"Nome de quem vai receber o vale (opcional)"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Comprador"}),w.jsx("input",{type:"text",required:!0,value:s.buyerName,onChange:E=>i({...s,buyerName:E.target.value}),className:"form-input",placeholder:"Nome de quem está comprando"})]}),w.jsxs("button",{type:"submit",disabled:d,className:"primary-button mt-4",children:[w.jsx(zd,{className:"h-5 w-5"}),w.jsx("span",{children:d?"Gerando...":"Gerar Vale-Presente"})]})]})]})})}const jt="/assets/logo-BXwS7maE.png";function cg({giftCards:c,isAuthenticated:s}){const[i,l]=se.useState(1),[a,d]=se.useState([]),[f,m]=se.useState(new Set),[p,g]=se.useState([]),y=10;se.useEffect(()=>{const $=c.filter(pe=>!pe.used),A={};$.forEach(pe=>{const _e=pe.code.substring(0,3);A[_e]||(A[_e]=[]),A[_e].push(pe)});const X=[],oe=[];Object.entries(A).forEach(([pe,_e])=>{_e.length===1?X.push(_e[0]):oe.push({groupId:pe,cards:_e,sampleCard:_e[0],isExpanded:f.has(pe)})}),g(X),d(oe)},[c,f]);const P=(()=>{const $=[...p.map(oe=>({type:"individual",card:oe})),...a.flatMap(oe=>oe.isExpanded&&s?[{type:"group",group:oe},...oe.cards.map(pe=>({type:"card",card:pe}))]:[{type:"group",group:oe}])],A=(i-1)*y,X=A+y;return{currentItems:$.slice(A,X),totalPages:Math.ceil($.length/y)}})(),O=P.currentItems,I=P.totalPages,F=$=>{l($)},D=()=>{const $=new Date;let A=$.getMonth()+2,X=$.getFullYear();return A>11&&(A=A-12,X+=1),`${new Date(X,A+1,0).getDate().toString().padStart(2,"0")}/${(A+1).toString().padStart(2,"0")}/${X}`},ne=($,A)=>{const X=_e=>{const lt=document.createElement("canvas");return lt.width=_e.width,lt.height=_e.height,lt.getContext("2d").drawImage(_e,0,0),lt.toDataURL("image/png")},oe=new Image;oe.src=jt;let pe=jt;if(oe.complete)try{pe=X(oe)}catch(_e){console.error("Error converting logo to data URL",_e)}return`
      <div style="
        width: ${A?"226px":"302px"}; 
        padding: ${A?"0.75rem":"1rem"}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${pe}"
              alt="Logo" 
              style="height: ${A?"20px":"28px"}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${A?"0.875rem":"1.125rem"}; 
              margin: 0; 
              color: ${A?"#dc2626":"black"};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${$.recipientName?`
            <div>
              <p style="font-size: ${A?"0.75rem":"0.875rem"}; margin: 0; color: ${A?"#4b5563":"black"};">Para:</p>
              <p style="font-weight: 500; font-size: ${A?"0.875rem":"1rem"}; margin: 0; color: ${A?"#1f2937":"black"}">
                ${$.recipientName}
              </p>
            </div>
          `:""}
          <div style="text-align: ${$.recipientName?"right":"center"}; ${$.recipientName?"":"width: 100%;"}">
            <p style="font-size: ${A?"0.75rem":"0.875rem"}; margin: 0; color: ${A?"#4b5563":"black"};">Valor:</p>
            <p style="font-weight: 700; font-size: ${A?"1.25rem":"1.5rem"}; margin: 0; color: ${A?"#dc2626":"black"}">
              R$ ${$.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${A?100:130}&data=${encodeURIComponent($.code)}" 
               style="width: ${A?"100px":"130px"}; height: ${A?"100px":"130px"};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${A?"1rem":"1.125rem"}; 
            margin: 0; 
            color: ${A?"#1f2937":"black"};
          ">
            ${$.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${A?"#6b7280":"black"};">
          <p style="font-weight: 500; font-size: ${A?"0.75rem":"0.875rem"}; margin: 0;">Um presente de: ${$.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${A?"0.625rem":"0.75rem"}; margin: 0;">
            Válido até ${D()}
          </p>
        </div>
      </div>
    `},ae=($,A=!1)=>{const X=window.open("","_blank");if(X){const oe=`
        <html>
          <head>
            <title>${A?"Lote de Vale-Presentes":"Vale-Presente"}</title>
            <style>
              @media print {
                @page {
                  ${A?"size: A4; margin: 10mm;":"size: 80mm auto; margin: 0;"}
                }
                body {
                  margin: 0;
                  padding: ${A?"10mm":"4mm"};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
              
              /* Grid layout for bulk cards */
              .bulk-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
              }
              
              .card-container {
                margin-bottom: 16px;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${Array.isArray($)?`
                <div class="bulk-grid">
                  ${$.map(pe=>`
                    <div class="card-container">
                      ${ne(pe,!0)}
                    </div>
                  `).join("")}
                </div>
              `:`
                <div style="display: flex; justify-content: center;">
                  ${ne($,!1)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;X.document.write(oe),X.document.close()}},U=$=>{s&&m(A=>{const X=new Set(A);return X.has($)?X.delete($):X.add($),X})},q=$=>$.substring(0,3)+"•••••••••••"+$.substring($.length-2);return w.jsxs("div",{className:"w-full mx-auto space-y-6 bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Vale-Presentes Ativos"}),w.jsx("div",{className:"space-y-3",children:O.length===0&&p.length===0&&a.length===0?w.jsxs("div",{className:"empty-state",children:[w.jsx("p",{className:"empty-state-title",children:"Nenhum Vale-Presente ativo"}),w.jsx("p",{className:"empty-state-subtitle",children:'Gere novos vale-presentes na aba "Vender Vale-Presente" ou "Gerar em Lote"'})]}):O.map(($,A)=>{if($.type==="individual")return w.jsxs("div",{className:"list-item",children:[w.jsxs("div",{children:[w.jsx("p",{className:"list-item-title",children:$.card.recipientName||`Comprador: ${$.card.buyerName}`}),w.jsxs("p",{className:"list-item-text",children:["Código do Vale-Presente: ",s?$.card.code:q($.card.code)]}),w.jsxs("p",{className:"list-item-text",children:["Valor: R$ ",$.card.value.toFixed(2)]})]}),s?w.jsxs("button",{onClick:()=>ae([$.card],!1),className:"print-button",children:[w.jsx(Pi,{className:"h-4 w-4"}),w.jsx("span",{children:"Imprimir"})]}):w.jsxs("div",{className:"text-gray-400 flex items-center",children:[w.jsx(Ci,{className:"h-4 w-4 mr-1"}),w.jsx("span",{className:"text-sm",children:"Bloqueado"})]})]},$.card.id);if($.type==="group"){const X=$.group;return w.jsx("div",{className:"list-item",children:w.jsxs("div",{className:"flex items-center justify-between w-full",children:[w.jsxs("div",{children:[w.jsxs("p",{className:"list-item-title",children:["Grupo ",X.groupId," - ",X.sampleCard.buyerName]}),w.jsxs("p",{className:"list-item-text",children:[X.cards.length," vale-presentes"]}),w.jsxs("p",{className:"list-item-text",children:["Valor: R$ ",X.sampleCard.value.toFixed(2)]})]}),w.jsx("div",{className:"flex items-center space-x-2",children:s?w.jsxs(w.Fragment,{children:[w.jsxs("button",{onClick:()=>ae(X.cards,!0),className:"print-button",children:[w.jsx(Pi,{className:"h-4 w-4"}),w.jsx("span",{children:"Imprimir Todos"})]}),w.jsx("button",{onClick:()=>U(X.groupId),className:"p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200",children:X.isExpanded?w.jsx(Zm,{className:"h-5 w-5"}):w.jsx(Xm,{className:"h-5 w-5"})})]}):w.jsxs("div",{className:"text-gray-400 flex items-center",children:[w.jsx(Ci,{className:"h-4 w-4 mr-1"}),w.jsx("span",{className:"text-sm",children:"Bloqueado"})]})})]})},X.groupId)}else return w.jsx("div",{className:"ml-6 p-3 border-l-2 border-red-200 bg-white rounded-r-lg",children:w.jsxs("div",{className:"flex items-center justify-between",children:[w.jsxs("p",{className:"list-item-text",children:["Código do Vale-Presente: ",s?$.card.code:q($.card.code)]}),w.jsx("button",{onClick:()=>ae([$.card],!1),className:"text-sm text-red-600 hover:text-red-700 px-2 py-1 rounded-md hover:bg-red-50 transition-colors duration-200",children:"Imprimir Individual"})]})},$.card.id)})}),I>1&&w.jsx("div",{className:"flex justify-center space-x-2 mt-4",children:Array.from({length:I},($,A)=>A+1).map($=>w.jsx("button",{onClick:()=>F($),className:`px-3 py-1 rounded ${i===$?"bg-red-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:$},$))})]})}function dg({giftCard:c}){const s=()=>{if(document.getElementById("redemption-receipt")){const a=window.open("","_blank");if(!a){alert("Por favor, permita pop-ups para imprimir o comprovante.");return}a.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Comprovante de Resgate</title>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
                padding: 0;
              }
              body {
                margin: 0;
                padding: 4mm;
                font-family: Arial, sans-serif;
                width: 72mm; /* 80mm - 8mm padding */
                box-sizing: border-box;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
              .receipt {
                width: 100%;
                background: white;
              }
              .logo {
                height: 8mm;
                width: auto;
                object-fit: contain;
                display: block;
                margin: 0 auto 4mm auto;
                visibility: visible !important;
              }
              .header {
                text-align: center;
                margin-bottom: 4mm;
              }
              .title {
                font-size: 5mm;
                font-weight: bold;
                margin: 0;
              }
              .date {
                font-size: 3mm;
                color: #666;
                margin: 1mm 0 0 0;
              }
              .info {
                margin-bottom: 3mm;
              }
              .label {
                font-size: 3mm;
                color: #666;
                margin: 0;
              }
              .value {
                font-size: 3.5mm;
                margin: 1mm 0 0 0;
              }
              .amount {
                font-size: 6mm;
                font-weight: bold;
                margin: 1mm 0 0 0;
              }
              .signature {
                margin-top: 8mm;
                padding-top: 8mm;
                border-top: 1px solid #ddd;
              }
              .signature-line {
                border-bottom: 1px solid #666;
                margin-bottom: 2mm;
              }
              .signature-label {
                text-align: center;
                font-size: 3mm;
                color: #666;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <img src="${jt}" alt="Logo" class="logo" />
              
              <div class="header">
                <h2 class="title">Comprovante de Resgate</h2>
                <p class="date">${new Date().toLocaleDateString("pt-BR")}</p>
              </div>
              
              <div class="info">
                <p class="label">Vale-Presente:</p>
                <p class="value">${(c==null?void 0:c.code)||"N/A"}</p>
              </div>
              
              <div class="info">
                <p class="label">Valor Resgatado:</p>
                <p class="amount">R$ ${i}</p>
              </div>
              
              <div class="signature">
                <div class="signature-line"></div>
                <p class="signature-label">Assinatura</p>
              </div>
            </div>
          </body>
        </html>
      `),a.document.close(),a.focus(),setTimeout(()=>{a.print(),a.close()},500)}},i=typeof(c==null?void 0:c.value)=="number"?c.value.toFixed(2):"0.00";return console.log("RedemptionReceipt received giftCard:",c),w.jsxs("div",{children:[w.jsxs("button",{onClick:s,className:"mb-4 flex items-center space-x-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:[w.jsx(Pi,{className:"h-5 w-5"}),w.jsx("span",{children:"Imprimir Comprovante"})]}),w.jsx("div",{className:"flex justify-center",children:w.jsxs("div",{id:"redemption-receipt",className:"w-[302px] p-4 bg-white print:w-[80mm] print:p-2 print:border-none print:shadow-none print-content print:text-black",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:jt,alt:"Logo",style:{height:"28px",width:"auto",objectFit:"contain",visibility:"visible"}})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-black text-lg print:text-black",style:{color:"black !important"},children:"Comprovante de Resgate"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[w.jsxs("div",{children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Data:"}),w.jsx("p",{className:"font-medium text-base print:text-black",style:{color:"black !important"},children:new Date().toLocaleDateString("pt-BR")})]}),w.jsxs("div",{className:"text-right",children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Valor:"}),w.jsxs("p",{className:"font-bold text-2xl",style:{color:"black !important"},children:["R$ ",i]})]})]}),w.jsxs("div",{className:"text-center mb-3",children:[w.jsx("p",{className:"text-sm print:text-black",style:{color:"black !important"},children:"Vale-Presente:"}),w.jsx("p",{className:"font-mono tracking-wider text-lg print:text-black",style:{color:"black !important"},children:(c==null?void 0:c.code)||"N/A"})]}),w.jsx("div",{className:"mt-8 pt-4 border-t border-gray-200",children:w.jsx("p",{className:"text-center text-sm text-gray-600",style:{color:"black !important"},children:"Assinatura"})})]})})]})}function fg({onRedeem:c}){const[s,i]=se.useState(""),[l,a]=se.useState(null),[d,f]=se.useState(null),[m,p]=se.useState(!1),g=async y=>{y.preventDefault(),a(null),f(null),p(!0);try{const E=await c(s);E.success&&E.giftCard?(f(E.giftCard),i("")):a(E.message||"Vale-presente inválido ou já utilizado")}catch(E){console.error("Error redeeming gift card:",E),a("Erro ao resgatar vale-presente. Tente novamente.")}finally{p(!1)}};return w.jsxs("div",{className:"space-y-8",children:[d&&w.jsx("div",{className:"w-full bg-white p-6 rounded-lg shadow-md",children:w.jsx(dg,{giftCard:d})}),w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Resgatar Vale-Presente"}),w.jsxs("form",{onSubmit:g,className:"space-y-6",children:[w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Código do Vale-Presente"}),w.jsx("input",{type:"text",name:"code",required:!0,className:"form-input",value:s,onChange:y=>i(y.target.value.toUpperCase()),placeholder:"Digite o código manualmente",disabled:m})]}),l&&w.jsx("div",{className:"rounded-md bg-red-50 p-4 border border-red-200",children:w.jsxs("div",{className:"flex",children:[w.jsx(Ym,{className:"h-5 w-5 text-red-500"}),w.jsx("div",{className:"ml-3",children:w.jsx("p",{className:"text-sm text-red-700",children:l})})]})}),w.jsxs("button",{type:"submit",className:"primary-button mt-4",disabled:m,children:[w.jsx(Ud,{className:"h-5 w-5"}),w.jsx("span",{children:m?"Resgatando...":"Resgatar Vale-Presente"})]})]})]})]})}function Fd({giftCards:c=[],isBulk:s=!1,isFromVenderTab:i=!1}){if(!c||c.length===0)return null;const l=()=>{const p=new Date;let g=p.getMonth()+2,y=p.getFullYear();return g>11&&(g=g-12,y+=1),`${new Date(y,g+1,0).getDate().toString().padStart(2,"0")}/${(g+1).toString().padStart(2,"0")}/${y}`},a=(p,g)=>{const y=O=>{const I=document.createElement("canvas");return I.width=O.width,I.height=O.height,I.getContext("2d").drawImage(O,0,0),I.toDataURL("image/png")},E=new Image;E.src=jt;let P=jt;if(E.complete)try{P=y(E)}catch(O){console.error("Error converting logo to data URL",O)}return`
      <div style="
        width: ${g?"226px":"302px"}; 
        padding: ${g?"0.75rem":"1rem"}; 
        background-color: white;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      ">
        <!-- Header with logo and title -->
        <div style="display: flex; align-items: center; margin-bottom: 0.75rem;">
          <div style="flex-shrink: 0; width: 50%;">
            <img 
              src="${P}"
              alt="Logo" 
              style="height: ${g?"20px":"28px"}; width: auto; object-fit: contain;"
            />
          </div>
          <div style="flex-grow: 1; text-align: right;">
            <h2 style="
              font-weight: 700; 
              white-space: nowrap; 
              font-size: ${g?"0.875rem":"1.125rem"}; 
              margin: 0; 
              color: ${g?"#dc2626":"black"};
            ">
              Vale-Presente
            </h2>
          </div>
        </div>
        
        <!-- Recipient and Value -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
          ${p.recipientName?`
            <div>
              <p style="font-size: ${g?"0.75rem":"0.875rem"}; margin: 0; color: ${g?"#4b5563":"black"};">Para:</p>
              <p style="font-weight: 500; font-size: ${g?"0.875rem":"1rem"}; margin: 0; color: ${g?"#1f2937":"black"}">
                ${p.recipientName}
              </p>
            </div>
          `:""}
          <div style="text-align: ${p.recipientName?"right":"center"}; ${p.recipientName?"":"width: 100%;"}">
            <p style="font-size: ${g?"0.75rem":"0.875rem"}; margin: 0; color: ${g?"#4b5563":"black"};">Valor:</p>
            <p style="font-weight: 700; font-size: ${g?"1.25rem":"1.5rem"}; margin: 0; color: ${g?"#dc2626":"black"}">
              R$ ${p.value.toFixed(2)}
            </p>
          </div>
        </div>

        <!-- QR Code -->
        <div style="display: flex; justify-content: center; margin-bottom: 0.5rem;">
          <img src="https://api.qrserver.com/v1/create-qr-code/?size=${g?100:130}&data=${encodeURIComponent(p.code)}" 
               style="width: ${g?"100px":"130px"}; height: ${g?"100px":"130px"};" 
               alt="QR Code" />
        </div>

        <!-- Code -->
        <div style="text-align: center;">
          <p style="
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, 'Courier New', monospace; 
            letter-spacing: 0.05em; 
            font-size: ${g?"1rem":"1.125rem"}; 
            margin: 0; 
            color: ${g?"#1f2937":"black"};
          ">
            ${p.code}
          </p>
        </div>

        <div style="margin-top: 0.75rem; text-align: center; color: ${g?"#6b7280":"black"};">
          <p style="font-weight: 500; font-size: ${g?"0.75rem":"0.875rem"}; margin: 0;">Um presente de: ${p.buyerName}</p>
          <p style="margin-top: 0.25rem; font-size: ${g?"0.625rem":"0.75rem"}; margin: 0;">
            Válido até ${l()}
          </p>
        </div>
      </div>
    `},d=()=>{f(),i&&!s&&c.length===1&&setTimeout(()=>{m(c[0])},1e3)},f=()=>{const p=window.open("","_blank");if(p){const g=`
        <html>
          <head>
            <title>${s?"Lote de Vale-Presentes":"Vale-Presente"}</title>
            <style>
              @media print {
                @page {
                  ${s?"size: A4; margin: 10mm;":"size: 80mm auto; margin: 0;"}
                }
                body {
                  margin: 0;
                  padding: ${s?"10mm":"4mm"};
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                }
                * {
                  print-color-adjust: exact !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  text-rendering: optimizeLegibility !important;
                }
              }
              
              /* Reset CSS to ensure consistent rendering */
              * {
                box-sizing: border-box;
                margin: 0;
                padding: 0;
              }
              
              body {
                font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
              }
            </style>
          </head>
          <body>
            <div class="container">
              ${s?`
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 16px;">
                  ${c.map(y=>`
                    <div style="margin-bottom: 16px;">
                      ${a(y,!0)}
                    </div>
                  `).join("")}
                </div>
              `:`
                <div style="${i?"":"display: flex; justify-content: center;"}">
                  ${a(c[0],!1)}
                </div>
              `}
            </div>
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;p.document.write(g),p.document.close()}},m=p=>{const g=window.open("","_blank");if(g){const y=new Date,E=y.toLocaleDateString("pt-BR"),P=y.toLocaleTimeString("pt-BR"),I=(U=>U.length<=6?U:U.substring(0,3)+"*".repeat(U.length-6)+U.substring(U.length-3))(p.code),F=U=>{const q=document.createElement("canvas");return q.width=U.width,q.height=U.height,q.getContext("2d").drawImage(U,0,0),q.toDataURL("image/png")},D=new Image;D.src=jt;let ne=jt;if(D.complete)try{ne=F(D)}catch(U){console.error("Error converting logo to data URL",U)}const ae=`
        <html>
          <head>
            <title>Controle Interno - Vale-Presente</title>
            <style>
              @media print {
                @page {
                  size: 80mm auto;
                  margin: 0;
                }
                body {
                  margin: 0;
                  padding: 4mm;
                }
              }
              
              body {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                line-height: 1.2;
              }
              
              .receipt {
                width: 72mm;
              }
              
              .header {
                text-align: center;
                margin-bottom: 10px;
              }
              
              .divider {
                border-top: 1px dashed #000;
                margin: 10px 0;
              }
              
              .info-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
              }
              
              .footer {
                text-align: center;
                margin-top: 10px;
                font-size: 10px;
              }
            </style>
          </head>
          <body>
            <div class="receipt">
              <div class="header">
                <img src="${ne}" alt="Logo" style="height: 30px; margin-bottom: 5px;" />
                <h3 style="margin: 5px 0;">CONTROLE INTERNO - VALE-PRESENTE</h3>
              </div>
              
              <div class="divider"></div>
              
              <div class="info-row">
                <span>Data:</span>
                <span>${E}</span>
              </div>
              
              <div class="info-row">
                <span>Hora:</span>
                <span>${P}</span>
              </div>
              
              <div class="info-row">
                <span>Código:</span>
                <span>${I}</span>
              </div>
              
              <div class="info-row">
                <span>Valor:</span>
                <span>R$ ${p.value.toFixed(2)}</span>
              </div>
              
              ${p.recipientName?`
              <div class="info-row">
                <span>Destinatário:</span>
                <span>${p.recipientName}</span>
              </div>
              `:""}
              
              <div class="info-row">
                <span>Comprador:</span>
                <span>${p.buyerName}</span>
              </div>
              
              <div class="divider"></div>
              
              <p style="text-align: center; margin: 10px 0;">Válido até ${l()}</p>
              
              <div class="divider"></div>
              
              <div class="footer">
                <p>CONTROLE INTERNO - NÃO ENTREGAR AO CLIENTE</p>
              </div>
            </div>
            
            <script>
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                  setTimeout(function() {
                    window.close();
                  }, 500);
                }, 300);
              };
            <\/script>
          </body>
        </html>
      `;g.document.write(ae),g.document.close()}};return w.jsxs("div",{children:[w.jsxs("button",{onClick:d,className:"mb-4 flex items-center gap-2 px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors",children:[w.jsx(Pi,{size:20}),"Imprimir Vale-Presente",c.length>1?"s":""]}),w.jsx("div",{className:"print:m-0",children:s?w.jsx("div",{className:"grid grid-cols-3 gap-4",children:c.map(p=>w.jsx("div",{className:"mb-4 print:mb-0",children:w.jsxs("div",{className:"w-[226px] p-3 bg-white",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:jt,alt:"Logo",className:"h-5 w-auto object-contain"})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-red-600 whitespace-nowrap text-sm",children:"Vale-Presente"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[p.recipientName&&w.jsxs("div",{children:[w.jsx("p",{className:"text-gray-600 text-xs",children:"Para:"}),w.jsx("p",{className:"font-medium text-gray-800 text-sm",children:p.recipientName})]}),w.jsxs("div",{className:`text-${p.recipientName?"right":"center"} ${p.recipientName?"":"w-full"}`,children:[w.jsx("p",{className:"text-gray-600 text-xs",children:"Valor:"}),w.jsxs("p",{className:"font-bold text-red-600 text-xl",children:["R$ ",p.value.toFixed(2)]})]})]}),w.jsx("div",{className:"flex justify-center mb-2",children:w.jsx("img",{src:`https://api.qrserver.com/v1/create-qr-code/?size=100&data=${encodeURIComponent(p.code)}`,alt:"QR Code",className:"w-[100px] h-[100px]"})}),w.jsx("div",{className:"text-center",children:w.jsx("p",{className:"font-mono tracking-wider text-gray-800 text-base",children:p.code})}),w.jsxs("div",{className:"mt-3 text-center text-gray-500",children:[w.jsxs("p",{className:"font-medium text-xs",children:["Um presente de: ",p.buyerName]}),w.jsxs("p",{className:"mt-1 text-[10px]",children:["Válido até ",l()]})]})]})},p.id||p.code))}):w.jsx("div",{className:"flex justify-center",children:w.jsxs("div",{className:"w-[302px] p-4 bg-white",children:[w.jsxs("div",{className:"flex items-center mb-3",children:[w.jsx("div",{className:"flex-shrink-0 w-1/2",children:w.jsx("img",{src:jt,alt:"Logo",className:"h-7 w-auto object-contain"})}),w.jsx("div",{className:"flex-grow text-right",children:w.jsx("h2",{className:"font-bold text-black whitespace-nowrap text-lg",children:"Vale-Presente"})})]}),w.jsxs("div",{className:"flex justify-between items-center mb-3",children:[c[0].recipientName&&w.jsxs("div",{children:[w.jsx("p",{className:"text-sm",children:"Para:"}),w.jsx("p",{className:"font-medium text-base",children:c[0].recipientName})]}),w.jsxs("div",{className:`text-${c[0].recipientName?"right":"center"} ${c[0].recipientName?"":"w-full"}`,children:[w.jsx("p",{className:"text-sm",children:"Valor:"}),w.jsxs("p",{className:"font-bold text-2xl",children:["R$ ",c[0].value.toFixed(2)]})]})]}),w.jsx("div",{className:"flex justify-center mb-2",children:w.jsx("img",{src:`https://api.qrserver.com/v1/create-qr-code/?size=130&data=${encodeURIComponent(c[0].code)}`,alt:"QR Code",className:"w-[130px] h-[130px]"})}),w.jsx("div",{className:"text-center",children:w.jsx("p",{className:"font-mono tracking-wider text-lg",children:c[0].code})}),w.jsxs("div",{className:"mt-3 text-center",children:[w.jsxs("p",{className:"font-medium text-sm",children:["Um presente de: ",c[0].buyerName]}),w.jsxs("p",{className:"mt-1 text-xs",children:["Válido até ",l()]})]})]})})})]})}function hg({onGenerate:c}){const[s,i]=se.useState({quantity:"2",value:"",buyerName:""}),[l,a]=se.useState([]),[d,f]=se.useState("0.00"),[m,p]=se.useState(!1),[g,y]=se.useState(null),E=async I=>{I.preventDefault(),y(null);const F=parseInt(s.quantity,10);if(F<2||F>100){y("A quantidade deve estar entre 2 e 100");return}if(!s.value||!s.buyerName){y("Preencha todos os campos obrigatórios");return}try{p(!0);const D=P(),ne=[];for(let ae=0;ae<F;ae++){const U={id:Ni(),code:O(D),value:Number(s.value),recipientName:"",buyerName:s.buyerName,createdAt:new Date,used:!1,isBulkGenerated:!0};ne.push(U)}c(ne),f(s.value),i({...s,quantity:"2",value:"",buyerName:""}),a(ne)}catch(D){console.error("Error generating bulk cards:",D),y("Erro ao gerar vale-presentes em lote")}finally{p(!1)}},P=()=>{let I;do I=Math.floor(Math.random()*900+100).toString();while(I.startsWith("N"));return I},O=I=>{const F=Ni().replace(/-/g,"").substring(0,13).toUpperCase();return`${I}${F}`.substring(0,16)};return w.jsxs("div",{className:"space-y-8",children:[l.length>0&&w.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-4 text-center",children:"Vale-Presentes Gerados"}),w.jsx("div",{className:"mb-4 p-4 bg-gray-50 rounded-md border border-gray-200",children:w.jsxs("p",{className:"text-sm text-gray-600",children:["Foram gerados ",w.jsx("span",{className:"font-medium",children:l.length})," vale-presentes no valor de ",w.jsxs("span",{className:"font-medium",children:["R$ ",Number(d).toFixed(2)]})," cada."]})}),w.jsx(Fd,{giftCards:l,isBulk:!0})]}),w.jsxs("div",{className:"w-full max-w-md mx-auto bg-white p-6 rounded-lg shadow-md",children:[w.jsx("h2",{className:"section-header",children:"Gerar em Lote"}),w.jsxs("form",{onSubmit:E,className:"space-y-6",children:[g&&w.jsx("div",{className:"p-4 text-sm text-red-600 bg-red-50 rounded-md border border-red-200",children:g}),m&&w.jsx("div",{className:"p-4 text-sm text-blue-600 bg-blue-50 rounded-md border border-blue-200",children:"Gerando vale-presentes..."}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Quantidade"}),w.jsx("input",{type:"number",min:"2",max:"100",value:s.quantity,onChange:I=>i({...s,quantity:I.target.value}),className:"form-input",placeholder:"Quantidade de vale-presentes a gerar"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Valor (R$)"}),w.jsx("input",{type:"number",step:"0.01",required:!0,value:s.value,onChange:I=>i({...s,value:I.target.value}),className:"form-input",placeholder:"0.00"})]}),w.jsxs("div",{className:"space-y-1",children:[w.jsx("label",{className:"form-label",children:"Nome do Comprador"}),w.jsx("input",{type:"text",required:!0,value:s.buyerName,onChange:I=>i({...s,buyerName:I.target.value}),className:"form-input",placeholder:"Nome de quem está comprando"})]}),w.jsxs("button",{type:"submit",disabled:m,className:"primary-button mt-4",children:[w.jsx(zd,{className:"h-5 w-5"}),w.jsx("span",{children:m?"Gerando...":"Gerar Vale-Presentes"})]})]})]})]})}const Vd=se.createContext(void 0);function pg({children:c}){const[s,i]=se.useState(()=>localStorage.getItem("isAuthenticated")==="true");se.useEffect(()=>{localStorage.setItem("isAuthenticated",s.toString())},[s]);const l=d=>d==="gerente"?(i(!0),!0):!1,a=()=>{i(!1)};return w.jsx(Vd.Provider,{value:{isAuthenticated:s,login:l,logout:a},children:c})}function Bd(){const c=se.useContext(Vd);if(c===void 0)throw new Error("useAuth must be used within an AuthProvider");return c}function mg({isOpen:c,onClose:s}){const[i,l]=se.useState(""),[a,d]=se.useState(null),{isAuthenticated:f,login:m,logout:p}=Bd(),g=y=>{if(y.preventDefault(),d(null),f){p(),s();return}if(!i.trim()){d("Por favor, insira a senha");return}m(i)?(l(""),s()):d("Senha incorreta")};return c?w.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:w.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-lg max-w-md w-full",children:[w.jsxs("div",{className:"flex justify-between items-center mb-4",children:[w.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:f?"Bloquear Acesso":"Acesso Restrito"}),w.jsx("button",{onClick:s,className:"text-gray-500 hover:text-gray-700",children:w.jsx(rg,{className:"h-5 w-5"})})]}),a&&w.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm",children:a}),w.jsxs("form",{onSubmit:g,children:[f?w.jsx("div",{className:"mb-4 p-3 bg-yellow-50 text-yellow-700 rounded-md",children:w.jsx("p",{children:"Você tem acesso às funcionalidades restritas. Deseja bloquear o acesso?"})}):w.jsxs("div",{className:"mb-4",children:[w.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Senha"}),w.jsx("input",{type:"password",id:"password",value:i,onChange:y=>l(y.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500",placeholder:"Digite a senha",autoFocus:!0})]}),w.jsx("div",{className:"flex justify-end",children:w.jsx("button",{type:"submit",className:`flex items-center space-x-2 px-4 py-2 rounded-md ${f?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-red-600 hover:bg-red-700 text-white"}`,children:f?w.jsxs(w.Fragment,{children:[w.jsx(Ci,{className:"h-4 w-4"}),w.jsx("span",{children:"Bloquear Acesso"})]}):w.jsxs(w.Fragment,{children:[w.jsx(Md,{className:"h-4 w-4"}),w.jsx("span",{children:"Desbloquear"})]})})})]})]})}):null}class gg extends se.Component{constructor(s){super(s),this.state={hasError:!1,error:null}}static getDerivedStateFromError(s){return{hasError:!0,error:s}}componentDidCatch(s,i){console.error("Uncaught error:",s,i)}render(){return this.state.hasError?w.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:w.jsx("div",{className:"max-w-md w-full p-6 bg-white rounded-lg shadow-lg",children:w.jsxs("div",{className:"text-center",children:[w.jsx("h2",{className:"text-xl font-bold text-red-600 mb-4",children:"Ops! Algo deu errado."}),w.jsx("p",{className:"text-gray-600 mb-4",children:"Ocorreu um erro ao processar sua solicitação."}),this.state.error&&w.jsx("pre",{className:"mt-4 p-4 bg-gray-100 rounded text-sm text-gray-700 overflow-auto",children:this.state.error.message}),w.jsx("button",{onClick:()=>window.location.reload(),className:"mt-6 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Recarregar Página"})]})})}):this.props.children}}function vg(){const[c,s]=se.useState([]),[i,l]=se.useState("sell"),[a,d]=se.useState(!1),[f,m]=se.useState(null),[p,g]=se.useState(null),[y,E]=se.useState(!1),{isAuthenticated:P}=Bd();se.useEffect(()=>{O()},[]);const O=async()=>{m(null),d(!0);try{const{data:U,error:q}=await Si.from("gift_cards").select("*").order("created_at",{ascending:!1});if(q)throw q;if(U){const $=U.map(A=>({id:A.id,code:A.code,value:A.value,recipientName:A.recipient_name,buyerName:A.buyer_name,createdAt:new Date(A.created_at),used:A.used,usedAt:A.used_at?new Date(A.used_at):null,isBulkGenerated:!A.code.startsWith("N")&&A.code.length===16}));s($)}}catch(U){console.error("Error fetching gift cards:",U),m("Erro ao carregar os vale-presentes. Por favor, tente novamente.")}finally{d(!1)}},I=async U=>{m(null),d(!0);try{const{error:q}=await Si.from("gift_cards").insert({id:U.id,code:U.code,value:U.value,recipient_name:U.recipientName,buyer_name:U.buyerName,created_at:U.createdAt.toISOString(),used:!1});if(q)throw q;return s($=>[U,...$]),g(U),!0}catch(q){throw console.error("Error selling gift card:",q),m("Erro ao salvar vale-presente"),q}finally{d(!1)}},F=async U=>{m(null),d(!0);try{const q=U.map(A=>({id:A.id,code:A.code,value:A.value,recipient_name:A.recipientName,buyer_name:A.buyerName,created_at:A.createdAt.toISOString(),used:!1})),{error:$}=await Si.from("gift_cards").insert(q);if($)throw $;return s(A=>[...U,...A]),O(),U.length>0&&g(U[0]),!0}catch(q){throw console.error("Error generating bulk gift cards:",q),m("Erro ao gerar vale-presentes em lote"),q}finally{d(!1)}},D=async U=>{m(null),d(!0);try{const q=c.find(oe=>oe.code===U);if(!q)return console.log("Gift card not found with code:",U),{success:!1,message:"Vale-presente não encontrado."};if(q.used)return console.log("Gift card already used:",q),{success:!1,message:"Este vale-presente já foi utilizado."};const $=new Date,{error:A}=await Si.from("gift_cards").update({used:!0,used_at:$.toISOString()}).eq("code",U);if(A)throw console.error("Error updating gift card in database:",A),A;s(oe=>oe.map(pe=>pe.code===U?{...pe,used:!0,usedAt:$}:pe));const X={...q,used:!0,usedAt:$};return console.log("Successfully redeemed gift card:",X),{success:!0,message:"Vale-presente resgatado com sucesso!",giftCard:X}}catch(q){throw console.error("Error redeeming gift card:",q),m("Erro ao resgatar o vale-presente. Por favor, tente novamente."),q}finally{d(!1)}},ne=U=>{l(U),g(null)},ae=()=>{E(!y)};return w.jsx(gg,{children:w.jsxs("div",{className:"min-h-screen bg-gray-50",children:[f&&w.jsx("div",{className:"mb-4 p-4 bg-red-50 text-red-600 rounded-md",children:f}),w.jsx("header",{className:"bg-white shadow-sm",children:w.jsx("div",{className:"max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8",children:w.jsxs("div",{className:"flex justify-between items-center mb-8",children:[w.jsx("div",{className:"flex-shrink-0",children:w.jsx("img",{src:jt,alt:"New Look Logo",className:"h-16"})}),w.jsxs("div",{className:"flex items-center",children:[w.jsx("h1",{className:"text-2xl font-bold text-gray-800 mr-4",children:"Sistema de Vale-Presentes"}),w.jsx("button",{onClick:ae,className:"p-2 rounded-full hover:bg-gray-100 transition-colors duration-200",title:P?"Bloquear acesso":"Desbloquear acesso",children:P?w.jsx(Md,{className:"h-5 w-5 text-green-600"}):w.jsx(Ci,{className:"h-5 w-5 text-gray-500"})})]})]})})}),w.jsxs("main",{className:"max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8",children:[w.jsx(sg,{activeTab:i,onTabChange:ne,isAuthenticated:P}),p&&i==="sell"&&w.jsxs("div",{className:"mb-8 bg-white shadow-sm rounded-lg p-6",children:[w.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-4 text-center",children:"Vale-Presente Gerado"}),w.jsx(Fd,{giftCards:[p],isFromVenderTab:!0})]}),w.jsxs("div",{className:"mt-6 bg-white shadow-sm rounded-lg p-6",children:[a&&w.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:w.jsx("div",{className:"bg-white p-4 rounded-md shadow-lg",children:w.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"})})}),i==="sell"&&w.jsx(ug,{onSell:I}),i==="bulk"&&P&&w.jsx(hg,{onGenerate:F}),i==="list"&&w.jsx(cg,{giftCards:c,isAuthenticated:P}),i==="redeem"&&w.jsx(fg,{onRedeem:D})]})]}),w.jsx(mg,{isOpen:y,onClose:()=>E(!1)})]})})}function yg(){return w.jsx(pg,{children:w.jsx(vg,{})})}Yh.createRoot(document.getElementById("root")).render(w.jsx(se.StrictMode,{children:w.jsx(yg,{})}));export{Fh as g};
//# sourceMappingURL=index-BexKckDt.js.map
