{"version": 3, "file": "browser-BcsEyRPb.js", "sources": ["../../node_modules/ws/browser.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function () {\n  throw new Error(\n    'ws does not work in the browser. Browser clients must use the native ' +\n      'WebSocket object'\n  );\n};\n"], "names": ["browser"], "mappings": "4EAEAA,EAAiB,UAAY,CAC3B,MAAM,IAAI,MACR,uFAED,CACF", "x_google_ignoreList": [0]}