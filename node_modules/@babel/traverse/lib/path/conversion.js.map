{"version": 3, "names": ["_t", "require", "_template", "_visitors", "_context", "arrowFunctionExpression", "assignmentExpression", "binaryExpression", "blockStatement", "callExpression", "conditionalExpression", "expressionStatement", "identifier", "isIdentifier", "jsxIdentifier", "logicalExpression", "LOGICAL_OPERATORS", "memberExpression", "metaProperty", "numericLiteral", "objectExpression", "restElement", "returnStatement", "sequenceExpression", "spreadElement", "stringLiteral", "super", "_super", "thisExpression", "toExpression", "unaryExpression", "toBindingIdentifierName", "isFunction", "isAssignmentPattern", "isRestElement", "getFunctionName", "cloneNode", "variableDeclaration", "variableDeclarator", "exportNamedDeclaration", "exportSpecifier", "inherits", "toCom<PERSON><PERSON>ey", "key", "isMemberExpression", "node", "property", "isProperty", "isMethod", "ReferenceError", "computed", "name", "ensureBlock", "body", "get", "bodyNode", "Array", "isArray", "Error", "isBlockStatement", "statements", "stringPath", "<PERSON><PERSON><PERSON>", "isStatement", "push", "parentPath", "setup", "call", "exports", "arrowFunctionToShadowed", "isArrowFunctionExpression", "arrowFunctionToExpression", "unwrapFunctionEnvironment", "isFunctionExpression", "isFunctionDeclaration", "buildCodeFrameError", "hoistFunctionEnvironment", "setType", "path", "type", "allowInsertArrow", "allowInsertArrowWithRest", "noNewArrows", "_arguments$", "arguments", "specCompliant", "self", "_self$ensureFunctionN", "ensureFunctionName", "thisBinding", "fnPath", "fn", "checkBinding", "scope", "generateUidIdentifier", "id", "init", "unshiftContainer", "hub", "addHelper", "replaceWith", "getSuperCallsVisitor", "environmentVisitor", "CallExpression", "child", "allSuperCalls", "is<PERSON><PERSON><PERSON>", "arrowParent", "thisEnvFn", "findParent", "p", "_arrowParent", "isProgram", "isClassProperty", "static", "isClassPrivateProperty", "inConstructor", "isClassMethod", "kind", "thisPaths", "argumentsPaths", "newTargetPaths", "superProps", "superCalls", "getScopeInformation", "length", "traverse", "superBinding", "getSuper<PERSON><PERSON><PERSON>", "for<PERSON>ach", "superCall", "callee", "loc", "argumentsBinding", "getBinding", "args", "buildUndefinedNode", "<PERSON><PERSON><PERSON><PERSON>", "argsRef", "newTargetBinding", "targetChild", "targetRef", "flatSuperProps", "reduce", "acc", "superProp", "concat", "standardizeSuperProperty", "superParent<PERSON>ath", "isAssignment", "isAssignmentExpression", "left", "isCall", "isCallExpression", "isTaggedTemplate", "isTaggedTemplateExpression", "tag", "getSuperPropBinding", "value", "right", "getThis<PERSON><PERSON>ing", "hasSuperClass", "<PERSON><PERSON><PERSON><PERSON>", "thisRef", "isJSX", "isLogicalOp", "op", "includes", "operator", "assignment<PERSON><PERSON>", "slice", "isLogicalAssignment", "tmp", "generateDeclaredUidIdentifier", "object", "rightExpression", "isUpdateExpression", "updateExpr", "computedKey", "parts", "prefix", "superClass", "assignSuperThisVisitor", "supers", "has", "add", "replaceWithMultiple", "WeakSet", "args<PERSON><PERSON><PERSON>", "propName", "argsList", "fnBody", "method", "unshift", "valueIdent", "cache<PERSON>ey", "data", "getData", "setData", "getScopeInformationVisitor", "ThisExpression", "JSXIdentifier", "isJSXMemberExpression", "isJSXOpeningElement", "MemberExpression", "Identifier", "isReferencedIdentifier", "curr", "hasOwnBinding", "rename", "parent", "MetaProperty", "splitExportDeclaration", "isExportDeclaration", "isExportAllDeclaration", "isExportNamedDeclaration", "declaration", "isExportDefaultDeclaration", "standaloneDeclaration", "isClassDeclaration", "exportExpr", "isClassExpression", "isScope", "needBindingRegistration", "hasBinding", "updatedDeclaration", "updatedExportDeclaration", "insertAfter", "registerDeclaration", "bindingIdentifiers", "getOuterBindingIdentifiers", "specifiers", "Object", "keys", "map", "aliasDeclar", "refersOuterBindingVisitor", "ReferencedIdentifier|BindingIdentifier", "state", "<PERSON><PERSON><PERSON><PERSON>", "stop", "<PERSON><PERSON>", "skip", "supportUnicodeId", "res", "test", "startsWith", "replace", "originalNode", "binding", "getOwnBinding", "hasGlobal", "getProgramParent", "references", "params", "i", "len", "getFunctionArity", "template", "expression", "ast", "count", "findIndex", "param"], "sources": ["../../src/path/conversion.ts"], "sourcesContent": ["// This file contains methods that convert the path node into another node or some other type of data.\n\nimport {\n  arrowFunctionExpression,\n  assignmentExpression,\n  binaryExpression,\n  blockStatement,\n  callExpression,\n  conditionalExpression,\n  expressionStatement,\n  identifier,\n  isIdentifier,\n  jsxIdentifier,\n  logicalExpression,\n  LOGICAL_OPERATORS,\n  memberExpression,\n  metaProperty,\n  numericLiteral,\n  objectExpression,\n  restElement,\n  returnStatement,\n  sequenceExpression,\n  spreadElement,\n  stringLiteral,\n  super as _super,\n  thisExpression,\n  toExpression,\n  unaryExpression,\n  toBindingIdentifierName,\n  isFunction,\n  isAssignmentPattern,\n  isRestElement,\n  getFunctionName,\n  cloneNode,\n  variableDeclaration,\n  variableDeclarator,\n  exportNamedDeclaration,\n  exportSpecifier,\n  inherits,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport template from \"@babel/template\";\nimport { environmentVisitor } from \"../visitors.ts\";\nimport type NodePath from \"./index.ts\";\nimport type { Visitor } from \"../types.ts\";\nimport { setup } from \"./context.ts\";\n\nexport function toComputedKey(this: NodePath) {\n  let key;\n  if (this.isMemberExpression()) {\n    key = this.node.property;\n  } else if (this.isProperty() || this.isMethod()) {\n    key = this.node.key;\n  } else {\n    throw new ReferenceError(\"todo\");\n  }\n\n  // @ts-expect-error todo(flow->ts) computed does not exist in ClassPrivateProperty\n  if (!this.node.computed) {\n    if (isIdentifier(key)) key = stringLiteral(key.name);\n  }\n\n  return key;\n}\n\nexport function ensureBlock(\n  this: NodePath<\n    t.Loop | t.WithStatement | t.Function | t.LabeledStatement | t.CatchClause\n  >,\n): void {\n  const body = this.get(\"body\");\n  const bodyNode = body.node;\n\n  if (Array.isArray(body)) {\n    throw new Error(\"Can't convert array path to a block statement\");\n  }\n  if (!bodyNode) {\n    throw new Error(\"Can't convert node without a body\");\n  }\n\n  if (body.isBlockStatement()) {\n    // @ts-expect-error TS throws because ensureBlock returns the body node path\n    // however, we don't use the return value and treat it as a transform and\n    // assertion utilities. For better type inference we annotate it as an\n    // assertion method\n    // TODO: Unify the implementation with the type definition\n    return bodyNode;\n  }\n\n  const statements: Array<t.Statement> = [];\n\n  let stringPath = \"body\";\n  let key;\n  let listKey;\n  if (body.isStatement()) {\n    listKey = \"body\";\n    key = 0;\n    statements.push(body.node);\n  } else {\n    stringPath += \".body.0\";\n    if (this.isFunction()) {\n      key = \"argument\";\n      statements.push(returnStatement(body.node as t.Expression));\n    } else {\n      key = \"expression\";\n      statements.push(expressionStatement(body.node as t.Expression));\n    }\n  }\n\n  this.node.body = blockStatement(statements);\n  const parentPath = this.get(stringPath) as NodePath;\n  setup.call(\n    body,\n    parentPath,\n    listKey\n      ? // @ts-expect-error listKey must present in parent path\n        parentPath.node[listKey]\n      : parentPath.node,\n    listKey,\n    key,\n  );\n\n  // @ts-expect-error TS throws because ensureBlock returns the body node path\n  // however, we don't use the return value and treat it as a transform and\n  // assertion utilities. For better type inference we annotate it as an\n  // assertion method\n  // TODO: Unify the implementation with the type definition\n  return this.node;\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * Keeping this for backward-compatibility. You should use arrowFunctionToExpression() for >=7.x.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.arrowFunctionToShadowed = function (this: NodePath) {\n    if (!this.isArrowFunctionExpression()) return;\n\n    this.arrowFunctionToExpression();\n  };\n}\n\n/**\n * Given an arbitrary function, process its content as if it were an arrow function, moving references\n * to \"this\", \"arguments\", \"super\", and such into the function's parent scope. This method is useful if\n * you have wrapped some set of items in an IIFE or other function, but want \"this\", \"arguments\", and super\"\n * to continue behaving as expected.\n */\nexport function unwrapFunctionEnvironment(this: NodePath) {\n  if (\n    !this.isArrowFunctionExpression() &&\n    !this.isFunctionExpression() &&\n    !this.isFunctionDeclaration()\n  ) {\n    throw this.buildCodeFrameError(\n      \"Can only unwrap the environment of a function.\",\n    );\n  }\n\n  hoistFunctionEnvironment(this);\n}\n\nfunction setType<N extends t.Node, T extends N[\"type\"]>(\n  path: NodePath<N>,\n  type: T,\n): asserts path is NodePath<Extract<N, { type: T }>> {\n  path.node.type = type;\n}\n\n/**\n * Convert a given arrow function into a normal ES5 function expression.\n */\nexport function arrowFunctionToExpression(\n  this: NodePath<t.ArrowFunctionExpression>,\n  {\n    allowInsertArrow = true,\n    allowInsertArrowWithRest = allowInsertArrow,\n    noNewArrows = process.env.BABEL_8_BREAKING\n      ? // TODO(Babel 8): Consider defaulting to `false` for spec compliance\n        true\n      : !arguments[0]?.specCompliant,\n  }: {\n    allowInsertArrow?: boolean | void;\n    allowInsertArrowWithRest?: boolean | void;\n    noNewArrows?: boolean;\n  } = {},\n): NodePath<\n  Exclude<t.Function, t.Method | t.ArrowFunctionExpression> | t.CallExpression\n> {\n  if (!this.isArrowFunctionExpression()) {\n    throw (this as NodePath).buildCodeFrameError(\n      \"Cannot convert non-arrow function to a function expression.\",\n    );\n  }\n\n  let self = this;\n  if (!noNewArrows) {\n    // @ts-expect-error This is technicallynot valid on arrow functions\n    // because it adds an .id property, but we are going to convert it\n    // to a function expression anyway\n    self = self.ensureFunctionName(false) ?? self;\n  }\n\n  const { thisBinding, fnPath: fn } = hoistFunctionEnvironment(\n    self,\n    noNewArrows,\n    allowInsertArrow,\n    allowInsertArrowWithRest,\n  );\n\n  fn.ensureBlock();\n  setType(fn, \"FunctionExpression\");\n\n  if (!noNewArrows) {\n    const checkBinding = thisBinding\n      ? null\n      : fn.scope.generateUidIdentifier(\"arrowCheckId\");\n    if (checkBinding) {\n      fn.parentPath.scope.push({\n        id: checkBinding,\n        init: objectExpression([]),\n      });\n    }\n\n    fn.get(\"body\").unshiftContainer(\n      \"body\",\n      expressionStatement(\n        callExpression(this.hub.addHelper(\"newArrowCheck\"), [\n          thisExpression(),\n          checkBinding\n            ? identifier(checkBinding.name)\n            : identifier(thisBinding),\n        ]),\n      ),\n    );\n\n    fn.replaceWith(\n      callExpression(memberExpression(fn.node, identifier(\"bind\")), [\n        checkBinding ? identifier(checkBinding.name) : thisExpression(),\n      ]),\n    );\n\n    return fn.get(\"callee.object\");\n  }\n\n  return fn;\n}\n\nconst getSuperCallsVisitor = environmentVisitor<{\n  allSuperCalls: NodePath<t.CallExpression>[];\n}>({\n  CallExpression(child, { allSuperCalls }) {\n    if (!child.get(\"callee\").isSuper()) return;\n    allSuperCalls.push(child);\n  },\n});\n\n/**\n * Given a function, traverse its contents, and if there are references to \"this\", \"arguments\", \"super\",\n * or \"new.target\", ensure that these references reference the parent environment around this function.\n *\n * @returns `thisBinding`: the name of the injected reference to `this`; for example \"_this\"\n * @returns `fnPath`: the new path to the function node. This is different from the fnPath\n *                    parameter when the function node is wrapped in another node.\n */\nfunction hoistFunctionEnvironment(\n  fnPath: NodePath<t.Function>,\n  // TODO(Babel 8): Consider defaulting to `false` for spec compliance\n  noNewArrows: boolean | void = true,\n  allowInsertArrow: boolean | void = true,\n  allowInsertArrowWithRest: boolean | void = true,\n): { thisBinding: string; fnPath: NodePath<t.Function> } {\n  let arrowParent;\n  let thisEnvFn: NodePath<t.Function> = fnPath.findParent(p => {\n    if (p.isArrowFunctionExpression()) {\n      arrowParent ??= p;\n      return false;\n    }\n    return (\n      p.isFunction() ||\n      p.isProgram() ||\n      p.isClassProperty({ static: false }) ||\n      p.isClassPrivateProperty({ static: false })\n    );\n  }) as NodePath<t.Function>;\n  const inConstructor = thisEnvFn.isClassMethod({ kind: \"constructor\" });\n\n  if (thisEnvFn.isClassProperty() || thisEnvFn.isClassPrivateProperty()) {\n    if (arrowParent) {\n      thisEnvFn = arrowParent;\n    } else if (allowInsertArrow) {\n      // It's safe to wrap this function in another and not hoist to the\n      // top level because the 'this' binding is constant in class\n      // properties (since 'super()' has already been called), so we don't\n      // need to capture/reassign it at the top level.\n      fnPath.replaceWith(\n        callExpression(\n          arrowFunctionExpression([], toExpression(fnPath.node)),\n          [],\n        ),\n      );\n      thisEnvFn = fnPath.get(\"callee\") as NodePath<t.ArrowFunctionExpression>;\n      fnPath = thisEnvFn.get(\"body\") as NodePath<t.FunctionExpression>;\n    } else {\n      throw fnPath.buildCodeFrameError(\n        \"Unable to transform arrow inside class property\",\n      );\n    }\n  }\n\n  const { thisPaths, argumentsPaths, newTargetPaths, superProps, superCalls } =\n    getScopeInformation(fnPath);\n\n  // Convert all super() calls in the constructor, if super is used in an arrow.\n  if (inConstructor && superCalls.length > 0) {\n    if (!allowInsertArrow) {\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super()` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    if (!allowInsertArrowWithRest) {\n      // preset-env with target `since 2017` enables `transform-parameters` without `transform-classes`.\n      throw superCalls[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-parameters', \" +\n          \"it's not possible to compile `super()` in an arrow function with default or rest parameters without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n    const allSuperCalls: NodePath<t.CallExpression>[] = [];\n    thisEnvFn.traverse(getSuperCallsVisitor, { allSuperCalls });\n    const superBinding = getSuperBinding(thisEnvFn);\n    allSuperCalls.forEach(superCall => {\n      const callee = identifier(superBinding);\n      callee.loc = superCall.node.callee.loc;\n\n      superCall.get(\"callee\").replaceWith(callee);\n    });\n  }\n\n  // Convert all \"arguments\" references in the arrow to point at the alias.\n  if (argumentsPaths.length > 0) {\n    const argumentsBinding = getBinding(thisEnvFn, \"arguments\", () => {\n      const args = () => identifier(\"arguments\");\n      if (thisEnvFn.scope.path.isProgram()) {\n        return conditionalExpression(\n          binaryExpression(\n            \"===\",\n            unaryExpression(\"typeof\", args()),\n            stringLiteral(\"undefined\"),\n          ),\n          thisEnvFn.scope.buildUndefinedNode(),\n          args(),\n        );\n      } else {\n        return args();\n      }\n    });\n\n    argumentsPaths.forEach(argumentsChild => {\n      const argsRef = identifier(argumentsBinding);\n      argsRef.loc = argumentsChild.node.loc;\n\n      argumentsChild.replaceWith(argsRef);\n    });\n  }\n\n  // Convert all \"new.target\" references in the arrow to point at the alias.\n  if (newTargetPaths.length > 0) {\n    const newTargetBinding = getBinding(thisEnvFn, \"newtarget\", () =>\n      metaProperty(identifier(\"new\"), identifier(\"target\")),\n    );\n\n    newTargetPaths.forEach(targetChild => {\n      const targetRef = identifier(newTargetBinding);\n      targetRef.loc = targetChild.node.loc;\n\n      targetChild.replaceWith(targetRef);\n    });\n  }\n\n  // Convert all \"super.prop\" references to point at aliases.\n  if (superProps.length > 0) {\n    if (!allowInsertArrow) {\n      throw superProps[0].buildCodeFrameError(\n        \"When using '@babel/plugin-transform-arrow-functions', \" +\n          \"it's not possible to compile `super.prop` in an arrow function without compiling classes.\\n\" +\n          \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n      );\n    }\n\n    const flatSuperProps: NodePath<t.MemberExpression>[] = superProps.reduce(\n      (acc, superProp) => acc.concat(standardizeSuperProperty(superProp)),\n      [],\n    );\n\n    flatSuperProps.forEach(superProp => {\n      const key = superProp.node.computed\n        ? \"\"\n        : // @ts-expect-error super property must not contain private name\n          superProp.get(\"property\").node.name;\n\n      const superParentPath = superProp.parentPath;\n\n      const isAssignment = superParentPath.isAssignmentExpression({\n        left: superProp.node,\n      });\n      const isCall = superParentPath.isCallExpression({\n        callee: superProp.node,\n      });\n      const isTaggedTemplate = superParentPath.isTaggedTemplateExpression({\n        tag: superProp.node,\n      });\n      const superBinding = getSuperPropBinding(thisEnvFn, isAssignment, key);\n\n      const args: t.Expression[] = [];\n      if (superProp.node.computed) {\n        // SuperProperty must not be a private name\n        args.push(superProp.get(\"property\").node as t.Expression);\n      }\n\n      if (isAssignment) {\n        const value = superParentPath.node.right;\n        args.push(value);\n      }\n\n      const call = callExpression(identifier(superBinding), args);\n\n      if (isCall) {\n        superParentPath.unshiftContainer(\"arguments\", thisExpression());\n        superProp.replaceWith(memberExpression(call, identifier(\"call\")));\n\n        thisPaths.push(\n          superParentPath.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else if (isAssignment) {\n        // Replace not only the super.prop, but the whole assignment\n        superParentPath.replaceWith(call);\n      } else if (isTaggedTemplate) {\n        superProp.replaceWith(\n          callExpression(memberExpression(call, identifier(\"bind\"), false), [\n            thisExpression(),\n          ]),\n        );\n\n        thisPaths.push(\n          superProp.get(\"arguments.0\") as NodePath<t.ThisExpression>,\n        );\n      } else {\n        superProp.replaceWith(call);\n      }\n    });\n  }\n\n  // Convert all \"this\" references in the arrow to point at the alias.\n  let thisBinding: string | null;\n  if (thisPaths.length > 0 || !noNewArrows) {\n    thisBinding = getThisBinding(thisEnvFn, inConstructor);\n\n    if (\n      noNewArrows ||\n      // In subclass constructors, still need to rewrite because \"this\" can't be bound in spec mode\n      // because it might not have been initialized yet.\n      (inConstructor && hasSuperClass(thisEnvFn))\n    ) {\n      thisPaths.forEach(thisChild => {\n        const thisRef = thisChild.isJSX()\n          ? jsxIdentifier(thisBinding)\n          : identifier(thisBinding);\n\n        thisRef.loc = thisChild.node.loc;\n        thisChild.replaceWith(thisRef);\n      });\n\n      if (!noNewArrows) thisBinding = null;\n    }\n  }\n\n  return { thisBinding, fnPath };\n}\n\ntype LogicalOp = Parameters<typeof logicalExpression>[0];\ntype BinaryOp = Parameters<typeof binaryExpression>[0];\n\nfunction isLogicalOp(op: string): op is LogicalOp {\n  return LOGICAL_OPERATORS.includes(op);\n}\n\nfunction standardizeSuperProperty(\n  superProp: NodePath<t.MemberExpression>,\n):\n  | [NodePath<t.MemberExpression>]\n  | [NodePath<t.MemberExpression>, NodePath<t.MemberExpression>] {\n  if (\n    superProp.parentPath.isAssignmentExpression() &&\n    superProp.parentPath.node.operator !== \"=\"\n  ) {\n    const assignmentPath = superProp.parentPath;\n\n    const op = assignmentPath.node.operator.slice(0, -1) as\n      | LogicalOp\n      | BinaryOp;\n\n    const value = assignmentPath.node.right;\n\n    const isLogicalAssignment = isLogicalOp(op);\n\n    if (superProp.node.computed) {\n      // from: super[foo] **= 4;\n      // to:   super[tmp = foo] = super[tmp] ** 4;\n\n      // from: super[foo] ??= 4;\n      // to:   super[tmp = foo] ?? super[tmp] = 4;\n\n      const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Expression;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(\n          memberExpression(\n            object,\n            assignmentExpression(\"=\", tmp, property),\n            true /* computed */,\n          ),\n        );\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(tmp.name), true /* computed */),\n            value,\n          ),\n        );\n    } else {\n      // from: super.foo **= 4;\n      // to:   super.foo = super.foo ** 4;\n\n      // from: super.foo ??= 4;\n      // to:   super.foo ?? super.foo = 4;\n\n      const object = superProp.node.object;\n      const property = superProp.node.property as t.Identifier;\n\n      assignmentPath\n        .get(\"left\")\n        .replaceWith(memberExpression(object, property));\n\n      assignmentPath\n        .get(\"right\")\n        .replaceWith(\n          rightExpression(\n            isLogicalAssignment ? \"=\" : op,\n            memberExpression(object, identifier(property.name)),\n            value,\n          ),\n        );\n    }\n\n    if (isLogicalAssignment) {\n      assignmentPath.replaceWith(\n        logicalExpression(\n          op,\n          assignmentPath.node.left as t.MemberExpression,\n          assignmentPath.node.right,\n        ),\n      );\n    } else {\n      assignmentPath.node.operator = \"=\";\n    }\n\n    return [\n      assignmentPath.get(\"left\") as NodePath<t.MemberExpression>,\n      assignmentPath.get(\"right\").get(\"left\") as NodePath<t.MemberExpression>,\n    ];\n  } else if (superProp.parentPath.isUpdateExpression()) {\n    const updateExpr = superProp.parentPath;\n\n    const tmp = superProp.scope.generateDeclaredUidIdentifier(\"tmp\");\n    const computedKey = superProp.node.computed\n      ? superProp.scope.generateDeclaredUidIdentifier(\"prop\")\n      : null;\n\n    const parts: t.Expression[] = [\n      assignmentExpression(\n        \"=\",\n        tmp,\n        memberExpression(\n          superProp.node.object,\n          computedKey\n            ? assignmentExpression(\n                \"=\",\n                computedKey,\n                superProp.node.property as t.Expression,\n              )\n            : superProp.node.property,\n          superProp.node.computed,\n        ),\n      ),\n      assignmentExpression(\n        \"=\",\n        memberExpression(\n          superProp.node.object,\n          computedKey ? identifier(computedKey.name) : superProp.node.property,\n          superProp.node.computed,\n        ),\n        binaryExpression(\n          // map `++` to `+`, and `--` to `-`\n          superProp.parentPath.node.operator[0] as \"+\" | \"-\",\n          identifier(tmp.name),\n          numericLiteral(1),\n        ),\n      ),\n    ];\n\n    if (!superProp.parentPath.node.prefix) {\n      parts.push(identifier(tmp.name));\n    }\n\n    updateExpr.replaceWith(sequenceExpression(parts));\n\n    const left = updateExpr.get(\n      \"expressions.0.right\",\n    ) as NodePath<t.MemberExpression>;\n    const right = updateExpr.get(\n      \"expressions.1.left\",\n    ) as NodePath<t.MemberExpression>;\n    return [left, right];\n  }\n\n  return [superProp];\n\n  function rightExpression(\n    op: BinaryOp | \"=\",\n    left: t.MemberExpression,\n    right: t.Expression,\n  ) {\n    if (op === \"=\") {\n      return assignmentExpression(\"=\", left, right);\n    } else {\n      return binaryExpression(op, left, right);\n    }\n  }\n}\n\nfunction hasSuperClass(thisEnvFn: NodePath<t.Function>) {\n  return (\n    thisEnvFn.isClassMethod() &&\n    !!(thisEnvFn.parentPath.parentPath.node as t.Class).superClass\n  );\n}\n\nconst assignSuperThisVisitor = environmentVisitor<{\n  supers: WeakSet<t.CallExpression>;\n  thisBinding: string;\n}>({\n  CallExpression(child, { supers, thisBinding }) {\n    if (!child.get(\"callee\").isSuper()) return;\n    if (supers.has(child.node)) return;\n    supers.add(child.node);\n\n    child.replaceWithMultiple([\n      child.node,\n      assignmentExpression(\"=\", identifier(thisBinding), identifier(\"this\")),\n    ]);\n  },\n});\n\n// Create a binding that evaluates to the \"this\" of the given function.\nfunction getThisBinding(\n  thisEnvFn: NodePath<t.Function>,\n  inConstructor: boolean,\n) {\n  return getBinding(thisEnvFn, \"this\", thisBinding => {\n    if (!inConstructor || !hasSuperClass(thisEnvFn)) return thisExpression();\n\n    thisEnvFn.traverse(assignSuperThisVisitor, {\n      supers: new WeakSet(),\n      thisBinding,\n    });\n  });\n}\n\n// Create a binding for a function that will call \"super()\" with arguments passed through.\nfunction getSuperBinding(thisEnvFn: NodePath<t.Function>) {\n  return getBinding(thisEnvFn, \"supercall\", () => {\n    const argsBinding = thisEnvFn.scope.generateUidIdentifier(\"args\");\n    return arrowFunctionExpression(\n      [restElement(argsBinding)],\n      callExpression(_super(), [spreadElement(identifier(argsBinding.name))]),\n    );\n  });\n}\n\n// Create a binding for a function that will call \"super.foo\" or \"super[foo]\".\nfunction getSuperPropBinding(\n  thisEnvFn: NodePath<t.Function>,\n  isAssignment: boolean,\n  propName: string,\n) {\n  const op = isAssignment ? \"set\" : \"get\";\n\n  return getBinding(thisEnvFn, `superprop_${op}:${propName || \"\"}`, () => {\n    const argsList = [];\n\n    let fnBody;\n    if (propName) {\n      // () => super.foo\n      fnBody = memberExpression(_super(), identifier(propName));\n    } else {\n      const method = thisEnvFn.scope.generateUidIdentifier(\"prop\");\n      // (method) => super[method]\n      argsList.unshift(method);\n      fnBody = memberExpression(\n        _super(),\n        identifier(method.name),\n        true /* computed */,\n      );\n    }\n\n    if (isAssignment) {\n      const valueIdent = thisEnvFn.scope.generateUidIdentifier(\"value\");\n      argsList.push(valueIdent);\n\n      fnBody = assignmentExpression(\"=\", fnBody, identifier(valueIdent.name));\n    }\n\n    return arrowFunctionExpression(argsList, fnBody);\n  });\n}\n\nfunction getBinding(\n  thisEnvFn: NodePath,\n  key: string,\n  init: (name: string) => t.Expression,\n) {\n  const cacheKey = \"binding:\" + key;\n  let data: string | undefined = thisEnvFn.getData(cacheKey);\n  if (!data) {\n    const id = thisEnvFn.scope.generateUidIdentifier(key);\n    data = id.name;\n    thisEnvFn.setData(cacheKey, data);\n\n    thisEnvFn.scope.push({\n      id: id,\n      init: init(data),\n    });\n  }\n\n  return data;\n}\n\ntype ScopeInfo = {\n  thisPaths: NodePath<t.ThisExpression | t.JSXIdentifier>[];\n  superCalls: NodePath<t.CallExpression>[];\n  superProps: NodePath<t.MemberExpression>[];\n  argumentsPaths: NodePath<t.Identifier | t.JSXIdentifier>[];\n  newTargetPaths: NodePath<t.MetaProperty>[];\n};\n\nconst getScopeInformationVisitor = environmentVisitor<ScopeInfo>({\n  ThisExpression(child, { thisPaths }) {\n    thisPaths.push(child);\n  },\n  JSXIdentifier(child, { thisPaths }) {\n    if (child.node.name !== \"this\") return;\n    if (\n      !child.parentPath.isJSXMemberExpression({ object: child.node }) &&\n      !child.parentPath.isJSXOpeningElement({ name: child.node })\n    ) {\n      return;\n    }\n\n    thisPaths.push(child);\n  },\n  CallExpression(child, { superCalls }) {\n    if (child.get(\"callee\").isSuper()) superCalls.push(child);\n  },\n  MemberExpression(child, { superProps }) {\n    if (child.get(\"object\").isSuper()) superProps.push(child);\n  },\n  Identifier(child, { argumentsPaths }) {\n    if (!child.isReferencedIdentifier({ name: \"arguments\" })) return;\n\n    let curr = child.scope;\n    do {\n      if (curr.hasOwnBinding(\"arguments\")) {\n        curr.rename(\"arguments\");\n        return;\n      }\n      if (curr.path.isFunction() && !curr.path.isArrowFunctionExpression()) {\n        break;\n      }\n    } while ((curr = curr.parent));\n\n    argumentsPaths.push(child);\n  },\n  MetaProperty(child, { newTargetPaths }) {\n    if (!child.get(\"meta\").isIdentifier({ name: \"new\" })) return;\n    if (!child.get(\"property\").isIdentifier({ name: \"target\" })) return;\n\n    newTargetPaths.push(child);\n  },\n});\n\nfunction getScopeInformation(fnPath: NodePath) {\n  const thisPaths: ScopeInfo[\"thisPaths\"] = [];\n  const argumentsPaths: ScopeInfo[\"argumentsPaths\"] = [];\n  const newTargetPaths: ScopeInfo[\"newTargetPaths\"] = [];\n  const superProps: ScopeInfo[\"superProps\"] = [];\n  const superCalls: ScopeInfo[\"superCalls\"] = [];\n\n  fnPath.traverse(getScopeInformationVisitor, {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  });\n\n  return {\n    thisPaths,\n    argumentsPaths,\n    newTargetPaths,\n    superProps,\n    superCalls,\n  };\n}\n\nexport function splitExportDeclaration(\n  this: NodePath<t.ExportDefaultDeclaration | t.ExportNamedDeclaration>,\n): NodePath<t.Declaration> {\n  if (!this.isExportDeclaration() || this.isExportAllDeclaration()) {\n    throw new Error(\"Only default and named export declarations can be split.\");\n  }\n  if (this.isExportNamedDeclaration() && this.get(\"specifiers\").length > 0) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const declaration = this.get(\"declaration\");\n\n  if (this.isExportDefaultDeclaration()) {\n    const standaloneDeclaration =\n      declaration.isFunctionDeclaration() || declaration.isClassDeclaration();\n    const exportExpr =\n      declaration.isFunctionExpression() || declaration.isClassExpression();\n\n    const scope = declaration.isScope()\n      ? declaration.scope.parent\n      : declaration.scope;\n\n    // @ts-expect-error id is not defined in expressions other than function/class\n    let id = declaration.node.id;\n    let needBindingRegistration = false;\n\n    if (!id) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(\"default\");\n\n      if (standaloneDeclaration || exportExpr) {\n        declaration.node.id = cloneNode(id);\n      }\n    } else if (exportExpr && scope.hasBinding(id.name)) {\n      needBindingRegistration = true;\n\n      id = scope.generateUidIdentifier(id.name);\n    }\n\n    const updatedDeclaration = standaloneDeclaration\n      ? declaration.node\n      : variableDeclaration(\"var\", [\n          variableDeclarator(\n            cloneNode(id),\n            // @ts-expect-error When `standaloneDeclaration` is false, declaration must not be a Function/ClassDeclaration\n            declaration.node,\n          ),\n        ]);\n\n    const updatedExportDeclaration = exportNamedDeclaration(null, [\n      exportSpecifier(cloneNode(id), identifier(\"default\")),\n    ]);\n\n    this.insertAfter(updatedExportDeclaration);\n    this.replaceWith(updatedDeclaration);\n\n    if (needBindingRegistration) {\n      scope.registerDeclaration(this);\n    }\n\n    return this;\n  } else if (this.get(\"specifiers\").length > 0) {\n    throw new Error(\"It doesn't make sense to split exported specifiers.\");\n  }\n\n  const bindingIdentifiers = declaration.getOuterBindingIdentifiers();\n\n  const specifiers = Object.keys(bindingIdentifiers).map(name => {\n    return exportSpecifier(identifier(name), identifier(name));\n  });\n\n  const aliasDeclar = exportNamedDeclaration(null, specifiers);\n\n  this.insertAfter(aliasDeclar);\n  this.replaceWith(declaration.node);\n  return this;\n}\n\nconst refersOuterBindingVisitor: Visitor<{\n  needsRename: boolean;\n  name: string;\n}> = {\n  \"ReferencedIdentifier|BindingIdentifier\"(\n    path: NodePath<t.Identifier>,\n    state,\n  ) {\n    // check if this node matches our function id\n    if (path.node.name !== state.name) return;\n    state.needsRename = true;\n    path.stop();\n  },\n  Scope(path, state) {\n    if (path.scope.hasOwnBinding(state.name)) {\n      path.skip();\n    }\n  },\n};\n\nexport function ensureFunctionName<\n  N extends t.FunctionExpression | t.ClassExpression,\n>(this: NodePath<N>, supportUnicodeId: boolean): null | NodePath<N> {\n  if (this.node.id) return this;\n\n  const res = getFunctionName(this.node, this.parent);\n  if (res == null) return this;\n  let { name } = res;\n\n  if (!supportUnicodeId && /[\\uD800-\\uDFFF]/.test(name)) {\n    return null;\n  }\n\n  if (name.startsWith(\"get \") || name.startsWith(\"set \")) {\n    // TODO: Remove this to support naming getters and setters\n    return null;\n  }\n\n  name = toBindingIdentifierName(name.replace(/[/ ]/g, \"_\"));\n  const id = identifier(name);\n  inherits(id, res.originalNode);\n\n  const state = { needsRename: false, name };\n\n  // check to see if we have a local binding of the id we're setting inside of\n  // the function, this is important as there are caveats associated\n\n  const { scope } = this;\n  const binding = scope.getOwnBinding(name);\n  if (binding) {\n    if (binding.kind === \"param\") {\n      // safari will blow up in strict mode with code like:\n      //\n      //   let t = function t(t) {};\n      //\n      // with the error:\n      //\n      //   Cannot declare a parameter named 't' as it shadows the name of a\n      //   strict mode function.\n      //\n      // this isn't to the spec and they've invented this behaviour which is\n      // **extremely** annoying so we avoid setting the name if it has a param\n      // with the same id\n      state.needsRename = true;\n    } else {\n      // otherwise it's defined somewhere in scope like:\n      //\n      //   let t = function () {\n      //     let t = 2;\n      //   };\n      //\n      // so we can safely just set the id and move along as it shadows the\n      // bound function id\n    }\n  } else if (scope.parent.hasBinding(name) || scope.hasGlobal(name)) {\n    this.traverse(refersOuterBindingVisitor, state);\n  }\n\n  if (!state.needsRename) {\n    this.node.id = id;\n    scope.getProgramParent().references[id.name] = true;\n    return this;\n  }\n\n  if (scope.hasBinding(id.name) && !scope.hasGlobal(id.name)) {\n    // we can just munge the local binding\n    scope.rename(id.name);\n    this.node.id = id;\n    scope.getProgramParent().references[id.name] = true;\n    return this;\n  }\n\n  // TODO: we don't currently support wrapping class expressions\n  if (!isFunction(this.node)) return null;\n\n  // need to add a wrapper since we can't change the references\n\n  const key = scope.generateUidIdentifier(id.name);\n  // shim in dummy params to retain function arity, if you try to read the\n  // source then you'll get the original since it's proxied so it's all good\n  const params = [];\n  for (let i = 0, len = getFunctionArity(this.node); i < len; i++) {\n    params.push(scope.generateUidIdentifier(\"x\"));\n  }\n  const call = template.expression.ast`\n    (function (${key}) {\n      function ${id}(${params}) {\n        return ${cloneNode(key)}.apply(this, arguments);\n      }\n\n      ${cloneNode(id)}.toString = function () {\n        return ${cloneNode(key)}.toString();\n      }\n\n      return ${cloneNode(id)};\n    })(${toExpression(this.node)})\n  ` as t.CallExpression;\n\n  return this.replaceWith(call)[0].get(\"arguments.0\") as NodePath<N>;\n}\n\nfunction getFunctionArity(node: t.Function): number {\n  const count = node.params.findIndex(\n    param => isAssignmentPattern(param) || isRestElement(param),\n  );\n  return count === -1 ? node.params.length : count;\n}\n"], "mappings": ";;;;;;;;;;;AAEA,IAAAA,EAAA,GAAAC,OAAA;AAuCA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAGA,IAAAG,QAAA,GAAAH,OAAA;AAAqC;EA1CnCI,uBAAuB;EACvBC,oBAAoB;EACpBC,gBAAgB;EAChBC,cAAc;EACdC,cAAc;EACdC,qBAAqB;EACrBC,mBAAmB;EACnBC,UAAU;EACVC,YAAY;EACZC,aAAa;EACbC,iBAAiB;EACjBC,iBAAiB;EACjBC,gBAAgB;EAChBC,YAAY;EACZC,cAAc;EACdC,gBAAgB;EAChBC,WAAW;EACXC,eAAe;EACfC,kBAAkB;EAClBC,aAAa;EACbC,aAAa;EACbC,KAAK,EAAIC,MAAM;EACfC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,uBAAuB;EACvBC,UAAU;EACVC,mBAAmB;EACnBC,aAAa;EACbC,eAAe;EACfC,SAAS;EACTC,mBAAmB;EACnBC,kBAAkB;EAClBC,sBAAsB;EACtBC,eAAe;EACfC;AAAQ,IAAAzC,EAAA;AASH,SAAS0C,aAAaA,CAAA,EAAiB;EAC5C,IAAIC,GAAG;EACP,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAE;IAC7BD,GAAG,GAAG,IAAI,CAACE,IAAI,CAACC,QAAQ;EAC1B,CAAC,MAAM,IAAI,IAAI,CAACC,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;IAC/CL,GAAG,GAAG,IAAI,CAACE,IAAI,CAACF,GAAG;EACrB,CAAC,MAAM;IACL,MAAM,IAAIM,cAAc,CAAC,MAAM,CAAC;EAClC;EAGA,IAAI,CAAC,IAAI,CAACJ,IAAI,CAACK,QAAQ,EAAE;IACvB,IAAIrC,YAAY,CAAC8B,GAAG,CAAC,EAAEA,GAAG,GAAGlB,aAAa,CAACkB,GAAG,CAACQ,IAAI,CAAC;EACtD;EAEA,OAAOR,GAAG;AACZ;AAEO,SAASS,WAAWA,CAAA,EAInB;EACN,MAAMC,IAAI,GAAG,IAAI,CAACC,GAAG,CAAC,MAAM,CAAC;EAC7B,MAAMC,QAAQ,GAAGF,IAAI,CAACR,IAAI;EAE1B,IAAIW,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IACvB,MAAM,IAAIK,KAAK,CAAC,+CAA+C,CAAC;EAClE;EACA,IAAI,CAACH,QAAQ,EAAE;IACb,MAAM,IAAIG,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAEA,IAAIL,IAAI,CAACM,gBAAgB,CAAC,CAAC,EAAE;IAM3B,OAAOJ,QAAQ;EACjB;EAEA,MAAMK,UAA8B,GAAG,EAAE;EAEzC,IAAIC,UAAU,GAAG,MAAM;EACvB,IAAIlB,GAAG;EACP,IAAImB,OAAO;EACX,IAAIT,IAAI,CAACU,WAAW,CAAC,CAAC,EAAE;IACtBD,OAAO,GAAG,MAAM;IAChBnB,GAAG,GAAG,CAAC;IACPiB,UAAU,CAACI,IAAI,CAACX,IAAI,CAACR,IAAI,CAAC;EAC5B,CAAC,MAAM;IACLgB,UAAU,IAAI,SAAS;IACvB,IAAI,IAAI,CAAC7B,UAAU,CAAC,CAAC,EAAE;MACrBW,GAAG,GAAG,UAAU;MAChBiB,UAAU,CAACI,IAAI,CAAC1C,eAAe,CAAC+B,IAAI,CAACR,IAAoB,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLF,GAAG,GAAG,YAAY;MAClBiB,UAAU,CAACI,IAAI,CAACrD,mBAAmB,CAAC0C,IAAI,CAACR,IAAoB,CAAC,CAAC;IACjE;EACF;EAEA,IAAI,CAACA,IAAI,CAACQ,IAAI,GAAG7C,cAAc,CAACoD,UAAU,CAAC;EAC3C,MAAMK,UAAU,GAAG,IAAI,CAACX,GAAG,CAACO,UAAU,CAAa;EACnDK,cAAK,CAACC,IAAI,CACRd,IAAI,EACJY,UAAU,EACVH,OAAO,GAEHG,UAAU,CAACpB,IAAI,CAACiB,OAAO,CAAC,GACxBG,UAAU,CAACpB,IAAI,EACnBiB,OAAO,EACPnB,GACF,CAAC;EAOD,OAAO,IAAI,CAACE,IAAI;AAClB;AAE+C;EAK7CuB,OAAO,CAACC,uBAAuB,GAAG,YAA0B;IAC1D,IAAI,CAAC,IAAI,CAACC,yBAAyB,CAAC,CAAC,EAAE;IAEvC,IAAI,CAACC,yBAAyB,CAAC,CAAC;EAClC,CAAC;AACH;AAQO,SAASC,yBAAyBA,CAAA,EAAiB;EACxD,IACE,CAAC,IAAI,CAACF,yBAAyB,CAAC,CAAC,IACjC,CAAC,IAAI,CAACG,oBAAoB,CAAC,CAAC,IAC5B,CAAC,IAAI,CAACC,qBAAqB,CAAC,CAAC,EAC7B;IACA,MAAM,IAAI,CAACC,mBAAmB,CAC5B,gDACF,CAAC;EACH;EAEAC,wBAAwB,CAAC,IAAI,CAAC;AAChC;AAEA,SAASC,OAAOA,CACdC,IAAiB,EACjBC,IAAO,EAC4C;EACnDD,IAAI,CAACjC,IAAI,CAACkC,IAAI,GAAGA,IAAI;AACvB;AAKO,SAASR,yBAAyBA,CAEvC;EACES,gBAAgB,GAAG,IAAI;EACvBC,wBAAwB,GAAGD,gBAAgB;EAC3CE,WAAW,GAGP,EAAAC,WAAA,KAAAA,WAAA,GAACC,SAAS,CAAC,CAAC,CAAC,qBAAZD,WAAA,CAAcE,aAAa;AAKlC,CAAC,GAAG,CAAC,CAAC,EAGN;EACA,IAAI,CAAC,IAAI,CAACf,yBAAyB,CAAC,CAAC,EAAE;IACrC,MAAO,IAAI,CAAcK,mBAAmB,CAC1C,6DACF,CAAC;EACH;EAEA,IAAIW,IAAI,GAAG,IAAI;EACf,IAAI,CAACJ,WAAW,EAAE;IAAA,IAAAK,qBAAA;IAIhBD,IAAI,IAAAC,qBAAA,GAAGD,IAAI,CAACE,kBAAkB,CAAC,KAAK,CAAC,YAAAD,qBAAA,GAAID,IAAI;EAC/C;EAEA,MAAM;IAAEG,WAAW;IAAEC,MAAM,EAAEC;EAAG,CAAC,GAAGf,wBAAwB,CAC1DU,IAAI,EACJJ,WAAW,EACXF,gBAAgB,EAChBC,wBACF,CAAC;EAEDU,EAAE,CAACvC,WAAW,CAAC,CAAC;EAChByB,OAAO,CAACc,EAAE,EAAE,oBAAoB,CAAC;EAEjC,IAAI,CAACT,WAAW,EAAE;IAChB,MAAMU,YAAY,GAAGH,WAAW,GAC5B,IAAI,GACJE,EAAE,CAACE,KAAK,CAACC,qBAAqB,CAAC,cAAc,CAAC;IAClD,IAAIF,YAAY,EAAE;MAChBD,EAAE,CAAC1B,UAAU,CAAC4B,KAAK,CAAC7B,IAAI,CAAC;QACvB+B,EAAE,EAAEH,YAAY;QAChBI,IAAI,EAAE5E,gBAAgB,CAAC,EAAE;MAC3B,CAAC,CAAC;IACJ;IAEAuE,EAAE,CAACrC,GAAG,CAAC,MAAM,CAAC,CAAC2C,gBAAgB,CAC7B,MAAM,EACNtF,mBAAmB,CACjBF,cAAc,CAAC,IAAI,CAACyF,GAAG,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAClDvE,cAAc,CAAC,CAAC,EAChBgE,YAAY,GACRhF,UAAU,CAACgF,YAAY,CAACzC,IAAI,CAAC,GAC7BvC,UAAU,CAAC6E,WAAW,CAAC,CAC5B,CACH,CACF,CAAC;IAEDE,EAAE,CAACS,WAAW,CACZ3F,cAAc,CAACQ,gBAAgB,CAAC0E,EAAE,CAAC9C,IAAI,EAAEjC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAC5DgF,YAAY,GAAGhF,UAAU,CAACgF,YAAY,CAACzC,IAAI,CAAC,GAAGvB,cAAc,CAAC,CAAC,CAChE,CACH,CAAC;IAED,OAAO+D,EAAE,CAACrC,GAAG,CAAC,eAAe,CAAC;EAChC;EAEA,OAAOqC,EAAE;AACX;AAEA,MAAMU,oBAAoB,GAAG,IAAAC,4BAAkB,EAE5C;EACDC,cAAcA,CAACC,KAAK,EAAE;IAAEC;EAAc,CAAC,EAAE;IACvC,IAAI,CAACD,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAE;IACpCD,aAAa,CAACzC,IAAI,CAACwC,KAAK,CAAC;EAC3B;AACF,CAAC,CAAC;AAUF,SAAS5B,wBAAwBA,CAC/Bc,MAA4B,EAE5BR,WAA2B,GAAG,IAAI,EAClCF,gBAAgC,GAAG,IAAI,EACvCC,wBAAwC,GAAG,IAAI,EACQ;EACvD,IAAI0B,WAAW;EACf,IAAIC,SAA+B,GAAGlB,MAAM,CAACmB,UAAU,CAACC,CAAC,IAAI;IAC3D,IAAIA,CAAC,CAACxC,yBAAyB,CAAC,CAAC,EAAE;MAAA,IAAAyC,YAAA;MACjC,CAAAA,YAAA,GAAAJ,WAAW,YAAAI,YAAA,GAAXJ,WAAW,GAAKG,CAAC;MACjB,OAAO,KAAK;IACd;IACA,OACEA,CAAC,CAAC9E,UAAU,CAAC,CAAC,IACd8E,CAAC,CAACE,SAAS,CAAC,CAAC,IACbF,CAAC,CAACG,eAAe,CAAC;MAAEC,MAAM,EAAE;IAAM,CAAC,CAAC,IACpCJ,CAAC,CAACK,sBAAsB,CAAC;MAAED,MAAM,EAAE;IAAM,CAAC,CAAC;EAE/C,CAAC,CAAyB;EAC1B,MAAME,aAAa,GAAGR,SAAS,CAACS,aAAa,CAAC;IAAEC,IAAI,EAAE;EAAc,CAAC,CAAC;EAEtE,IAAIV,SAAS,CAACK,eAAe,CAAC,CAAC,IAAIL,SAAS,CAACO,sBAAsB,CAAC,CAAC,EAAE;IACrE,IAAIR,WAAW,EAAE;MACfC,SAAS,GAAGD,WAAW;IACzB,CAAC,MAAM,IAAI3B,gBAAgB,EAAE;MAK3BU,MAAM,CAACU,WAAW,CAChB3F,cAAc,CACZJ,uBAAuB,CAAC,EAAE,EAAEwB,YAAY,CAAC6D,MAAM,CAAC7C,IAAI,CAAC,CAAC,EACtD,EACF,CACF,CAAC;MACD+D,SAAS,GAAGlB,MAAM,CAACpC,GAAG,CAAC,QAAQ,CAAwC;MACvEoC,MAAM,GAAGkB,SAAS,CAACtD,GAAG,CAAC,MAAM,CAAmC;IAClE,CAAC,MAAM;MACL,MAAMoC,MAAM,CAACf,mBAAmB,CAC9B,iDACF,CAAC;IACH;EACF;EAEA,MAAM;IAAE4C,SAAS;IAAEC,cAAc;IAAEC,cAAc;IAAEC,UAAU;IAAEC;EAAW,CAAC,GACzEC,mBAAmB,CAAClC,MAAM,CAAC;EAG7B,IAAI0B,aAAa,IAAIO,UAAU,CAACE,MAAM,GAAG,CAAC,EAAE;IAC1C,IAAI,CAAC7C,gBAAgB,EAAE;MACrB,MAAM2C,UAAU,CAAC,CAAC,CAAC,CAAChD,mBAAmB,CACrC,wDAAwD,GACtD,0FAA0F,GAC1F,2EACJ,CAAC;IACH;IACA,IAAI,CAACM,wBAAwB,EAAE;MAE7B,MAAM0C,UAAU,CAAC,CAAC,CAAC,CAAChD,mBAAmB,CACrC,mDAAmD,GACjD,0HAA0H,GAC1H,2EACJ,CAAC;IACH;IACA,MAAM8B,aAA2C,GAAG,EAAE;IACtDG,SAAS,CAACkB,QAAQ,CAACzB,oBAAoB,EAAE;MAAEI;IAAc,CAAC,CAAC;IAC3D,MAAMsB,YAAY,GAAGC,eAAe,CAACpB,SAAS,CAAC;IAC/CH,aAAa,CAACwB,OAAO,CAACC,SAAS,IAAI;MACjC,MAAMC,MAAM,GAAGvH,UAAU,CAACmH,YAAY,CAAC;MACvCI,MAAM,CAACC,GAAG,GAAGF,SAAS,CAACrF,IAAI,CAACsF,MAAM,CAACC,GAAG;MAEtCF,SAAS,CAAC5E,GAAG,CAAC,QAAQ,CAAC,CAAC8C,WAAW,CAAC+B,MAAM,CAAC;IAC7C,CAAC,CAAC;EACJ;EAGA,IAAIX,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMQ,gBAAgB,GAAGC,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAAM;MAChE,MAAM2B,IAAI,GAAGA,CAAA,KAAM3H,UAAU,CAAC,WAAW,CAAC;MAC1C,IAAIgG,SAAS,CAACf,KAAK,CAACf,IAAI,CAACkC,SAAS,CAAC,CAAC,EAAE;QACpC,OAAOtG,qBAAqB,CAC1BH,gBAAgB,CACd,KAAK,EACLuB,eAAe,CAAC,QAAQ,EAAEyG,IAAI,CAAC,CAAC,CAAC,EACjC9G,aAAa,CAAC,WAAW,CAC3B,CAAC,EACDmF,SAAS,CAACf,KAAK,CAAC2C,kBAAkB,CAAC,CAAC,EACpCD,IAAI,CAAC,CACP,CAAC;MACH,CAAC,MAAM;QACL,OAAOA,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;IAEFf,cAAc,CAACS,OAAO,CAACQ,cAAc,IAAI;MACvC,MAAMC,OAAO,GAAG9H,UAAU,CAACyH,gBAAgB,CAAC;MAC5CK,OAAO,CAACN,GAAG,GAAGK,cAAc,CAAC5F,IAAI,CAACuF,GAAG;MAErCK,cAAc,CAACrC,WAAW,CAACsC,OAAO,CAAC;IACrC,CAAC,CAAC;EACJ;EAGA,IAAIjB,cAAc,CAACI,MAAM,GAAG,CAAC,EAAE;IAC7B,MAAMc,gBAAgB,GAAGL,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAC1D1F,YAAY,CAACN,UAAU,CAAC,KAAK,CAAC,EAAEA,UAAU,CAAC,QAAQ,CAAC,CACtD,CAAC;IAED6G,cAAc,CAACQ,OAAO,CAACW,WAAW,IAAI;MACpC,MAAMC,SAAS,GAAGjI,UAAU,CAAC+H,gBAAgB,CAAC;MAC9CE,SAAS,CAACT,GAAG,GAAGQ,WAAW,CAAC/F,IAAI,CAACuF,GAAG;MAEpCQ,WAAW,CAACxC,WAAW,CAACyC,SAAS,CAAC;IACpC,CAAC,CAAC;EACJ;EAGA,IAAInB,UAAU,CAACG,MAAM,GAAG,CAAC,EAAE;IACzB,IAAI,CAAC7C,gBAAgB,EAAE;MACrB,MAAM0C,UAAU,CAAC,CAAC,CAAC,CAAC/C,mBAAmB,CACrC,wDAAwD,GACtD,6FAA6F,GAC7F,2EACJ,CAAC;IACH;IAEA,MAAMmE,cAA8C,GAAGpB,UAAU,CAACqB,MAAM,CACtE,CAACC,GAAG,EAAEC,SAAS,KAAKD,GAAG,CAACE,MAAM,CAACC,wBAAwB,CAACF,SAAS,CAAC,CAAC,EACnE,EACF,CAAC;IAEDH,cAAc,CAACb,OAAO,CAACgB,SAAS,IAAI;MAClC,MAAMtG,GAAG,GAAGsG,SAAS,CAACpG,IAAI,CAACK,QAAQ,GAC/B,EAAE,GAEF+F,SAAS,CAAC3F,GAAG,CAAC,UAAU,CAAC,CAACT,IAAI,CAACM,IAAI;MAEvC,MAAMiG,eAAe,GAAGH,SAAS,CAAChF,UAAU;MAE5C,MAAMoF,YAAY,GAAGD,eAAe,CAACE,sBAAsB,CAAC;QAC1DC,IAAI,EAAEN,SAAS,CAACpG;MAClB,CAAC,CAAC;MACF,MAAM2G,MAAM,GAAGJ,eAAe,CAACK,gBAAgB,CAAC;QAC9CtB,MAAM,EAAEc,SAAS,CAACpG;MACpB,CAAC,CAAC;MACF,MAAM6G,gBAAgB,GAAGN,eAAe,CAACO,0BAA0B,CAAC;QAClEC,GAAG,EAAEX,SAAS,CAACpG;MACjB,CAAC,CAAC;MACF,MAAMkF,YAAY,GAAG8B,mBAAmB,CAACjD,SAAS,EAAEyC,YAAY,EAAE1G,GAAG,CAAC;MAEtE,MAAM4F,IAAoB,GAAG,EAAE;MAC/B,IAAIU,SAAS,CAACpG,IAAI,CAACK,QAAQ,EAAE;QAE3BqF,IAAI,CAACvE,IAAI,CAACiF,SAAS,CAAC3F,GAAG,CAAC,UAAU,CAAC,CAACT,IAAoB,CAAC;MAC3D;MAEA,IAAIwG,YAAY,EAAE;QAChB,MAAMS,KAAK,GAAGV,eAAe,CAACvG,IAAI,CAACkH,KAAK;QACxCxB,IAAI,CAACvE,IAAI,CAAC8F,KAAK,CAAC;MAClB;MAEA,MAAM3F,IAAI,GAAG1D,cAAc,CAACG,UAAU,CAACmH,YAAY,CAAC,EAAEQ,IAAI,CAAC;MAE3D,IAAIiB,MAAM,EAAE;QACVJ,eAAe,CAACnD,gBAAgB,CAAC,WAAW,EAAErE,cAAc,CAAC,CAAC,CAAC;QAC/DqH,SAAS,CAAC7C,WAAW,CAACnF,gBAAgB,CAACkD,IAAI,EAAEvD,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjE2G,SAAS,CAACvD,IAAI,CACZoF,eAAe,CAAC9F,GAAG,CAAC,aAAa,CACnC,CAAC;MACH,CAAC,MAAM,IAAI+F,YAAY,EAAE;QAEvBD,eAAe,CAAChD,WAAW,CAACjC,IAAI,CAAC;MACnC,CAAC,MAAM,IAAIuF,gBAAgB,EAAE;QAC3BT,SAAS,CAAC7C,WAAW,CACnB3F,cAAc,CAACQ,gBAAgB,CAACkD,IAAI,EAAEvD,UAAU,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,CAChEgB,cAAc,CAAC,CAAC,CACjB,CACH,CAAC;QAED2F,SAAS,CAACvD,IAAI,CACZiF,SAAS,CAAC3F,GAAG,CAAC,aAAa,CAC7B,CAAC;MACH,CAAC,MAAM;QACL2F,SAAS,CAAC7C,WAAW,CAACjC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ;EAGA,IAAIsB,WAA0B;EAC9B,IAAI8B,SAAS,CAACM,MAAM,GAAG,CAAC,IAAI,CAAC3C,WAAW,EAAE;IACxCO,WAAW,GAAGuE,cAAc,CAACpD,SAAS,EAAEQ,aAAa,CAAC;IAEtD,IACElC,WAAW,IAGVkC,aAAa,IAAI6C,aAAa,CAACrD,SAAS,CAAE,EAC3C;MACAW,SAAS,CAACU,OAAO,CAACiC,SAAS,IAAI;QAC7B,MAAMC,OAAO,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC,GAC7BtJ,aAAa,CAAC2E,WAAW,CAAC,GAC1B7E,UAAU,CAAC6E,WAAW,CAAC;QAE3B0E,OAAO,CAAC/B,GAAG,GAAG8B,SAAS,CAACrH,IAAI,CAACuF,GAAG;QAChC8B,SAAS,CAAC9D,WAAW,CAAC+D,OAAO,CAAC;MAChC,CAAC,CAAC;MAEF,IAAI,CAACjF,WAAW,EAAEO,WAAW,GAAG,IAAI;IACtC;EACF;EAEA,OAAO;IAAEA,WAAW;IAAEC;EAAO,CAAC;AAChC;AAKA,SAAS2E,WAAWA,CAACC,EAAU,EAAmB;EAChD,OAAOtJ,iBAAiB,CAACuJ,QAAQ,CAACD,EAAE,CAAC;AACvC;AAEA,SAASnB,wBAAwBA,CAC/BF,SAAuC,EAGwB;EAC/D,IACEA,SAAS,CAAChF,UAAU,CAACqF,sBAAsB,CAAC,CAAC,IAC7CL,SAAS,CAAChF,UAAU,CAACpB,IAAI,CAAC2H,QAAQ,KAAK,GAAG,EAC1C;IACA,MAAMC,cAAc,GAAGxB,SAAS,CAAChF,UAAU;IAE3C,MAAMqG,EAAE,GAAGG,cAAc,CAAC5H,IAAI,CAAC2H,QAAQ,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAEvC;IAEZ,MAAMZ,KAAK,GAAGW,cAAc,CAAC5H,IAAI,CAACkH,KAAK;IAEvC,MAAMY,mBAAmB,GAAGN,WAAW,CAACC,EAAE,CAAC;IAE3C,IAAIrB,SAAS,CAACpG,IAAI,CAACK,QAAQ,EAAE;MAO3B,MAAM0H,GAAG,GAAG3B,SAAS,CAACpD,KAAK,CAACgF,6BAA6B,CAAC,KAAK,CAAC;MAEhE,MAAMC,MAAM,GAAG7B,SAAS,CAACpG,IAAI,CAACiI,MAAM;MACpC,MAAMhI,QAAQ,GAAGmG,SAAS,CAACpG,IAAI,CAACC,QAAwB;MAExD2H,cAAc,CACXnH,GAAG,CAAC,MAAM,CAAC,CACX8C,WAAW,CACVnF,gBAAgB,CACd6J,MAAM,EACNxK,oBAAoB,CAAC,GAAG,EAAEsK,GAAG,EAAE9H,QAAQ,CAAC,EACxC,IACF,CACF,CAAC;MAEH2H,cAAc,CACXnH,GAAG,CAAC,OAAO,CAAC,CACZ8C,WAAW,CACV2E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BrJ,gBAAgB,CAAC6J,MAAM,EAAElK,UAAU,CAACgK,GAAG,CAACzH,IAAI,CAAC,EAAE,IAAmB,CAAC,EACnE2G,KACF,CACF,CAAC;IACL,CAAC,MAAM;MAOL,MAAMgB,MAAM,GAAG7B,SAAS,CAACpG,IAAI,CAACiI,MAAM;MACpC,MAAMhI,QAAQ,GAAGmG,SAAS,CAACpG,IAAI,CAACC,QAAwB;MAExD2H,cAAc,CACXnH,GAAG,CAAC,MAAM,CAAC,CACX8C,WAAW,CAACnF,gBAAgB,CAAC6J,MAAM,EAAEhI,QAAQ,CAAC,CAAC;MAElD2H,cAAc,CACXnH,GAAG,CAAC,OAAO,CAAC,CACZ8C,WAAW,CACV2E,eAAe,CACbJ,mBAAmB,GAAG,GAAG,GAAGL,EAAE,EAC9BrJ,gBAAgB,CAAC6J,MAAM,EAAElK,UAAU,CAACkC,QAAQ,CAACK,IAAI,CAAC,CAAC,EACnD2G,KACF,CACF,CAAC;IACL;IAEA,IAAIa,mBAAmB,EAAE;MACvBF,cAAc,CAACrE,WAAW,CACxBrF,iBAAiB,CACfuJ,EAAE,EACFG,cAAc,CAAC5H,IAAI,CAAC0G,IAAI,EACxBkB,cAAc,CAAC5H,IAAI,CAACkH,KACtB,CACF,CAAC;IACH,CAAC,MAAM;MACLU,cAAc,CAAC5H,IAAI,CAAC2H,QAAQ,GAAG,GAAG;IACpC;IAEA,OAAO,CACLC,cAAc,CAACnH,GAAG,CAAC,MAAM,CAAC,EAC1BmH,cAAc,CAACnH,GAAG,CAAC,OAAO,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CACxC;EACH,CAAC,MAAM,IAAI2F,SAAS,CAAChF,UAAU,CAAC+G,kBAAkB,CAAC,CAAC,EAAE;IACpD,MAAMC,UAAU,GAAGhC,SAAS,CAAChF,UAAU;IAEvC,MAAM2G,GAAG,GAAG3B,SAAS,CAACpD,KAAK,CAACgF,6BAA6B,CAAC,KAAK,CAAC;IAChE,MAAMK,WAAW,GAAGjC,SAAS,CAACpG,IAAI,CAACK,QAAQ,GACvC+F,SAAS,CAACpD,KAAK,CAACgF,6BAA6B,CAAC,MAAM,CAAC,GACrD,IAAI;IAER,MAAMM,KAAqB,GAAG,CAC5B7K,oBAAoB,CAClB,GAAG,EACHsK,GAAG,EACH3J,gBAAgB,CACdgI,SAAS,CAACpG,IAAI,CAACiI,MAAM,EACrBI,WAAW,GACP5K,oBAAoB,CAClB,GAAG,EACH4K,WAAW,EACXjC,SAAS,CAACpG,IAAI,CAACC,QACjB,CAAC,GACDmG,SAAS,CAACpG,IAAI,CAACC,QAAQ,EAC3BmG,SAAS,CAACpG,IAAI,CAACK,QACjB,CACF,CAAC,EACD5C,oBAAoB,CAClB,GAAG,EACHW,gBAAgB,CACdgI,SAAS,CAACpG,IAAI,CAACiI,MAAM,EACrBI,WAAW,GAAGtK,UAAU,CAACsK,WAAW,CAAC/H,IAAI,CAAC,GAAG8F,SAAS,CAACpG,IAAI,CAACC,QAAQ,EACpEmG,SAAS,CAACpG,IAAI,CAACK,QACjB,CAAC,EACD3C,gBAAgB,CAEd0I,SAAS,CAAChF,UAAU,CAACpB,IAAI,CAAC2H,QAAQ,CAAC,CAAC,CAAC,EACrC5J,UAAU,CAACgK,GAAG,CAACzH,IAAI,CAAC,EACpBhC,cAAc,CAAC,CAAC,CAClB,CACF,CAAC,CACF;IAED,IAAI,CAAC8H,SAAS,CAAChF,UAAU,CAACpB,IAAI,CAACuI,MAAM,EAAE;MACrCD,KAAK,CAACnH,IAAI,CAACpD,UAAU,CAACgK,GAAG,CAACzH,IAAI,CAAC,CAAC;IAClC;IAEA8H,UAAU,CAAC7E,WAAW,CAAC7E,kBAAkB,CAAC4J,KAAK,CAAC,CAAC;IAEjD,MAAM5B,IAAI,GAAG0B,UAAU,CAAC3H,GAAG,CACzB,qBACF,CAAiC;IACjC,MAAMyG,KAAK,GAAGkB,UAAU,CAAC3H,GAAG,CAC1B,oBACF,CAAiC;IACjC,OAAO,CAACiG,IAAI,EAAEQ,KAAK,CAAC;EACtB;EAEA,OAAO,CAACd,SAAS,CAAC;EAElB,SAAS8B,eAAeA,CACtBT,EAAkB,EAClBf,IAAwB,EACxBQ,KAAmB,EACnB;IACA,IAAIO,EAAE,KAAK,GAAG,EAAE;MACd,OAAOhK,oBAAoB,CAAC,GAAG,EAAEiJ,IAAI,EAAEQ,KAAK,CAAC;IAC/C,CAAC,MAAM;MACL,OAAOxJ,gBAAgB,CAAC+J,EAAE,EAAEf,IAAI,EAAEQ,KAAK,CAAC;IAC1C;EACF;AACF;AAEA,SAASE,aAAaA,CAACrD,SAA+B,EAAE;EACtD,OACEA,SAAS,CAACS,aAAa,CAAC,CAAC,IACzB,CAAC,CAAET,SAAS,CAAC3C,UAAU,CAACA,UAAU,CAACpB,IAAI,CAAawI,UAAU;AAElE;AAEA,MAAMC,sBAAsB,GAAG,IAAAhF,4BAAkB,EAG9C;EACDC,cAAcA,CAACC,KAAK,EAAE;IAAE+E,MAAM;IAAE9F;EAAY,CAAC,EAAE;IAC7C,IAAI,CAACe,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAE;IACpC,IAAI6E,MAAM,CAACC,GAAG,CAAChF,KAAK,CAAC3D,IAAI,CAAC,EAAE;IAC5B0I,MAAM,CAACE,GAAG,CAACjF,KAAK,CAAC3D,IAAI,CAAC;IAEtB2D,KAAK,CAACkF,mBAAmB,CAAC,CACxBlF,KAAK,CAAC3D,IAAI,EACVvC,oBAAoB,CAAC,GAAG,EAAEM,UAAU,CAAC6E,WAAW,CAAC,EAAE7E,UAAU,CAAC,MAAM,CAAC,CAAC,CACvE,CAAC;EACJ;AACF,CAAC,CAAC;AAGF,SAASoJ,cAAcA,CACrBpD,SAA+B,EAC/BQ,aAAsB,EACtB;EACA,OAAOkB,UAAU,CAAC1B,SAAS,EAAE,MAAM,EAAEnB,WAAW,IAAI;IAClD,IAAI,CAAC2B,aAAa,IAAI,CAAC6C,aAAa,CAACrD,SAAS,CAAC,EAAE,OAAOhF,cAAc,CAAC,CAAC;IAExEgF,SAAS,CAACkB,QAAQ,CAACwD,sBAAsB,EAAE;MACzCC,MAAM,EAAE,IAAII,OAAO,CAAC,CAAC;MACrBlG;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAGA,SAASuC,eAAeA,CAACpB,SAA+B,EAAE;EACxD,OAAO0B,UAAU,CAAC1B,SAAS,EAAE,WAAW,EAAE,MAAM;IAC9C,MAAMgF,WAAW,GAAGhF,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;IACjE,OAAOzF,uBAAuB,CAC5B,CAACgB,WAAW,CAACuK,WAAW,CAAC,CAAC,EAC1BnL,cAAc,CAACkB,MAAM,CAAC,CAAC,EAAE,CAACH,aAAa,CAACZ,UAAU,CAACgL,WAAW,CAACzI,IAAI,CAAC,CAAC,CAAC,CACxE,CAAC;EACH,CAAC,CAAC;AACJ;AAGA,SAAS0G,mBAAmBA,CAC1BjD,SAA+B,EAC/ByC,YAAqB,EACrBwC,QAAgB,EAChB;EACA,MAAMvB,EAAE,GAAGjB,YAAY,GAAG,KAAK,GAAG,KAAK;EAEvC,OAAOf,UAAU,CAAC1B,SAAS,EAAE,aAAa0D,EAAE,IAAIuB,QAAQ,IAAI,EAAE,EAAE,EAAE,MAAM;IACtE,MAAMC,QAAQ,GAAG,EAAE;IAEnB,IAAIC,MAAM;IACV,IAAIF,QAAQ,EAAE;MAEZE,MAAM,GAAG9K,gBAAgB,CAACU,MAAM,CAAC,CAAC,EAAEf,UAAU,CAACiL,QAAQ,CAAC,CAAC;IAC3D,CAAC,MAAM;MACL,MAAMG,MAAM,GAAGpF,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,MAAM,CAAC;MAE5DgG,QAAQ,CAACG,OAAO,CAACD,MAAM,CAAC;MACxBD,MAAM,GAAG9K,gBAAgB,CACvBU,MAAM,CAAC,CAAC,EACRf,UAAU,CAACoL,MAAM,CAAC7I,IAAI,CAAC,EACvB,IACF,CAAC;IACH;IAEA,IAAIkG,YAAY,EAAE;MAChB,MAAM6C,UAAU,GAAGtF,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAAC,OAAO,CAAC;MACjEgG,QAAQ,CAAC9H,IAAI,CAACkI,UAAU,CAAC;MAEzBH,MAAM,GAAGzL,oBAAoB,CAAC,GAAG,EAAEyL,MAAM,EAAEnL,UAAU,CAACsL,UAAU,CAAC/I,IAAI,CAAC,CAAC;IACzE;IAEA,OAAO9C,uBAAuB,CAACyL,QAAQ,EAAEC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ;AAEA,SAASzD,UAAUA,CACjB1B,SAAmB,EACnBjE,GAAW,EACXqD,IAAoC,EACpC;EACA,MAAMmG,QAAQ,GAAG,UAAU,GAAGxJ,GAAG;EACjC,IAAIyJ,IAAwB,GAAGxF,SAAS,CAACyF,OAAO,CAACF,QAAQ,CAAC;EAC1D,IAAI,CAACC,IAAI,EAAE;IACT,MAAMrG,EAAE,GAAGa,SAAS,CAACf,KAAK,CAACC,qBAAqB,CAACnD,GAAG,CAAC;IACrDyJ,IAAI,GAAGrG,EAAE,CAAC5C,IAAI;IACdyD,SAAS,CAAC0F,OAAO,CAACH,QAAQ,EAAEC,IAAI,CAAC;IAEjCxF,SAAS,CAACf,KAAK,CAAC7B,IAAI,CAAC;MACnB+B,EAAE,EAAEA,EAAE;MACNC,IAAI,EAAEA,IAAI,CAACoG,IAAI;IACjB,CAAC,CAAC;EACJ;EAEA,OAAOA,IAAI;AACb;AAUA,MAAMG,0BAA0B,GAAG,IAAAjG,4BAAkB,EAAY;EAC/DkG,cAAcA,CAAChG,KAAK,EAAE;IAAEe;EAAU,CAAC,EAAE;IACnCA,SAAS,CAACvD,IAAI,CAACwC,KAAK,CAAC;EACvB,CAAC;EACDiG,aAAaA,CAACjG,KAAK,EAAE;IAAEe;EAAU,CAAC,EAAE;IAClC,IAAIf,KAAK,CAAC3D,IAAI,CAACM,IAAI,KAAK,MAAM,EAAE;IAChC,IACE,CAACqD,KAAK,CAACvC,UAAU,CAACyI,qBAAqB,CAAC;MAAE5B,MAAM,EAAEtE,KAAK,CAAC3D;IAAK,CAAC,CAAC,IAC/D,CAAC2D,KAAK,CAACvC,UAAU,CAAC0I,mBAAmB,CAAC;MAAExJ,IAAI,EAAEqD,KAAK,CAAC3D;IAAK,CAAC,CAAC,EAC3D;MACA;IACF;IAEA0E,SAAS,CAACvD,IAAI,CAACwC,KAAK,CAAC;EACvB,CAAC;EACDD,cAAcA,CAACC,KAAK,EAAE;IAAEmB;EAAW,CAAC,EAAE;IACpC,IAAInB,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAEiB,UAAU,CAAC3D,IAAI,CAACwC,KAAK,CAAC;EAC3D,CAAC;EACDoG,gBAAgBA,CAACpG,KAAK,EAAE;IAAEkB;EAAW,CAAC,EAAE;IACtC,IAAIlB,KAAK,CAAClD,GAAG,CAAC,QAAQ,CAAC,CAACoD,OAAO,CAAC,CAAC,EAAEgB,UAAU,CAAC1D,IAAI,CAACwC,KAAK,CAAC;EAC3D,CAAC;EACDqG,UAAUA,CAACrG,KAAK,EAAE;IAAEgB;EAAe,CAAC,EAAE;IACpC,IAAI,CAAChB,KAAK,CAACsG,sBAAsB,CAAC;MAAE3J,IAAI,EAAE;IAAY,CAAC,CAAC,EAAE;IAE1D,IAAI4J,IAAI,GAAGvG,KAAK,CAACX,KAAK;IACtB,GAAG;MACD,IAAIkH,IAAI,CAACC,aAAa,CAAC,WAAW,CAAC,EAAE;QACnCD,IAAI,CAACE,MAAM,CAAC,WAAW,CAAC;QACxB;MACF;MACA,IAAIF,IAAI,CAACjI,IAAI,CAAC9C,UAAU,CAAC,CAAC,IAAI,CAAC+K,IAAI,CAACjI,IAAI,CAACR,yBAAyB,CAAC,CAAC,EAAE;QACpE;MACF;IACF,CAAC,QAASyI,IAAI,GAAGA,IAAI,CAACG,MAAM;IAE5B1F,cAAc,CAACxD,IAAI,CAACwC,KAAK,CAAC;EAC5B,CAAC;EACD2G,YAAYA,CAAC3G,KAAK,EAAE;IAAEiB;EAAe,CAAC,EAAE;IACtC,IAAI,CAACjB,KAAK,CAAClD,GAAG,CAAC,MAAM,CAAC,CAACzC,YAAY,CAAC;MAAEsC,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE;IACtD,IAAI,CAACqD,KAAK,CAAClD,GAAG,CAAC,UAAU,CAAC,CAACzC,YAAY,CAAC;MAAEsC,IAAI,EAAE;IAAS,CAAC,CAAC,EAAE;IAE7DsE,cAAc,CAACzD,IAAI,CAACwC,KAAK,CAAC;EAC5B;AACF,CAAC,CAAC;AAEF,SAASoB,mBAAmBA,CAAClC,MAAgB,EAAE;EAC7C,MAAM6B,SAAiC,GAAG,EAAE;EAC5C,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,cAA2C,GAAG,EAAE;EACtD,MAAMC,UAAmC,GAAG,EAAE;EAC9C,MAAMC,UAAmC,GAAG,EAAE;EAE9CjC,MAAM,CAACoC,QAAQ,CAACyE,0BAA0B,EAAE;IAC1ChF,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC,CAAC;EAEF,OAAO;IACLJ,SAAS;IACTC,cAAc;IACdC,cAAc;IACdC,UAAU;IACVC;EACF,CAAC;AACH;AAEO,SAASyF,sBAAsBA,CAAA,EAEX;EACzB,IAAI,CAAC,IAAI,CAACC,mBAAmB,CAAC,CAAC,IAAI,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;IAChE,MAAM,IAAI5J,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,IAAI,IAAI,CAAC6J,wBAAwB,CAAC,CAAC,IAAI,IAAI,CAACjK,GAAG,CAAC,YAAY,CAAC,CAACuE,MAAM,GAAG,CAAC,EAAE;IACxE,MAAM,IAAInE,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAM8J,WAAW,GAAG,IAAI,CAAClK,GAAG,CAAC,aAAa,CAAC;EAE3C,IAAI,IAAI,CAACmK,0BAA0B,CAAC,CAAC,EAAE;IACrC,MAAMC,qBAAqB,GACzBF,WAAW,CAAC9I,qBAAqB,CAAC,CAAC,IAAI8I,WAAW,CAACG,kBAAkB,CAAC,CAAC;IACzE,MAAMC,UAAU,GACdJ,WAAW,CAAC/I,oBAAoB,CAAC,CAAC,IAAI+I,WAAW,CAACK,iBAAiB,CAAC,CAAC;IAEvE,MAAMhI,KAAK,GAAG2H,WAAW,CAACM,OAAO,CAAC,CAAC,GAC/BN,WAAW,CAAC3H,KAAK,CAACqH,MAAM,GACxBM,WAAW,CAAC3H,KAAK;IAGrB,IAAIE,EAAE,GAAGyH,WAAW,CAAC3K,IAAI,CAACkD,EAAE;IAC5B,IAAIgI,uBAAuB,GAAG,KAAK;IAEnC,IAAI,CAAChI,EAAE,EAAE;MACPgI,uBAAuB,GAAG,IAAI;MAE9BhI,EAAE,GAAGF,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC;MAE3C,IAAI4H,qBAAqB,IAAIE,UAAU,EAAE;QACvCJ,WAAW,CAAC3K,IAAI,CAACkD,EAAE,GAAG3D,SAAS,CAAC2D,EAAE,CAAC;MACrC;IACF,CAAC,MAAM,IAAI6H,UAAU,IAAI/H,KAAK,CAACmI,UAAU,CAACjI,EAAE,CAAC5C,IAAI,CAAC,EAAE;MAClD4K,uBAAuB,GAAG,IAAI;MAE9BhI,EAAE,GAAGF,KAAK,CAACC,qBAAqB,CAACC,EAAE,CAAC5C,IAAI,CAAC;IAC3C;IAEA,MAAM8K,kBAAkB,GAAGP,qBAAqB,GAC5CF,WAAW,CAAC3K,IAAI,GAChBR,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAChBF,SAAS,CAAC2D,EAAE,CAAC,EAEbyH,WAAW,CAAC3K,IACd,CAAC,CACF,CAAC;IAEN,MAAMqL,wBAAwB,GAAG3L,sBAAsB,CAAC,IAAI,EAAE,CAC5DC,eAAe,CAACJ,SAAS,CAAC2D,EAAE,CAAC,EAAEnF,UAAU,CAAC,SAAS,CAAC,CAAC,CACtD,CAAC;IAEF,IAAI,CAACuN,WAAW,CAACD,wBAAwB,CAAC;IAC1C,IAAI,CAAC9H,WAAW,CAAC6H,kBAAkB,CAAC;IAEpC,IAAIF,uBAAuB,EAAE;MAC3BlI,KAAK,CAACuI,mBAAmB,CAAC,IAAI,CAAC;IACjC;IAEA,OAAO,IAAI;EACb,CAAC,MAAM,IAAI,IAAI,CAAC9K,GAAG,CAAC,YAAY,CAAC,CAACuE,MAAM,GAAG,CAAC,EAAE;IAC5C,MAAM,IAAInE,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAM2K,kBAAkB,GAAGb,WAAW,CAACc,0BAA0B,CAAC,CAAC;EAEnE,MAAMC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,kBAAkB,CAAC,CAACK,GAAG,CAACvL,IAAI,IAAI;IAC7D,OAAOX,eAAe,CAAC5B,UAAU,CAACuC,IAAI,CAAC,EAAEvC,UAAU,CAACuC,IAAI,CAAC,CAAC;EAC5D,CAAC,CAAC;EAEF,MAAMwL,WAAW,GAAGpM,sBAAsB,CAAC,IAAI,EAAEgM,UAAU,CAAC;EAE5D,IAAI,CAACJ,WAAW,CAACQ,WAAW,CAAC;EAC7B,IAAI,CAACvI,WAAW,CAACoH,WAAW,CAAC3K,IAAI,CAAC;EAClC,OAAO,IAAI;AACb;AAEA,MAAM+L,yBAGJ,GAAG;EACH,wCAAwCC,CACtC/J,IAA4B,EAC5BgK,KAAK,EACL;IAEA,IAAIhK,IAAI,CAACjC,IAAI,CAACM,IAAI,KAAK2L,KAAK,CAAC3L,IAAI,EAAE;IACnC2L,KAAK,CAACC,WAAW,GAAG,IAAI;IACxBjK,IAAI,CAACkK,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,KAAKA,CAACnK,IAAI,EAAEgK,KAAK,EAAE;IACjB,IAAIhK,IAAI,CAACe,KAAK,CAACmH,aAAa,CAAC8B,KAAK,CAAC3L,IAAI,CAAC,EAAE;MACxC2B,IAAI,CAACoK,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEM,SAAS1J,kBAAkBA,CAEb2J,gBAAyB,EAAsB;EAClE,IAAI,IAAI,CAACtM,IAAI,CAACkD,EAAE,EAAE,OAAO,IAAI;EAE7B,MAAMqJ,GAAG,GAAGjN,eAAe,CAAC,IAAI,CAACU,IAAI,EAAE,IAAI,CAACqK,MAAM,CAAC;EACnD,IAAIkC,GAAG,IAAI,IAAI,EAAE,OAAO,IAAI;EAC5B,IAAI;IAAEjM;EAAK,CAAC,GAAGiM,GAAG;EAElB,IAAI,CAACD,gBAAgB,IAAI,iBAAiB,CAACE,IAAI,CAAClM,IAAI,CAAC,EAAE;IACrD,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,CAACmM,UAAU,CAAC,MAAM,CAAC,IAAInM,IAAI,CAACmM,UAAU,CAAC,MAAM,CAAC,EAAE;IAEtD,OAAO,IAAI;EACb;EAEAnM,IAAI,GAAGpB,uBAAuB,CAACoB,IAAI,CAACoM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;EAC1D,MAAMxJ,EAAE,GAAGnF,UAAU,CAACuC,IAAI,CAAC;EAC3BV,QAAQ,CAACsD,EAAE,EAAEqJ,GAAG,CAACI,YAAY,CAAC;EAE9B,MAAMV,KAAK,GAAG;IAAEC,WAAW,EAAE,KAAK;IAAE5L;EAAK,CAAC;EAK1C,MAAM;IAAE0C;EAAM,CAAC,GAAG,IAAI;EACtB,MAAM4J,OAAO,GAAG5J,KAAK,CAAC6J,aAAa,CAACvM,IAAI,CAAC;EACzC,IAAIsM,OAAO,EAAE;IACX,IAAIA,OAAO,CAACnI,IAAI,KAAK,OAAO,EAAE;MAa5BwH,KAAK,CAACC,WAAW,GAAG,IAAI;IAC1B,CAAC,MAAM,CASP;EACF,CAAC,MAAM,IAAIlJ,KAAK,CAACqH,MAAM,CAACc,UAAU,CAAC7K,IAAI,CAAC,IAAI0C,KAAK,CAAC8J,SAAS,CAACxM,IAAI,CAAC,EAAE;IACjE,IAAI,CAAC2E,QAAQ,CAAC8G,yBAAyB,EAAEE,KAAK,CAAC;EACjD;EAEA,IAAI,CAACA,KAAK,CAACC,WAAW,EAAE;IACtB,IAAI,CAAClM,IAAI,CAACkD,EAAE,GAAGA,EAAE;IACjBF,KAAK,CAAC+J,gBAAgB,CAAC,CAAC,CAACC,UAAU,CAAC9J,EAAE,CAAC5C,IAAI,CAAC,GAAG,IAAI;IACnD,OAAO,IAAI;EACb;EAEA,IAAI0C,KAAK,CAACmI,UAAU,CAACjI,EAAE,CAAC5C,IAAI,CAAC,IAAI,CAAC0C,KAAK,CAAC8J,SAAS,CAAC5J,EAAE,CAAC5C,IAAI,CAAC,EAAE;IAE1D0C,KAAK,CAACoH,MAAM,CAAClH,EAAE,CAAC5C,IAAI,CAAC;IACrB,IAAI,CAACN,IAAI,CAACkD,EAAE,GAAGA,EAAE;IACjBF,KAAK,CAAC+J,gBAAgB,CAAC,CAAC,CAACC,UAAU,CAAC9J,EAAE,CAAC5C,IAAI,CAAC,GAAG,IAAI;IACnD,OAAO,IAAI;EACb;EAGA,IAAI,CAACnB,UAAU,CAAC,IAAI,CAACa,IAAI,CAAC,EAAE,OAAO,IAAI;EAIvC,MAAMF,GAAG,GAAGkD,KAAK,CAACC,qBAAqB,CAACC,EAAE,CAAC5C,IAAI,CAAC;EAGhD,MAAM2M,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGC,gBAAgB,CAAC,IAAI,CAACpN,IAAI,CAAC,EAAEkN,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAC/DD,MAAM,CAAC9L,IAAI,CAAC6B,KAAK,CAACC,qBAAqB,CAAC,GAAG,CAAC,CAAC;EAC/C;EACA,MAAM3B,IAAI,GAAG+L,iBAAQ,CAACC,UAAU,CAACC,GAAG;AACtC,iBAAiBzN,GAAG;AACpB,iBAAiBoD,EAAE,IAAI+J,MAAM;AAC7B,iBAAiB1N,SAAS,CAACO,GAAG,CAAC;AAC/B;AACA;AACA,QAAQP,SAAS,CAAC2D,EAAE,CAAC;AACrB,iBAAiB3D,SAAS,CAACO,GAAG,CAAC;AAC/B;AACA;AACA,eAAeP,SAAS,CAAC2D,EAAE,CAAC;AAC5B,SAASlE,YAAY,CAAC,IAAI,CAACgB,IAAI,CAAC;AAChC,GAAuB;EAErB,OAAO,IAAI,CAACuD,WAAW,CAACjC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACb,GAAG,CAAC,aAAa,CAAC;AACrD;AAEA,SAAS2M,gBAAgBA,CAACpN,IAAgB,EAAU;EAClD,MAAMwN,KAAK,GAAGxN,IAAI,CAACiN,MAAM,CAACQ,SAAS,CACjCC,KAAK,IAAItO,mBAAmB,CAACsO,KAAK,CAAC,IAAIrO,aAAa,CAACqO,KAAK,CAC5D,CAAC;EACD,OAAOF,KAAK,KAAK,CAAC,CAAC,GAAGxN,IAAI,CAACiN,MAAM,CAACjI,MAAM,GAAGwI,KAAK;AAClD", "ignoreList": []}